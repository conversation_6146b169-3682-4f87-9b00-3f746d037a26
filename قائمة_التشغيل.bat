@echo off
chcp 65001 >nul
title نظام إدارة الاشتراكات - قائمة التشغيل
color 0E

:MENU
cls
echo.
echo ===============================================================================
echo                    🚀 نظام إدارة الاشتراكات المتطور 🚀
echo ===============================================================================
echo.
echo 👨‍💻 المطور: المهندس محمد ياسر الجبوري
echo 📅 التاريخ: 2025-07-21
echo 🎯 النسخة: النهائية المتطورة
echo.
echo ===============================================================================
echo                              📋 قائمة التشغيل
echo ===============================================================================
echo.
echo [1] 🚀 تشغيل النظام (عادي)
echo [2] 🌐 تشغيل النظام + فتح المتصفح
echo [3] 📊 تشغيل النظام + فتح جميع الصفحات
echo [4] ⚡ تشغيل سريع
echo [5] 🔍 فحص النظام
echo [6] 📋 عرض معلومات النظام
echo [7] 🌐 فتح النظام في المتصفح (إذا كان يعمل)
echo [8] ❌ خروج
echo.
echo ===============================================================================
echo.

set /p choice=اختر رقم الخيار (1-8): 

if "%choice%"=="1" goto START_NORMAL
if "%choice%"=="2" goto START_BROWSER
if "%choice%"=="3" goto START_ALL
if "%choice%"=="4" goto START_QUICK
if "%choice%"=="5" goto CHECK_SYSTEM
if "%choice%"=="6" goto SHOW_INFO
if "%choice%"=="7" goto OPEN_BROWSER
if "%choice%"=="8" goto EXIT

echo ❌ خيار غير صحيح! يرجى اختيار رقم من 1 إلى 8
pause
goto MENU

:START_NORMAL
cls
echo 🚀 تشغيل النظام العادي...
cd /d "D:\vps cloud mohammed"
python app.py
pause
goto MENU

:START_BROWSER
cls
echo 🚀 تشغيل النظام مع فتح المتصفح...
cd /d "D:\vps cloud mohammed"
start /b python app.py
timeout /t 8 /nobreak >nul
start http://localhost:9090
echo ✅ تم تشغيل النظام وفتح المتصفح
echo 🌐 الرابط: http://localhost:9090
echo 👤 المستخدم: admin | 🔑 كلمة المرور: 123456
pause
goto MENU

:START_ALL
cls
echo 🚀 تشغيل النظام مع فتح جميع الصفحات...
cd /d "D:\vps cloud mohammed"
start /b python app.py
echo ⏳ انتظار تشغيل النظام...
timeout /t 10 /nobreak >nul

echo 🌐 فتح الصفحات...
start http://localhost:9090
timeout /t 2 /nobreak >nul
start http://localhost:9090/dashboard
timeout /t 1 /nobreak >nul
start http://localhost:9090/advanced-dashboard
timeout /t 1 /nobreak >nul
start http://localhost:9090/subscriptions
timeout /t 1 /nobreak >nul
start http://localhost:9090/ui-controller
timeout /t 1 /nobreak >nul
start http://localhost:9090/advanced-users

echo ✅ تم فتح جميع الصفحات الرئيسية
pause
goto MENU

:START_QUICK
cls
echo ⚡ تشغيل سريع...
cd /d "D:\vps cloud mohammed"
start /b python app.py
timeout /t 5 /nobreak >nul
start http://localhost:9090
echo ✅ تشغيل سريع مكتمل
pause
goto MENU

:CHECK_SYSTEM
cls
echo 🔍 فحص النظام...
echo.
echo 📍 التحقق من Python:
python --version
echo.
echo 📁 التحقق من مجلد النظام:
if exist "D:\vps cloud mohammed\app.py" (
    echo ✅ ملف النظام موجود
) else (
    echo ❌ ملف النظام غير موجود
)
echo.
echo 🌐 التحقق من حالة النظام:
curl -s http://localhost:9090 >nul 2>&1
if errorlevel 1 (
    echo ❌ النظام غير يعمل
) else (
    echo ✅ النظام يعمل
)
echo.
pause
goto MENU

:SHOW_INFO
cls
echo 📋 معلومات النظام:
echo.
echo ===============================================================================
echo                           📋 معلومات النظام الكاملة
echo ===============================================================================
echo.
echo 🌐 الرابط الرئيسي: http://localhost:9090
echo 👤 اسم المستخدم: admin
echo 🔑 كلمة المرور: 123456
echo 📍 مسار النظام: D:\vps cloud mohammed
echo.
echo 🌐 الصفحات المتاحة:
echo    • الصفحة الرئيسية: http://localhost:9090
echo    • لوحة التحكم: http://localhost:9090/dashboard
echo    • لوحة التحكم المتقدمة: http://localhost:9090/advanced-dashboard
echo    • إدارة الاشتراكات: http://localhost:9090/subscriptions
echo    • إدارة المستخدمين: http://localhost:9090/advanced-users
echo    • إدارة الموزعين: http://localhost:9090/distributors-management
echo    • أنظمة الدفع: http://localhost:9090/payment-gateways
echo    • التقارير المتقدمة: http://localhost:9090/advanced-reports
echo    • مراقبة النظام: http://localhost:9090/system-monitor
echo    • إعدادات الألوان: http://localhost:9090/theme-settings
echo    • إعدادات التنقل: http://localhost:9090/navigation-settings
echo    • تحكم الواجهات: http://localhost:9090/ui-controller
echo    • مركز الحماية: http://localhost:9090/security
echo    • إدارة الفواتير: http://localhost:9090/invoices
echo.
echo 🎮 التحكمات العائمة:
echo    🎛️ شريط التحكم العائم (يسار الشاشة)
echo    🌈 مبدل الألوان (أعلى يسار الشاشة)
echo    🎛️ شريط التحكم السفلي (صفحة الاشتراكات)
echo.
pause
goto MENU

:OPEN_BROWSER
cls
echo 🌐 فتح النظام في المتصفح...
start http://localhost:9090
echo ✅ تم فتح المتصفح
echo 🌐 الرابط: http://localhost:9090
echo 👤 المستخدم: admin | 🔑 كلمة المرور: 123456
pause
goto MENU

:EXIT
cls
echo.
echo ===============================================================================
echo                              👋 شكراً لاستخدام النظام
echo ===============================================================================
echo.
echo 👨‍💻 المطور: المهندس محمد ياسر الجبوري
echo 📞 للدعم الفني: يرجى التواصل مع المطور
echo.
echo ✅ تم إغلاق قائمة التشغيل بنجاح
echo.
pause
exit
