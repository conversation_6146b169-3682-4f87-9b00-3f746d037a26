/**
 * نظام التنقل المتقدم والأنيميشن
 * تطوير: المهندس محمد ياسر الجبوري
 * نظام إدارة الاشتراكات المتطور
 */

class AdvancedNavigation {
    constructor() {
        this.init();
        this.setupPageTransitions();
        this.setupSmoothScrolling();
        this.setupLoadingAnimations();
        this.setupInteractiveElements();
    }

    init() {
        console.log('🚀 تم تحميل نظام التنقل المتقدم');
        this.addPageLoadingEffect();
    }

    // تأثير تحميل الصفحة
    addPageLoadingEffect() {
        document.body.classList.add('page-transition');
        
        window.addEventListener('load', () => {
            setTimeout(() => {
                document.body.classList.add('loaded');
                this.animatePageElements();
            }, 100);
        });
    }

    // تأثيرات انتقال الصفحات
    setupPageTransitions() {
        // اعتراض النقرات على الروابط
        document.addEventListener('click', (e) => {
            const link = e.target.closest('a[href]');
            if (link && !link.hasAttribute('target') && link.href.startsWith(window.location.origin)) {
                e.preventDefault();
                this.navigateWithTransition(link.href);
            }
        });
    }

    // التنقل مع التأثيرات
    navigateWithTransition(url) {
        // تأثير الخروج
        document.body.style.opacity = '0';
        document.body.style.transform = 'translateY(-20px)';
        
        setTimeout(() => {
            window.location.href = url;
        }, 300);
    }

    // التمرير السلس
    setupSmoothScrolling() {
        // التمرير السلس للروابط الداخلية
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(anchor.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    // تأثيرات التحميل للعناصر
    setupLoadingAnimations() {
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    this.animateElement(entry.target);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });

        // مراقبة العناصر القابلة للتحريك
        document.querySelectorAll('.card, .nav-item, .btn-primary, .glass-card').forEach(el => {
            observer.observe(el);
        });
    }

    // تحريك العناصر عند ظهورها
    animateElement(element) {
        const animations = [
            'animate-fadeInUp',
            'animate-fadeInLeft', 
            'animate-fadeInRight',
            'animate-scaleIn',
            'animate-bounceIn'
        ];
        
        const randomAnimation = animations[Math.floor(Math.random() * animations.length)];
        element.classList.add(randomAnimation);
    }

    // تحريك عناصر الصفحة
    animatePageElements() {
        // تحريك الكروت
        const cards = document.querySelectorAll('.card, .glass-card');
        cards.forEach((card, index) => {
            setTimeout(() => {
                card.classList.add('animate-fadeInUp');
            }, index * 100);
        });

        // تحريك عناصر التنقل
        const navItems = document.querySelectorAll('.nav-item');
        navItems.forEach((item, index) => {
            setTimeout(() => {
                item.classList.add('animate-fadeInLeft');
            }, index * 50);
        });

        // تحريك الأزرار
        const buttons = document.querySelectorAll('.btn-primary, .btn-secondary');
        buttons.forEach((btn, index) => {
            setTimeout(() => {
                btn.classList.add('animate-scaleIn');
            }, 500 + (index * 100));
        });
    }

    // العناصر التفاعلية
    setupInteractiveElements() {
        // تأثير الماوس للكروت
        document.querySelectorAll('.card, .glass-card').forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-5px) scale(1.02)';
                card.style.boxShadow = '0 20px 40px rgba(0, 0, 0, 0.15)';
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0) scale(1)';
                card.style.boxShadow = '';
            });
        });

        // تأثير النقر للأزرار
        document.querySelectorAll('.btn-primary, .btn-secondary, .btn-danger').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.createRippleEffect(e, btn);
            });
        });

        // تأثير التركيز للحقول
        document.querySelectorAll('.input-field').forEach(input => {
            input.addEventListener('focus', () => {
                input.parentElement.classList.add('focused');
            });

            input.addEventListener('blur', () => {
                input.parentElement.classList.remove('focused');
            });
        });
    }

    // تأثير الموجة عند النقر
    createRippleEffect(event, element) {
        const ripple = document.createElement('span');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;

        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple');

        element.appendChild(ripple);

        setTimeout(() => {
            ripple.remove();
        }, 600);
    }

    // تأثير التمرير المتوازي
    setupParallaxEffect() {
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const parallaxElements = document.querySelectorAll('.parallax');
            
            parallaxElements.forEach(element => {
                const speed = element.dataset.speed || 0.5;
                const yPos = -(scrolled * speed);
                element.style.transform = `translateY(${yPos}px)`;
            });
        });
    }

    // تأثير الكتابة
    typewriterEffect(element, text, speed = 100) {
        element.innerHTML = '';
        let i = 0;
        
        const timer = setInterval(() => {
            if (i < text.length) {
                element.innerHTML += text.charAt(i);
                i++;
            } else {
                clearInterval(timer);
            }
        }, speed);
    }

    // تأثير العد التصاعدي
    countUpAnimation(element, target, duration = 2000) {
        const start = 0;
        const increment = target / (duration / 16);
        let current = start;

        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                element.textContent = target;
                clearInterval(timer);
            } else {
                element.textContent = Math.floor(current);
            }
        }, 16);
    }

    // تأثير الاهتزاز للتنبيهات
    shakeElement(element) {
        element.classList.add('shake');
        setTimeout(() => {
            element.classList.remove('shake');
        }, 500);
    }

    // تأثير النبض
    pulseElement(element, duration = 1000) {
        element.classList.add('pulse');
        setTimeout(() => {
            element.classList.remove('pulse');
        }, duration);
    }

    // تأثير التدوير
    rotateElement(element, degrees = 360) {
        element.style.transform = `rotate(${degrees}deg)`;
        setTimeout(() => {
            element.style.transform = 'rotate(0deg)';
        }, 500);
    }
}

// تأثيرات CSS إضافية
const additionalStyles = `
    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }

    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }

    .focused {
        transform: scale(1.02);
        transition: transform 0.3s ease;
    }
`;

// إضافة الأنماط للصفحة
const styleSheet = document.createElement('style');
styleSheet.textContent = additionalStyles;
document.head.appendChild(styleSheet);

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.advancedNav = new AdvancedNavigation();
});

// تصدير الكلاس للاستخدام العام
window.AdvancedNavigation = AdvancedNavigation;
