<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نظام إدارة الاشتراكات</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { 
            font-family: 'Cairo', sans-serif; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        .input-field {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(102, 126, 234, 0.2);
            border-radius: 12px;
            padding: 14px 50px 14px 16px;
            transition: all 0.3s ease;
            font-family: 'Cairo', sans-serif;
            width: 100%;
        }
        .input-field:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: white;
        }
        .btn-primary { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; 
            padding: 14px 24px; 
            border-radius: 12px; 
            transition: all 0.3s ease;
            border: none;
            font-weight: 600;
            width: 100%;
            position: relative;
            overflow: hidden;
        }
        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        .btn-primary:hover::before {
            left: 100%;
        }
        .btn-primary:hover { 
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }
        .floating-shapes {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 0;
        }
        .shape {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        .shape:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }
        .shape:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }
        .shape:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }
        .shape:nth-child(4) {
            width: 40px;
            height: 40px;
            top: 10%;
            right: 30%;
            animation-delay: 1s;
        }
        .shape:nth-child(5) {
            width: 100px;
            height: 100px;
            bottom: 10%;
            right: 20%;
            animation-delay: 3s;
        }
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        .input-group {
            position: relative;
        }
        .input-icon {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: #667eea;
            z-index: 10;
        }
        .logo-container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .remember-checkbox {
            accent-color: #667eea;
        }
        .link-hover {
            transition: all 0.3s ease;
        }
        .link-hover:hover {
            color: #667eea;
            text-decoration: underline;
        }
    </style>
</head>
<body class="flex items-center justify-center min-h-screen p-4">
    <!-- Floating Shapes Background -->
    <div class="floating-shapes">
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
        <div class="shape"></div>
    </div>

    <!-- Login Container -->
    <div class="login-container w-full max-w-md p-8 relative z-10">
        <!-- Logo and Title -->
        <div class="text-center mb-8">
            <div class="mb-4">
                <i class="fas fa-cloud-upload-alt text-5xl logo-container"></i>
            </div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2">نظام إدارة الاشتراكات</h1>
            <p class="text-gray-600">مرحباً بك! يرجى تسجيل الدخول للمتابعة</p>
        </div>

        <!-- Login Form -->
        <form id="loginForm" class="space-y-6">
            <!-- Username Field -->
            <div class="input-group">
                <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-user ml-2"></i>
                    اسم المستخدم
                </label>
                <input 
                    type="text" 
                    id="username" 
                    name="username" 
                    class="input-field"
                    placeholder="أدخل اسم المستخدم"
                    required
                >
                <i class="fas fa-user input-icon"></i>
            </div>

            <!-- Password Field -->
            <div class="input-group">
                <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                    <i class="fas fa-lock ml-2"></i>
                    كلمة المرور
                </label>
                <input 
                    type="password" 
                    id="password" 
                    name="password" 
                    class="input-field"
                    placeholder="أدخل كلمة المرور"
                    required
                >
                <i class="fas fa-lock input-icon"></i>
                <button type="button" id="togglePassword" class="absolute left-4 top-12 text-gray-500 hover:text-gray-700">
                    <i class="fas fa-eye"></i>
                </button>
            </div>

            <!-- Remember Me & Forgot Password -->
            <div class="flex items-center justify-between">
                <label class="flex items-center">
                    <input type="checkbox" class="remember-checkbox ml-2" id="remember">
                    <span class="text-sm text-gray-600">تذكرني</span>
                </label>
                <a href="#" class="text-sm text-gray-600 link-hover">نسيت كلمة المرور؟</a>
            </div>

            <!-- Login Button -->
            <button type="submit" class="btn-primary">
                <i class="fas fa-sign-in-alt ml-2"></i>
                تسجيل الدخول
            </button>
        </form>

        <!-- Demo Credentials -->
        <div class="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
            <h4 class="text-sm font-medium text-blue-800 mb-2">
                <i class="fas fa-info-circle ml-2"></i>
                بيانات تجريبية:
            </h4>
            <p class="text-xs text-blue-600">اسم المستخدم: admin</p>
            <p class="text-xs text-blue-600">كلمة المرور: 123456</p>
        </div>

        <!-- Footer -->
        <div class="text-center mt-8 text-sm text-gray-500">
            <p>&copy; 2024 نظام إدارة الاشتراكات</p>
            <p>جميع الحقوق محفوظة</p>
        </div>
    </div>

    <script>
        // Toggle Password Visibility
        document.getElementById('togglePassword').addEventListener('click', function() {
            const passwordField = document.getElementById('password');
            const icon = this.querySelector('i');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });

        // Login Form Handler
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const remember = document.getElementById('remember').checked;
            
            // Simple validation (in real app, this would be server-side)
            if (username === 'admin' && password === '123456') {
                // Store login state
                if (remember) {
                    localStorage.setItem('isLoggedIn', 'true');
                    localStorage.setItem('username', username);
                } else {
                    sessionStorage.setItem('isLoggedIn', 'true');
                    sessionStorage.setItem('username', username);
                }
                
                // Show success message
                showMessage('تم تسجيل الدخول بنجاح!', 'success');
                
                // Redirect to dashboard
                setTimeout(() => {
                    window.location.href = 'dashboard.html';
                }, 1500);
            } else {
                showMessage('اسم المستخدم أو كلمة المرور غير صحيحة', 'error');
            }
        });

        // Show Message Function
        function showMessage(message, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `fixed top-4 right-4 p-4 rounded-lg z-50 ${
                type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
            }`;
            messageDiv.innerHTML = `
                <i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'} ml-2"></i>
                ${message}
            `;
            
            document.body.appendChild(messageDiv);
            
            setTimeout(() => {
                messageDiv.remove();
            }, 3000);
        }

        // Check if already logged in
        if (localStorage.getItem('isLoggedIn') || sessionStorage.getItem('isLoggedIn')) {
            window.location.href = 'dashboard.html';
        }
    </script>
</body>
</html>
