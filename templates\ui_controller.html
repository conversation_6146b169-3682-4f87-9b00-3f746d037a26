{% extends "base.html" %}

{% block title %}تحكم الواجهات - نظام إدارة الاشتراكات{% endblock %}
{% block page_title %}تحكم الواجهات المتقدم{% endblock %}
{% block page_description %}تحكم شامل في جميع عناصر الواجهة والتصميم{% endblock %}

{% block extra_css %}
<style>
/* شريط التحكم الجانبي */
.ui-controller {
    position: fixed;
    top: 50%;
    right: -320px;
    transform: translateY(-50%);
    width: 320px;
    height: 80vh;
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 15px 0 0 15px;
    backdrop-filter: blur(20px);
    z-index: 9999;
    transition: all var(--transition-medium);
    overflow-y: auto;
    box-shadow: var(--shadow-large);
}

.ui-controller.active {
    right: 0;
}

.ui-controller-toggle {
    position: fixed;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 1.25rem;
    cursor: pointer;
    z-index: 10000;
    transition: all var(--transition-medium);
    box-shadow: var(--shadow-medium);
}

.ui-controller-toggle:hover {
    transform: translateY(-50%) scale(1.1);
    box-shadow: var(--shadow-large);
}

.ui-controller-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--glass-border);
    text-align: center;
}

.ui-controller-title {
    color: var(--text-primary);
    font-size: 1.125rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.ui-controller-content {
    padding: 1rem;
}

.control-section {
    margin-bottom: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.control-section-title {
    color: var(--text-primary);
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.control-group {
    margin-bottom: 1rem;
}

.control-label {
    color: var(--text-secondary);
    font-size: 0.75rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
    display: block;
}

.control-input {
    width: 100%;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--glass-border);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: all var(--transition-medium);
}

.control-input:focus {
    outline: none;
    border-color: var(--primary-color);
    background: rgba(255, 255, 255, 0.15);
}

.control-slider {
    width: 100%;
    height: 6px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    outline: none;
    -webkit-appearance: none;
}

.control-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    width: 18px;
    height: 18px;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
}

.control-slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
    border: none;
}

.control-buttons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.control-btn {
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--glass-border);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 0.75rem;
    cursor: pointer;
    transition: all var(--transition-medium);
    text-align: center;
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.control-btn.active {
    background: var(--primary-color);
    color: white;
}

.position-controls {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.position-btn {
    aspect-ratio: 1;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--glass-border);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 0.75rem;
    cursor: pointer;
    transition: all var(--transition-medium);
    display: flex;
    align-items: center;
    justify-content: center;
}

.position-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.position-btn.active {
    background: var(--primary-color);
    color: white;
}

.size-controls {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.size-btn {
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid var(--glass-border);
    border-radius: 6px;
    color: var(--text-primary);
    font-size: 0.625rem;
    cursor: pointer;
    transition: all var(--transition-medium);
    text-align: center;
}

.size-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

.size-btn.active {
    background: var(--primary-color);
    color: white;
}

.color-picker {
    width: 100%;
    height: 40px;
    border: 1px solid var(--glass-border);
    border-radius: 6px;
    cursor: pointer;
    background: transparent;
}

.preset-colors {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.preset-color {
    aspect-ratio: 1;
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all var(--transition-medium);
}

.preset-color:hover {
    transform: scale(1.1);
    border-color: white;
}

.preset-color.active {
    border-color: var(--primary-color);
    transform: scale(1.2);
}

.control-actions {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 0.5rem;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--glass-border);
}

.action-btn {
    padding: 0.75rem;
    border: 1px solid var(--glass-border);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    font-size: 0.75rem;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-medium);
    text-align: center;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.action-btn.primary {
    background: var(--primary-color);
    color: white;
}

.action-btn.success {
    background: var(--success-color);
    color: white;
}

.action-btn.danger {
    background: var(--error-color);
    color: white;
}

/* تأثيرات الواجهة */
.ui-effect-glow {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.5) !important;
}

.ui-effect-blur {
    filter: blur(2px) !important;
}

.ui-effect-grayscale {
    filter: grayscale(100%) !important;
}

.ui-effect-sepia {
    filter: sepia(100%) !important;
}

.ui-effect-invert {
    filter: invert(100%) !important;
}

.ui-effect-rotate {
    transform: rotate(5deg) !important;
}

.ui-effect-scale {
    transform: scale(1.05) !important;
}

.ui-effect-shadow {
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3) !important;
}

/* أحجام مخصصة */
.size-xs { font-size: 0.75rem !important; }
.size-sm { font-size: 0.875rem !important; }
.size-md { font-size: 1rem !important; }
.size-lg { font-size: 1.125rem !important; }
.size-xl { font-size: 1.25rem !important; }

/* مواضع مخصصة */
.pos-top-left { position: fixed !important; top: 20px !important; left: 20px !important; }
.pos-top-center { position: fixed !important; top: 20px !important; left: 50% !important; transform: translateX(-50%) !important; }
.pos-top-right { position: fixed !important; top: 20px !important; right: 20px !important; }
.pos-center-left { position: fixed !important; top: 50% !important; left: 20px !important; transform: translateY(-50%) !important; }
.pos-center { position: fixed !important; top: 50% !important; left: 50% !important; transform: translate(-50%, -50%) !important; }
.pos-center-right { position: fixed !important; top: 50% !important; right: 20px !important; transform: translateY(-50%) !important; }
.pos-bottom-left { position: fixed !important; bottom: 20px !important; left: 20px !important; }
.pos-bottom-center { position: fixed !important; bottom: 20px !important; left: 50% !important; transform: translateX(-50%) !important; }
.pos-bottom-right { position: fixed !important; bottom: 20px !important; right: 20px !important; }

/* تجاوب الأجهزة */
@media (max-width: 768px) {
    .ui-controller {
        width: 280px;
        right: -280px;
        height: 70vh;
    }
    
    .ui-controller-toggle {
        right: 5px;
        width: 45px;
        height: 45px;
        font-size: 1rem;
    }
    
    .control-buttons {
        grid-template-columns: 1fr;
    }
    
    .position-controls {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .size-controls {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .ui-controller {
        width: 250px;
        right: -250px;
        height: 60vh;
    }
    
    .preset-colors {
        grid-template-columns: repeat(4, 1fr);
    }
}
</style>
{% endblock %}

{% block content %}
<!-- زر تفعيل التحكم -->
<button class="ui-controller-toggle" onclick="toggleUIController()">
    <i class="fas fa-cog"></i>
</button>

<!-- شريط التحكم الجانبي -->
<div class="ui-controller" id="uiController">
    <div class="ui-controller-header">
        <h3 class="ui-controller-title">
            <i class="fas fa-palette"></i>
            تحكم الواجهات
        </h3>
    </div>
    
    <div class="ui-controller-content">
        <!-- تحكم الموضع -->
        <div class="control-section">
            <div class="control-section-title">
                <i class="fas fa-arrows-alt"></i>
                موضع العناصر
            </div>
            
            <div class="control-group">
                <label class="control-label">اختر الموضع:</label>
                <div class="position-controls">
                    <button class="position-btn" data-position="top-left">
                        <i class="fas fa-arrow-up"></i>
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <button class="position-btn" data-position="top-center">
                        <i class="fas fa-arrow-up"></i>
                    </button>
                    <button class="position-btn" data-position="top-right">
                        <i class="fas fa-arrow-up"></i>
                        <i class="fas fa-arrow-right"></i>
                    </button>
                    
                    <button class="position-btn" data-position="center-left">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <button class="position-btn active" data-position="center">
                        <i class="fas fa-dot-circle"></i>
                    </button>
                    <button class="position-btn" data-position="center-right">
                        <i class="fas fa-arrow-right"></i>
                    </button>
                    
                    <button class="position-btn" data-position="bottom-left">
                        <i class="fas fa-arrow-down"></i>
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <button class="position-btn" data-position="bottom-center">
                        <i class="fas fa-arrow-down"></i>
                    </button>
                    <button class="position-btn" data-position="bottom-right">
                        <i class="fas fa-arrow-down"></i>
                        <i class="fas fa-arrow-right"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- تحكم الأحجام -->
        <div class="control-section">
            <div class="control-section-title">
                <i class="fas fa-expand-arrows-alt"></i>
                أحجام العناصر
            </div>
            
            <div class="control-group">
                <label class="control-label">حجم النص:</label>
                <div class="size-controls">
                    <button class="size-btn" data-size="xs">XS</button>
                    <button class="size-btn" data-size="sm">SM</button>
                    <button class="size-btn active" data-size="md">MD</button>
                    <button class="size-btn" data-size="lg">LG</button>
                    <button class="size-btn" data-size="xl">XL</button>
                </div>
            </div>
            
            <div class="control-group">
                <label class="control-label">عرض العنصر:</label>
                <input type="range" class="control-slider" min="50" max="100" value="100" id="widthSlider">
                <span class="control-label" id="widthValue">100%</span>
            </div>
            
            <div class="control-group">
                <label class="control-label">ارتفاع العنصر:</label>
                <input type="range" class="control-slider" min="50" max="100" value="100" id="heightSlider">
                <span class="control-label" id="heightValue">100%</span>
            </div>
        </div>

        <!-- تحكم الألوان -->
        <div class="control-section">
            <div class="control-section-title">
                <i class="fas fa-palette"></i>
                ألوان الواجهة
            </div>
            
            <div class="control-group">
                <label class="control-label">لون أساسي:</label>
                <input type="color" class="color-picker" id="primaryColor" value="#3b82f6">
                
                <div class="preset-colors">
                    <div class="preset-color" style="background: #3b82f6;" data-color="#3b82f6"></div>
                    <div class="preset-color" style="background: #10b981;" data-color="#10b981"></div>
                    <div class="preset-color" style="background: #f59e0b;" data-color="#f59e0b"></div>
                    <div class="preset-color" style="background: #ef4444;" data-color="#ef4444"></div>
                    <div class="preset-color" style="background: #8b5cf6;" data-color="#8b5cf6"></div>
                    <div class="preset-color" style="background: #ec4899;" data-color="#ec4899"></div>
                </div>
            </div>
            
            <div class="control-group">
                <label class="control-label">شفافية الخلفية:</label>
                <input type="range" class="control-slider" min="0" max="100" value="80" id="opacitySlider">
                <span class="control-label" id="opacityValue">80%</span>
            </div>
        </div>

        <!-- تأثيرات بصرية -->
        <div class="control-section">
            <div class="control-section-title">
                <i class="fas fa-magic"></i>
                التأثيرات البصرية
            </div>
            
            <div class="control-buttons">
                <button class="control-btn" data-effect="glow">توهج</button>
                <button class="control-btn" data-effect="blur">ضبابية</button>
                <button class="control-btn" data-effect="shadow">ظل</button>
                <button class="control-btn" data-effect="rotate">دوران</button>
                <button class="control-btn" data-effect="scale">تكبير</button>
                <button class="control-btn" data-effect="grayscale">رمادي</button>
                <button class="control-btn" data-effect="sepia">بني</button>
                <button class="control-btn" data-effect="invert">عكس</button>
            </div>
        </div>

        <!-- تحكم الجهاز -->
        <div class="control-section">
            <div class="control-section-title">
                <i class="fas fa-mobile-alt"></i>
                تجاوب الأجهزة
            </div>
            
            <div class="control-buttons">
                <button class="control-btn active" data-device="desktop">
                    <i class="fas fa-desktop"></i> كمبيوتر
                </button>
                <button class="control-btn" data-device="tablet">
                    <i class="fas fa-tablet-alt"></i> تابلت
                </button>
                <button class="control-btn" data-device="mobile">
                    <i class="fas fa-mobile-alt"></i> جوال
                </button>
            </div>
        </div>

        <!-- إجراءات سريعة -->
        <div class="control-actions">
            <button class="action-btn" onclick="resetUI()">
                <i class="fas fa-undo"></i> إعادة تعيين
            </button>
            <button class="action-btn primary" onclick="saveUISettings()">
                <i class="fas fa-save"></i> حفظ
            </button>
            <button class="action-btn success" onclick="exportUISettings()">
                <i class="fas fa-download"></i> تصدير
            </button>
            <button class="action-btn danger" onclick="importUISettings()">
                <i class="fas fa-upload"></i> استيراد
            </button>
        </div>
    </div>
</div>

<!-- محتوى الصفحة للاختبار -->
<div class="container mx-auto p-6">
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div class="ui-element card bg-white rounded-lg shadow-lg p-6" id="testElement1">
            <h3 class="text-lg font-bold mb-4">عنصر اختبار 1</h3>
            <p class="text-gray-600">هذا نص تجريبي لاختبار التحكم في الواجهة</p>
            <button class="btn btn-primary mt-4">زر تجريبي</button>
        </div>
        
        <div class="ui-element card bg-white rounded-lg shadow-lg p-6" id="testElement2">
            <h3 class="text-lg font-bold mb-4">عنصر اختبار 2</h3>
            <p class="text-gray-600">يمكنك تجربة جميع التحكمات على هذا العنصر</p>
            <button class="btn btn-secondary mt-4">زر آخر</button>
        </div>
        
        <div class="ui-element card bg-white rounded-lg shadow-lg p-6" id="testElement3">
            <h3 class="text-lg font-bold mb-4">عنصر اختبار 3</h3>
            <p class="text-gray-600">اختبر الألوان والتأثيرات المختلفة</p>
            <button class="btn btn-success mt-4">زر ثالث</button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// متغيرات التحكم
let selectedElement = null;
let uiSettings = {
    position: 'center',
    size: 'md',
    width: 100,
    height: 100,
    primaryColor: '#3b82f6',
    opacity: 80,
    effects: [],
    device: 'desktop'
};

// تفعيل/إلغاء تفعيل شريط التحكم
function toggleUIController() {
    const controller = document.getElementById('uiController');
    controller.classList.toggle('active');
}

// تهيئة التحكم
document.addEventListener('DOMContentLoaded', function() {
    initializeUIController();
    loadUISettings();
});

function initializeUIController() {
    // ربط أحداث العناصر القابلة للتحكم
    document.querySelectorAll('.ui-element').forEach(element => {
        element.addEventListener('click', function() {
            selectElement(this);
        });
    });
    
    // ربط أحداث أزرار الموضع
    document.querySelectorAll('.position-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            setPosition(this.dataset.position);
        });
    });
    
    // ربط أحداث أزرار الحجم
    document.querySelectorAll('.size-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            setSize(this.dataset.size);
        });
    });
    
    // ربط أحداث التأثيرات
    document.querySelectorAll('[data-effect]').forEach(btn => {
        btn.addEventListener('click', function() {
            toggleEffect(this.dataset.effect);
        });
    });
    
    // ربط أحداث الألوان المحددة مسبقاً
    document.querySelectorAll('.preset-color').forEach(color => {
        color.addEventListener('click', function() {
            setPrimaryColor(this.dataset.color);
        });
    });
    
    // ربط أحداث أزرار الجهاز
    document.querySelectorAll('[data-device]').forEach(btn => {
        btn.addEventListener('click', function() {
            setDevice(this.dataset.device);
        });
    });
    
    // ربط أحداث المنزلقات
    document.getElementById('widthSlider').addEventListener('input', function() {
        setWidth(this.value);
    });
    
    document.getElementById('heightSlider').addEventListener('input', function() {
        setHeight(this.value);
    });
    
    document.getElementById('opacitySlider').addEventListener('input', function() {
        setOpacity(this.value);
    });
    
    document.getElementById('primaryColor').addEventListener('change', function() {
        setPrimaryColor(this.value);
    });
}

// اختيار عنصر للتحكم
function selectElement(element) {
    // إزالة التحديد من العناصر الأخرى
    document.querySelectorAll('.ui-element').forEach(el => {
        el.classList.remove('selected');
    });
    
    // تحديد العنصر الحالي
    element.classList.add('selected');
    selectedElement = element;
    
    // إضافة حدود للعنصر المحدد
    element.style.border = '2px solid var(--primary-color)';
    
    showNotification('تم تحديد العنصر للتحكم', 'info');
}

// تعيين الموضع
function setPosition(position) {
    if (!selectedElement) {
        showNotification('يرجى تحديد عنصر أولاً', 'warning');
        return;
    }
    
    // إزالة جميع فئات الموضع
    selectedElement.className = selectedElement.className.replace(/pos-\w+-?\w*/g, '');
    
    // إضافة الموضع الجديد
    selectedElement.classList.add(`pos-${position}`);
    
    // تحديث الأزرار
    document.querySelectorAll('.position-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-position="${position}"]`).classList.add('active');
    
    uiSettings.position = position;
    showNotification(`تم تغيير الموضع إلى ${position}`, 'success');
}

// تعيين الحجم
function setSize(size) {
    if (!selectedElement) {
        showNotification('يرجى تحديد عنصر أولاً', 'warning');
        return;
    }
    
    // إزالة جميع فئات الحجم
    selectedElement.className = selectedElement.className.replace(/size-\w+/g, '');
    
    // إضافة الحجم الجديد
    selectedElement.classList.add(`size-${size}`);
    
    // تحديث الأزرار
    document.querySelectorAll('.size-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-size="${size}"]`).classList.add('active');
    
    uiSettings.size = size;
    showNotification(`تم تغيير الحجم إلى ${size}`, 'success');
}

// تعيين العرض
function setWidth(width) {
    if (!selectedElement) return;
    
    selectedElement.style.width = `${width}%`;
    document.getElementById('widthValue').textContent = `${width}%`;
    uiSettings.width = width;
}

// تعيين الارتفاع
function setHeight(height) {
    if (!selectedElement) return;
    
    selectedElement.style.height = `${height}%`;
    document.getElementById('heightValue').textContent = `${height}%`;
    uiSettings.height = height;
}

// تعيين اللون الأساسي
function setPrimaryColor(color) {
    document.documentElement.style.setProperty('--primary-color', color);
    document.getElementById('primaryColor').value = color;
    
    // تحديث الألوان المحددة مسبقاً
    document.querySelectorAll('.preset-color').forEach(colorEl => {
        colorEl.classList.remove('active');
    });
    document.querySelector(`[data-color="${color}"]`)?.classList.add('active');
    
    uiSettings.primaryColor = color;
    showNotification('تم تغيير اللون الأساسي', 'success');
}

// تعيين الشفافية
function setOpacity(opacity) {
    if (!selectedElement) return;
    
    selectedElement.style.opacity = opacity / 100;
    document.getElementById('opacityValue').textContent = `${opacity}%`;
    uiSettings.opacity = opacity;
}

// تبديل التأثيرات
function toggleEffect(effect) {
    if (!selectedElement) {
        showNotification('يرجى تحديد عنصر أولاً', 'warning');
        return;
    }
    
    const effectClass = `ui-effect-${effect}`;
    const btn = document.querySelector(`[data-effect="${effect}"]`);
    
    if (selectedElement.classList.contains(effectClass)) {
        selectedElement.classList.remove(effectClass);
        btn.classList.remove('active');
        uiSettings.effects = uiSettings.effects.filter(e => e !== effect);
        showNotification(`تم إزالة تأثير ${effect}`, 'info');
    } else {
        selectedElement.classList.add(effectClass);
        btn.classList.add('active');
        uiSettings.effects.push(effect);
        showNotification(`تم إضافة تأثير ${effect}`, 'success');
    }
}

// تعيين نوع الجهاز
function setDevice(device) {
    // إزالة جميع فئات الجهاز
    document.body.className = document.body.className.replace(/device-\w+/g, '');
    
    // إضافة فئة الجهاز الجديد
    document.body.classList.add(`device-${device}`);
    
    // تحديث الأزرار
    document.querySelectorAll('[data-device]').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-device="${device}"]`).classList.add('active');
    
    // تطبيق أنماط الجهاز
    applyDeviceStyles(device);
    
    uiSettings.device = device;
    showNotification(`تم التبديل إلى وضع ${device}`, 'success');
}

// تطبيق أنماط الجهاز
function applyDeviceStyles(device) {
    const root = document.documentElement;
    
    switch(device) {
        case 'mobile':
            root.style.setProperty('--container-width', '100%');
            root.style.setProperty('--font-size-base', '14px');
            root.style.setProperty('--spacing-base', '0.5rem');
            break;
        case 'tablet':
            root.style.setProperty('--container-width', '768px');
            root.style.setProperty('--font-size-base', '15px');
            root.style.setProperty('--spacing-base', '0.75rem');
            break;
        case 'desktop':
        default:
            root.style.setProperty('--container-width', '1200px');
            root.style.setProperty('--font-size-base', '16px');
            root.style.setProperty('--spacing-base', '1rem');
            break;
    }
}

// إعادة تعيين الواجهة
function resetUI() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
        // إعادة تعيين العناصر
        document.querySelectorAll('.ui-element').forEach(element => {
            element.className = 'ui-element card bg-white rounded-lg shadow-lg p-6';
            element.style.cssText = '';
        });
        
        // إعادة تعيين الإعدادات
        uiSettings = {
            position: 'center',
            size: 'md',
            width: 100,
            height: 100,
            primaryColor: '#3b82f6',
            opacity: 80,
            effects: [],
            device: 'desktop'
        };
        
        // إعادة تعيين الواجهة
        setPrimaryColor('#3b82f6');
        setDevice('desktop');
        
        // إعادة تعيين الأزرار
        document.querySelectorAll('.control-btn, .position-btn, .size-btn').forEach(btn => {
            btn.classList.remove('active');
        });
        
        // تفعيل الإعدادات الافتراضية
        document.querySelector('[data-position="center"]').classList.add('active');
        document.querySelector('[data-size="md"]').classList.add('active');
        document.querySelector('[data-device="desktop"]').classList.add('active');
        
        selectedElement = null;
        showNotification('تم إعادة تعيين جميع الإعدادات', 'success');
    }
}

// حفظ إعدادات الواجهة
function saveUISettings() {
    localStorage.setItem('uiSettings', JSON.stringify(uiSettings));
    showNotification('تم حفظ إعدادات الواجهة', 'success');
}

// تحميل إعدادات الواجهة
function loadUISettings() {
    const saved = localStorage.getItem('uiSettings');
    if (saved) {
        uiSettings = JSON.parse(saved);
        applyUISettings();
        showNotification('تم تحميل إعدادات الواجهة المحفوظة', 'info');
    }
}

// تطبيق الإعدادات المحفوظة
function applyUISettings() {
    setPrimaryColor(uiSettings.primaryColor);
    setDevice(uiSettings.device);
    
    // تحديث المنزلقات
    document.getElementById('widthSlider').value = uiSettings.width;
    document.getElementById('heightSlider').value = uiSettings.height;
    document.getElementById('opacitySlider').value = uiSettings.opacity;
    
    // تحديث القيم المعروضة
    document.getElementById('widthValue').textContent = `${uiSettings.width}%`;
    document.getElementById('heightValue').textContent = `${uiSettings.height}%`;
    document.getElementById('opacityValue').textContent = `${uiSettings.opacity}%`;
}

// تصدير إعدادات الواجهة
function exportUISettings() {
    const dataStr = JSON.stringify(uiSettings, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = 'ui-settings.json';
    link.click();
    
    URL.revokeObjectURL(url);
    showNotification('تم تصدير إعدادات الواجهة', 'success');
}

// استيراد إعدادات الواجهة
function importUISettings() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    
    input.onchange = function(event) {
        const file = event.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const imported = JSON.parse(e.target.result);
                    uiSettings = imported;
                    applyUISettings();
                    showNotification('تم استيراد إعدادات الواجهة بنجاح', 'success');
                } catch (error) {
                    showNotification('خطأ في ملف الإعدادات', 'error');
                }
            };
            reader.readAsText(file);
        }
    };
    
    input.click();
}

// إضافة أنماط CSS للعناصر المحددة
const style = document.createElement('style');
style.textContent = `
    .ui-element.selected {
        border: 2px solid var(--primary-color) !important;
        box-shadow: 0 0 10px rgba(59, 130, 246, 0.3) !important;
    }
    
    .device-mobile .ui-element {
        font-size: 0.875rem;
        padding: 1rem;
    }
    
    .device-tablet .ui-element {
        font-size: 0.9375rem;
        padding: 1.25rem;
    }
    
    .device-desktop .ui-element {
        font-size: 1rem;
        padding: 1.5rem;
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
