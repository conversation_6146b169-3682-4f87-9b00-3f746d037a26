/**
 * Service Worker للتطبيق التقدمي
 * نظام إدارة الاشتراكات المتطور
 * تطوير: المهندس محمد ياسر الجبوري
 */

const CACHE_NAME = 'subscription-manager-v1.0.0';
const OFFLINE_URL = '/offline';

// الملفات المطلوب تخزينها مؤقتاً
const CACHE_URLS = [
    '/',
    '/dashboard',
    '/subscriptions',
    '/invoices',
    '/ai_insights',
    '/static/css/animations.css',
    '/static/css/notifications.css',
    '/static/js/navigation.js',
    '/static/js/notifications.js',
    '/static/manifest.json',
    'https://cdn.tailwindcss.com',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
    'https://cdn.jsdelivr.net/npm/chart.js',
    'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap'
];

// تثبيت Service Worker
self.addEventListener('install', event => {
    console.log('🔧 تثبيت Service Worker...');
    
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => {
                console.log('📦 تخزين الملفات الأساسية...');
                return cache.addAll(CACHE_URLS);
            })
            .then(() => {
                console.log('✅ تم تثبيت Service Worker بنجاح');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('❌ خطأ في تثبيت Service Worker:', error);
            })
    );
});

// تفعيل Service Worker
self.addEventListener('activate', event => {
    console.log('🚀 تفعيل Service Worker...');
    
    event.waitUntil(
        caches.keys()
            .then(cacheNames => {
                return Promise.all(
                    cacheNames.map(cacheName => {
                        if (cacheName !== CACHE_NAME) {
                            console.log('🗑️ حذف cache قديم:', cacheName);
                            return caches.delete(cacheName);
                        }
                    })
                );
            })
            .then(() => {
                console.log('✅ تم تفعيل Service Worker بنجاح');
                return self.clients.claim();
            })
    );
});

// اعتراض الطلبات
self.addEventListener('fetch', event => {
    // تجاهل الطلبات غير HTTP/HTTPS
    if (!event.request.url.startsWith('http')) {
        return;
    }

    // استراتيجية Cache First للموارد الثابتة
    if (isStaticResource(event.request.url)) {
        event.respondWith(cacheFirst(event.request));
        return;
    }

    // استراتيجية Network First للصفحات والAPI
    if (isPageRequest(event.request) || isAPIRequest(event.request)) {
        event.respondWith(networkFirst(event.request));
        return;
    }

    // استراتيجية افتراضية
    event.respondWith(
        fetch(event.request).catch(() => {
            return caches.match(event.request);
        })
    );
});

// استراتيجية Cache First
async function cacheFirst(request) {
    try {
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }

        const networkResponse = await fetch(request);
        if (networkResponse.ok) {
            const cache = await caches.open(CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }
        return networkResponse;
    } catch (error) {
        console.error('خطأ في Cache First:', error);
        return caches.match(request);
    }
}

// استراتيجية Network First
async function networkFirst(request) {
    try {
        const networkResponse = await fetch(request);
        
        if (networkResponse.ok) {
            const cache = await caches.open(CACHE_NAME);
            cache.put(request, networkResponse.clone());
        }
        
        return networkResponse;
    } catch (error) {
        console.log('🌐 لا يوجد اتصال، استخدام Cache...');
        
        const cachedResponse = await caches.match(request);
        if (cachedResponse) {
            return cachedResponse;
        }

        // إرجاع صفحة offline للصفحات
        if (isPageRequest(request)) {
            return caches.match(OFFLINE_URL) || createOfflineResponse();
        }

        // إرجاع استجابة JSON فارغة للAPI
        if (isAPIRequest(request)) {
            return new Response(
                JSON.stringify({ 
                    error: 'لا يوجد اتصال بالإنترنت',
                    offline: true 
                }),
                {
                    status: 503,
                    headers: { 'Content-Type': 'application/json' }
                }
            );
        }

        throw error;
    }
}

// فحص إذا كان الطلب لمورد ثابت
function isStaticResource(url) {
    return url.includes('/static/') || 
           url.includes('cdn.') || 
           url.includes('fonts.') ||
           url.includes('.css') ||
           url.includes('.js') ||
           url.includes('.png') ||
           url.includes('.jpg') ||
           url.includes('.svg');
}

// فحص إذا كان الطلب لصفحة
function isPageRequest(request) {
    return request.method === 'GET' && 
           request.headers.get('accept') && 
           request.headers.get('accept').includes('text/html');
}

// فحص إذا كان الطلب لAPI
function isAPIRequest(request) {
    return request.url.includes('/api/');
}

// إنشاء استجابة offline
function createOfflineResponse() {
    const offlineHTML = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>غير متصل - نظام إدارة الاشتراكات</title>
            <style>
                body {
                    font-family: 'Cairo', sans-serif;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    margin: 0;
                    padding: 0;
                    min-height: 100vh;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: white;
                }
                .offline-container {
                    text-align: center;
                    padding: 2rem;
                    background: rgba(255, 255, 255, 0.1);
                    backdrop-filter: blur(20px);
                    border-radius: 20px;
                    border: 1px solid rgba(255, 255, 255, 0.2);
                    max-width: 400px;
                }
                .offline-icon {
                    font-size: 4rem;
                    margin-bottom: 1rem;
                    opacity: 0.8;
                }
                .offline-title {
                    font-size: 1.5rem;
                    font-weight: bold;
                    margin-bottom: 1rem;
                }
                .offline-message {
                    margin-bottom: 2rem;
                    opacity: 0.9;
                }
                .retry-btn {
                    background: rgba(255, 255, 255, 0.2);
                    border: 1px solid rgba(255, 255, 255, 0.3);
                    color: white;
                    padding: 0.75rem 1.5rem;
                    border-radius: 10px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                }
                .retry-btn:hover {
                    background: rgba(255, 255, 255, 0.3);
                    transform: translateY(-2px);
                }
            </style>
        </head>
        <body>
            <div class="offline-container">
                <div class="offline-icon">📡</div>
                <h1 class="offline-title">غير متصل بالإنترنت</h1>
                <p class="offline-message">
                    لا يمكن الوصول إلى الخادم حالياً. 
                    يرجى التحقق من اتصال الإنترنت والمحاولة مرة أخرى.
                </p>
                <button class="retry-btn" onclick="window.location.reload()">
                    إعادة المحاولة
                </button>
            </div>
        </body>
        </html>
    `;

    return new Response(offlineHTML, {
        status: 503,
        headers: { 'Content-Type': 'text/html; charset=utf-8' }
    });
}

// معالجة الرسائل من التطبيق الرئيسي
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
    
    if (event.data && event.data.type === 'GET_VERSION') {
        event.ports[0].postMessage({ version: CACHE_NAME });
    }
    
    if (event.data && event.data.type === 'CACHE_URLS') {
        event.waitUntil(
            caches.open(CACHE_NAME)
                .then(cache => cache.addAll(event.data.urls))
        );
    }
});

// معالجة إشعارات Push
self.addEventListener('push', event => {
    if (!event.data) return;

    const data = event.data.json();
    const options = {
        body: data.body || 'إشعار جديد من نظام إدارة الاشتراكات',
        icon: '/static/images/icon-192x192.png',
        badge: '/static/images/badge-icon.png',
        tag: data.tag || 'subscription-notification',
        data: data.data || {},
        actions: [
            {
                action: 'view',
                title: 'عرض',
                icon: '/static/images/action-view.png'
            },
            {
                action: 'dismiss',
                title: 'إغلاق',
                icon: '/static/images/action-dismiss.png'
            }
        ],
        requireInteraction: data.requireInteraction || false,
        silent: data.silent || false,
        vibrate: data.vibrate || [200, 100, 200]
    };

    event.waitUntil(
        self.registration.showNotification(
            data.title || 'نظام إدارة الاشتراكات',
            options
        )
    );
});

// معالجة النقر على الإشعارات
self.addEventListener('notificationclick', event => {
    event.notification.close();

    if (event.action === 'view') {
        const urlToOpen = event.notification.data.url || '/dashboard';
        
        event.waitUntil(
            clients.matchAll({ type: 'window', includeUncontrolled: true })
                .then(clientList => {
                    // البحث عن نافذة مفتوحة
                    for (const client of clientList) {
                        if (client.url.includes(urlToOpen) && 'focus' in client) {
                            return client.focus();
                        }
                    }
                    
                    // فتح نافذة جديدة
                    if (clients.openWindow) {
                        return clients.openWindow(urlToOpen);
                    }
                })
        );
    }
});

// معالجة مزامنة البيانات في الخلفية
self.addEventListener('sync', event => {
    if (event.tag === 'background-sync') {
        event.waitUntil(doBackgroundSync());
    }
});

// تنفيذ مزامنة البيانات
async function doBackgroundSync() {
    try {
        // مزامنة البيانات المحلية مع الخادم
        console.log('🔄 تنفيذ مزامنة البيانات...');
        
        // يمكن إضافة منطق المزامنة هنا
        // مثل إرسال البيانات المحفوظة محلياً
        
        console.log('✅ تمت مزامنة البيانات بنجاح');
    } catch (error) {
        console.error('❌ خطأ في مزامنة البيانات:', error);
    }
}

// تسجيل معلومات Service Worker
console.log('🎯 Service Worker جاهز للعمل');
console.log('📋 إصدار Cache:', CACHE_NAME);
console.log('🌐 نطاق العمل:', self.registration.scope);
