#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الاشتراكات المتطور
تطوير: المهندس محمد ياسر الجبوري

ملف تشغيل مبسط للنظام
"""

import os
import sys

def check_requirements():
    """فحص المتطلبات الأساسية"""
    required_modules = [
        'flask',
        'flask_sqlalchemy',
        'werkzeug'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print("❌ المتطلبات التالية مفقودة:")
        for module in missing_modules:
            print(f"   - {module}")
        print("\n📦 لتثبيت المتطلبات، قم بتشغيل:")
        print("   pip install -r requirements.txt")
        return False
    
    return True

def main():
    """الدالة الرئيسية"""
    print("🚀 نظام إدارة الاشتراكات المتطور")
    print("👨‍💻 تطوير: المهندس محمد ياسر الجبوري")
    print("=" * 50)
    
    # فحص المتطلبات
    if not check_requirements():
        sys.exit(1)
    
    # تشغيل التطبيق
    try:
        from app import app
        print("✅ تم تحميل التطبيق بنجاح")
        print("🌐 فتح المتصفح على: http://localhost:5000")
        print("👤 اسم المستخدم: admin")
        print("🔑 كلمة المرور: 123456")
        print("=" * 50)
        
        # تشغيل الخادم
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True,
            use_reloader=True
        )
        
    except ImportError as e:
        print(f"❌ خطأ في استيراد التطبيق: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
