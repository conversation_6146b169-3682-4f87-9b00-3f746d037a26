{% extends "base.html" %}

{% block title %}إضافة دفعة جديدة - {{ subscription.customer_name }}{% endblock %}
{% block page_title %}إضافة دفعة جديدة{% endblock %}
{% block page_description %}{{ subscription.customer_name }} - {{ subscription.network_name }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/subscriptions-advanced.css') }}">
<style>
.payment-form {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

.payment-form:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    color: white;
    font-weight: 500;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 0.75rem 1rem;
    color: white;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: rgba(255, 255, 255, 0.15);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.subscription-info {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
}

.quick-amounts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.quick-amount {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 0.5rem;
    text-align: center;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.875rem;
}

.quick-amount:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.btn-submit {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 1rem 2rem;
    border-radius: 12px;
    border: none;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    justify-content: center;
    min-width: 200px;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

@media (max-width: 768px) {
    .payment-form,
    .subscription-info {
        margin: 0 1rem 1.5rem;
        padding: 1.5rem;
    }
}
</style>
{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3 space-x-reverse">
    <a href="{{ url_for('customer_statement', subscription_id=subscription.id) }}" class="btn-secondary">
        <i class="fas fa-arrow-right ml-2"></i>
        العودة لكشف الحساب
    </a>
</div>
{% endblock %}

{% block content %}
<!-- معلومات الاشتراك -->
<div class="subscription-info">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
            <h3 class="text-xl font-bold mb-3">معلومات العميل</h3>
            <p><strong>الاسم:</strong> {{ subscription.customer_name }}</p>
            <p><strong>الشبكة:</strong> {{ subscription.network_name }}</p>
            <p><strong>نوع الاشتراك:</strong> 
                {% if subscription.subscription_type == 'monthly' %}شهري
                {% elif subscription.subscription_type == 'semi_annual' %}نصف سنوي
                {% elif subscription.subscription_type == 'annual' %}سنوي
                {% else %}{{ subscription.subscription_type }}{% endif %}
            </p>
        </div>
        <div>
            <h3 class="text-xl font-bold mb-3">المعلومات المالية</h3>
            <p><strong>سعر الاشتراك:</strong> ${{ "%.2f"|format(subscription.price) }}</p>
            <p><strong>إجمالي المدفوع:</strong> ${{ "%.2f"|format(subscription.total_paid) }}</p>
            <p><strong>المبلغ المتبقي:</strong> ${{ "%.2f"|format(subscription.remaining_amount) }}</p>
        </div>
    </div>
</div>

<!-- نموذج إضافة الدفعة -->
<div class="payment-form max-w-2xl mx-auto">
    <h3 class="text-2xl font-bold text-white mb-6 text-center">إضافة دفعة جديدة</h3>
    
    <form method="POST">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="form-group">
                <label for="amount">مبلغ الدفعة ($) *</label>
                <input type="number" id="amount" name="amount" step="0.01" required 
                       placeholder="0.00" value="{{ subscription.remaining_amount if subscription.remaining_amount > 0 else subscription.price }}">
                <div class="quick-amounts">
                    <div class="quick-amount" onclick="setAmount({{ subscription.price }})">
                        ${{ "%.0f"|format(subscription.price) }}
                    </div>
                    {% if subscription.remaining_amount > 0 %}
                    <div class="quick-amount" onclick="setAmount({{ subscription.remaining_amount }})">
                        ${{ "%.0f"|format(subscription.remaining_amount) }}
                    </div>
                    {% endif %}
                    <div class="quick-amount" onclick="setAmount({{ subscription.price / 2 }})">
                        ${{ "%.0f"|format(subscription.price / 2) }}
                    </div>
                </div>
            </div>
            
            <div class="form-group">
                <label for="payment_date">تاريخ الدفعة *</label>
                <input type="date" id="payment_date" name="payment_date" required 
                       value="{{ today }}">
            </div>
            
            <div class="form-group">
                <label for="payment_method">طريقة الدفع *</label>
                <select id="payment_method" name="payment_method" required>
                    <option value="cash">نقداً</option>
                    <option value="bank_transfer">تحويل بنكي</option>
                    <option value="card">بطاقة ائتمان</option>
                    <option value="check">شيك</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="status">حالة الدفعة</label>
                <select id="status" name="status">
                    <option value="completed">مكتملة</option>
                    <option value="pending">معلقة</option>
                    <option value="failed">فشلت</option>
                </select>
            </div>
            
            <div class="form-group md:col-span-2">
                <label for="receipt_number">رقم الإيصال</label>
                <input type="text" id="receipt_number" name="receipt_number" 
                       placeholder="أدخل رقم الإيصال إذا كان متوفراً">
            </div>
            
            <div class="form-group md:col-span-2">
                <label for="notes">ملاحظات</label>
                <textarea id="notes" name="notes" rows="3" 
                          placeholder="أدخل أي ملاحظات حول هذه الدفعة"></textarea>
            </div>
        </div>
        
        <div class="flex justify-center gap-4 mt-8">
            <button type="submit" class="btn-submit">
                <i class="fas fa-save"></i>
                حفظ الدفعة
            </button>
            <a href="{{ url_for('customer_statement', subscription_id=subscription.id) }}" class="btn-secondary">
                <i class="fas fa-times"></i>
                إلغاء
            </a>
        </div>
    </form>
</div>

<!-- معاينة الدفعة -->
<div class="payment-form max-w-2xl mx-auto">
    <h3 class="text-xl font-bold text-white mb-4">معاينة الدفعة</h3>
    <div id="paymentPreview" class="bg-white bg-opacity-5 rounded-lg p-4">
        <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
                <span class="text-white text-opacity-70">المبلغ:</span>
                <span class="text-white font-medium" id="previewAmount">$0.00</span>
            </div>
            <div>
                <span class="text-white text-opacity-70">التاريخ:</span>
                <span class="text-white font-medium" id="previewDate">{{ today }}</span>
            </div>
            <div>
                <span class="text-white text-opacity-70">طريقة الدفع:</span>
                <span class="text-white font-medium" id="previewMethod">نقداً</span>
            </div>
            <div>
                <span class="text-white text-opacity-70">الحالة:</span>
                <span class="text-white font-medium" id="previewStatus">مكتملة</span>
            </div>
        </div>
        <div class="mt-4 pt-4 border-t border-white border-opacity-20">
            <div class="flex justify-between">
                <span class="text-white text-opacity-70">المبلغ المتبقي بعد الدفعة:</span>
                <span class="text-white font-bold" id="previewRemaining">${{ "%.2f"|format(subscription.remaining_amount) }}</span>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// تعيين مبلغ سريع
function setAmount(amount) {
    document.getElementById('amount').value = amount.toFixed(2);
    updatePreview();
}

// تحديث المعاينة
function updatePreview() {
    const amount = parseFloat(document.getElementById('amount').value) || 0;
    const date = document.getElementById('payment_date').value;
    const method = document.getElementById('payment_method').value;
    const status = document.getElementById('status').value;
    
    const remainingAmount = {{ subscription.remaining_amount }} - amount;
    
    document.getElementById('previewAmount').textContent = '$' + amount.toFixed(2);
    document.getElementById('previewDate').textContent = date;
    document.getElementById('previewMethod').textContent = getMethodLabel(method);
    document.getElementById('previewStatus').textContent = getStatusLabel(status);
    document.getElementById('previewRemaining').textContent = '$' + Math.max(0, remainingAmount).toFixed(2);
}

// تسميات طرق الدفع
function getMethodLabel(method) {
    const labels = {
        'cash': 'نقداً',
        'bank_transfer': 'تحويل بنكي',
        'card': 'بطاقة ائتمان',
        'check': 'شيك'
    };
    return labels[method] || method;
}

// تسميات حالات الدفع
function getStatusLabel(status) {
    const labels = {
        'completed': 'مكتملة',
        'pending': 'معلقة',
        'failed': 'فشلت'
    };
    return labels[status] || status;
}

// تحديث المعاينة عند تغيير القيم
document.addEventListener('DOMContentLoaded', function() {
    const inputs = ['amount', 'payment_date', 'payment_method', 'status'];
    inputs.forEach(id => {
        document.getElementById(id).addEventListener('change', updatePreview);
        document.getElementById(id).addEventListener('input', updatePreview);
    });
    
    // تحديث أولي
    updatePreview();
});
</script>
{% endblock %}
