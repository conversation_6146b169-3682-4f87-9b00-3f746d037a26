{% extends "base.html" %}

{% block title %}لوحة التحكم المتقدمة - نظام إدارة الاشتراكات{% endblock %}
{% block page_title %}لوحة التحكم المتقدمة{% endblock %}
{% block page_description %}إحصائيات شاملة وتحليلات ذكية للنظام{% endblock %}

{% block extra_css %}
<style>
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.dashboard-card {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    transition: all var(--transition-medium);
    position: relative;
    overflow: hidden;
}

.dashboard-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.card-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.card-title {
    color: var(--text-primary);
    font-size: 1.125rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.card-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
}

.card-menu {
    position: relative;
}

.card-menu-btn {
    width: 2rem;
    height: 2rem;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    border-radius: 4px;
    cursor: pointer;
    transition: all var(--transition-medium);
}

.card-menu-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
}

.stat-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    line-height: 1;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
    margin-bottom: 1rem;
}

.stat-change {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
}

.stat-change.positive {
    color: var(--success-color);
}

.stat-change.negative {
    color: var(--error-color);
}

.stat-change.neutral {
    color: var(--text-muted);
}

.chart-container {
    height: 200px;
    margin-top: 1rem;
    position: relative;
}

.mini-chart {
    height: 60px;
    margin-top: 1rem;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    margin-top: 1rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    border-radius: 4px;
    transition: width 1s ease-in-out;
}

.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    color: white;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-title {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.activity-description {
    color: var(--text-secondary);
    font-size: 0.75rem;
}

.activity-time {
    color: var(--text-muted);
    font-size: 0.75rem;
    white-space: nowrap;
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    margin-top: 1rem;
}

.quick-action {
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-medium);
    text-decoration: none;
    color: var(--text-primary);
}

.quick-action:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    color: var(--text-primary);
}

.quick-action-icon {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
    color: var(--primary-color);
}

.quick-action-label {
    font-size: 0.75rem;
    font-weight: 600;
}

.notification-badge {
    position: absolute;
    top: -4px;
    right: -4px;
    width: 18px;
    height: 18px;
    background: var(--error-color);
    color: white;
    border-radius: 50%;
    font-size: 0.625rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: pulse 2s infinite;
}

.weather-widget {
    text-align: center;
    padding: 1rem;
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    border-radius: 12px;
    color: white;
    margin-top: 1rem;
}

.weather-temp {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.weather-desc {
    font-size: 0.875rem;
    opacity: 0.9;
}

.calendar-widget {
    margin-top: 1rem;
}

.calendar-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1rem;
    color: var(--text-primary);
    font-weight: 600;
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.25rem;
}

.calendar-day {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    color: var(--text-secondary);
    border-radius: 4px;
    cursor: pointer;
    transition: all var(--transition-medium);
}

.calendar-day:hover {
    background: rgba(255, 255, 255, 0.1);
}

.calendar-day.today {
    background: var(--primary-color);
    color: white;
}

.calendar-day.has-event {
    background: var(--success-color);
    color: white;
}

.system-health {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-top: 1rem;
}

.health-metric {
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
}

.health-value {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.health-value.good {
    color: var(--success-color);
}

.health-value.warning {
    color: var(--warning-color);
}

.health-value.critical {
    color: var(--error-color);
}

.health-label {
    font-size: 0.75rem;
    color: var(--text-secondary);
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .quick-actions {
        grid-template-columns: 1fr;
    }
    
    .system-health {
        grid-template-columns: 1fr;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="dashboard-grid">
    <!-- إحصائيات الاشتراكات -->
    <div class="dashboard-card">
        <div class="card-header">
            <h3 class="card-title">
                <div class="card-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                    <i class="fas fa-users"></i>
                </div>
                الاشتراكات
            </h3>
            <button class="card-menu-btn">
                <i class="fas fa-ellipsis-v"></i>
            </button>
        </div>
        
        <div class="stat-value">{{ total_subscriptions or 1247 }}</div>
        <div class="stat-label">إجمالي الاشتراكات</div>
        
        <div class="stat-change positive">
            <i class="fas fa-arrow-up"></i>
            <span>+12.5% من الشهر الماضي</span>
        </div>
        
        <div class="progress-bar">
            <div class="progress-fill" style="width: 75%;"></div>
        </div>
    </div>

    <!-- الإيرادات -->
    <div class="dashboard-card">
        <div class="card-header">
            <h3 class="card-title">
                <div class="card-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                    <i class="fas fa-dollar-sign"></i>
                </div>
                الإيرادات
            </h3>
            <button class="card-menu-btn">
                <i class="fas fa-ellipsis-v"></i>
            </button>
        </div>
        
        <div class="stat-value">{{ '{:,.0f}'.format(total_revenue or 89750) }}</div>
        <div class="stat-label">ريال يمني</div>
        
        <div class="stat-change positive">
            <i class="fas fa-arrow-up"></i>
            <span>+8.3% من الشهر الماضي</span>
        </div>
        
        <div class="mini-chart" id="revenueChart"></div>
    </div>

    <!-- المستخدمون النشطون -->
    <div class="dashboard-card">
        <div class="card-header">
            <h3 class="card-title">
                <div class="card-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                    <i class="fas fa-user-check"></i>
                </div>
                المستخدمون النشطون
            </h3>
            <div class="notification-badge">3</div>
        </div>
        
        <div class="stat-value">{{ active_users or 156 }}</div>
        <div class="stat-label">مستخدم نشط الآن</div>
        
        <div class="stat-change neutral">
            <i class="fas fa-minus"></i>
            <span>لا تغيير</span>
        </div>
        
        <div class="quick-actions">
            <a href="/advanced-users" class="quick-action">
                <div class="quick-action-icon">
                    <i class="fas fa-users-cog"></i>
                </div>
                <div class="quick-action-label">إدارة المستخدمين</div>
            </a>
            <a href="/distributors-management" class="quick-action">
                <div class="quick-action-icon">
                    <i class="fas fa-store"></i>
                </div>
                <div class="quick-action-label">الموزعين</div>
            </a>
        </div>
    </div>

    <!-- الأنشطة الحديثة -->
    <div class="dashboard-card">
        <div class="card-header">
            <h3 class="card-title">
                <div class="card-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                    <i class="fas fa-clock"></i>
                </div>
                الأنشطة الحديثة
            </h3>
        </div>
        
        <div class="activity-list">
            <div class="activity-item">
                <div class="activity-icon" style="background: var(--success-color);">
                    <i class="fas fa-plus"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">اشتراك جديد</div>
                    <div class="activity-description">تم إضافة اشتراك جديد للعميل أحمد محمد</div>
                </div>
                <div class="activity-time">منذ 5 دقائق</div>
            </div>
            
            <div class="activity-item">
                <div class="activity-icon" style="background: var(--primary-color);">
                    <i class="fas fa-credit-card"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">دفعة جديدة</div>
                    <div class="activity-description">تم استلام دفعة بقيمة 15,000 ريال</div>
                </div>
                <div class="activity-time">منذ 12 دقيقة</div>
            </div>
            
            <div class="activity-item">
                <div class="activity-icon" style="background: var(--warning-color);">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">تنبيه انتهاء اشتراك</div>
                    <div class="activity-description">اشتراك العميل سارة أحمد ينتهي خلال 3 أيام</div>
                </div>
                <div class="activity-time">منذ 25 دقيقة</div>
            </div>
            
            <div class="activity-item">
                <div class="activity-icon" style="background: var(--info-color);">
                    <i class="fas fa-user-plus"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">مستخدم جديد</div>
                    <div class="activity-description">انضم مستخدم جديد: محمد علي</div>
                </div>
                <div class="activity-time">منذ ساعة</div>
            </div>
        </div>
    </div>

    <!-- صحة النظام -->
    <div class="dashboard-card">
        <div class="card-header">
            <h3 class="card-title">
                <div class="card-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626);">
                    <i class="fas fa-heartbeat"></i>
                </div>
                صحة النظام
            </h3>
        </div>
        
        <div class="system-health">
            <div class="health-metric">
                <div class="health-value good">98%</div>
                <div class="health-label">وقت التشغيل</div>
            </div>
            <div class="health-metric">
                <div class="health-value good">45ms</div>
                <div class="health-label">زمن الاستجابة</div>
            </div>
            <div class="health-metric">
                <div class="health-value warning">72%</div>
                <div class="health-label">استخدام الذاكرة</div>
            </div>
        </div>
        
        <div class="quick-actions">
            <a href="/system-monitor" class="quick-action">
                <div class="quick-action-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
                <div class="quick-action-label">مراقبة مفصلة</div>
            </a>
            <a href="/security" class="quick-action">
                <div class="quick-action-icon">
                    <i class="fas fa-shield-alt"></i>
                </div>
                <div class="quick-action-label">الأمان</div>
            </a>
        </div>
    </div>

    <!-- التقويم والطقس -->
    <div class="dashboard-card">
        <div class="card-header">
            <h3 class="card-title">
                <div class="card-icon" style="background: linear-gradient(135deg, #06b6d4, #0891b2);">
                    <i class="fas fa-calendar-alt"></i>
                </div>
                التقويم
            </h3>
        </div>
        
        <div class="calendar-widget">
            <div class="calendar-header">
                <button><i class="fas fa-chevron-left"></i></button>
                <span>يناير 2025</span>
                <button><i class="fas fa-chevron-right"></i></button>
            </div>
            
            <div class="calendar-grid">
                <div class="calendar-day">س</div>
                <div class="calendar-day">ح</div>
                <div class="calendar-day">ن</div>
                <div class="calendar-day">ث</div>
                <div class="calendar-day">ر</div>
                <div class="calendar-day">خ</div>
                <div class="calendar-day">ج</div>
                
                <div class="calendar-day">29</div>
                <div class="calendar-day">30</div>
                <div class="calendar-day">31</div>
                <div class="calendar-day">1</div>
                <div class="calendar-day">2</div>
                <div class="calendar-day">3</div>
                <div class="calendar-day">4</div>
                
                <div class="calendar-day">5</div>
                <div class="calendar-day">6</div>
                <div class="calendar-day">7</div>
                <div class="calendar-day">8</div>
                <div class="calendar-day">9</div>
                <div class="calendar-day">10</div>
                <div class="calendar-day">11</div>
                
                <div class="calendar-day">12</div>
                <div class="calendar-day">13</div>
                <div class="calendar-day">14</div>
                <div class="calendar-day">15</div>
                <div class="calendar-day">16</div>
                <div class="calendar-day">17</div>
                <div class="calendar-day">18</div>
                
                <div class="calendar-day">19</div>
                <div class="calendar-day">20</div>
                <div class="calendar-day today">21</div>
                <div class="calendar-day">22</div>
                <div class="calendar-day has-event">23</div>
                <div class="calendar-day">24</div>
                <div class="calendar-day">25</div>
            </div>
        </div>
        
        <div class="weather-widget">
            <div class="weather-temp">24°</div>
            <div class="weather-desc">صنعاء - صافي</div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// لوحة التحكم المتقدمة
document.addEventListener('DOMContentLoaded', function() {
    initializeAdvancedDashboard();
});

function initializeAdvancedDashboard() {
    // إنشاء الرسوم البيانية
    createMiniCharts();
    
    // تحديث البيانات كل 30 ثانية
    setInterval(updateDashboardData, 30000);
    
    // تحديث الوقت الحقيقي
    updateRealTimeData();
    setInterval(updateRealTimeData, 1000);
}

function createMiniCharts() {
    // رسم بياني للإيرادات
    const revenueCtx = document.getElementById('revenueChart');
    if (revenueCtx) {
        new Chart(revenueCtx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    data: [65000, 72000, 68000, 89000, 85000, 89750],
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        display: false
                    },
                    y: {
                        display: false
                    }
                },
                elements: {
                    point: {
                        radius: 0
                    }
                }
            }
        });
    }
}

function updateDashboardData() {
    fetch('/api/dashboard/data')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStatistics(data.stats);
                updateActivities(data.activities);
                updateSystemHealth(data.health);
            }
        })
        .catch(error => {
            console.error('Error updating dashboard data:', error);
        });
}

function updateStatistics(stats) {
    // تحديث الإحصائيات
    const elements = {
        subscriptions: document.querySelector('.dashboard-card:nth-child(1) .stat-value'),
        revenue: document.querySelector('.dashboard-card:nth-child(2) .stat-value'),
        activeUsers: document.querySelector('.dashboard-card:nth-child(3) .stat-value')
    };
    
    if (elements.subscriptions && stats.subscriptions) {
        animateNumber(elements.subscriptions, stats.subscriptions);
    }
    
    if (elements.revenue && stats.revenue) {
        animateNumber(elements.revenue, stats.revenue);
    }
    
    if (elements.activeUsers && stats.activeUsers) {
        animateNumber(elements.activeUsers, stats.activeUsers);
    }
}

function updateActivities(activities) {
    const activityList = document.querySelector('.activity-list');
    if (activityList && activities) {
        // تحديث قائمة الأنشطة
        activityList.innerHTML = activities.map(activity => `
            <div class="activity-item">
                <div class="activity-icon" style="background: ${activity.color};">
                    <i class="${activity.icon}"></i>
                </div>
                <div class="activity-content">
                    <div class="activity-title">${activity.title}</div>
                    <div class="activity-description">${activity.description}</div>
                </div>
                <div class="activity-time">${activity.time}</div>
            </div>
        `).join('');
    }
}

function updateSystemHealth(health) {
    if (health) {
        const healthMetrics = document.querySelectorAll('.health-value');
        if (healthMetrics[0]) healthMetrics[0].textContent = health.uptime + '%';
        if (healthMetrics[1]) healthMetrics[1].textContent = health.responseTime + 'ms';
        if (healthMetrics[2]) healthMetrics[2].textContent = health.memoryUsage + '%';
        
        // تحديث ألوان المؤشرات
        healthMetrics.forEach((metric, index) => {
            const value = parseInt(metric.textContent);
            metric.className = 'health-value ' + getHealthStatus(value, index);
        });
    }
}

function getHealthStatus(value, metricType) {
    switch(metricType) {
        case 0: // uptime
            return value >= 95 ? 'good' : value >= 90 ? 'warning' : 'critical';
        case 1: // response time
            return value <= 100 ? 'good' : value <= 300 ? 'warning' : 'critical';
        case 2: // memory usage
            return value <= 70 ? 'good' : value <= 85 ? 'warning' : 'critical';
        default:
            return 'good';
    }
}

function animateNumber(element, targetValue) {
    const currentValue = parseInt(element.textContent.replace(/,/g, ''));
    const increment = (targetValue - currentValue) / 20;
    let current = currentValue;
    
    const animation = setInterval(() => {
        current += increment;
        if ((increment > 0 && current >= targetValue) || (increment < 0 && current <= targetValue)) {
            current = targetValue;
            clearInterval(animation);
        }
        element.textContent = Math.round(current).toLocaleString();
    }, 50);
}

function updateRealTimeData() {
    // تحديث الوقت في التقويم
    const now = new Date();
    const today = now.getDate();
    
    document.querySelectorAll('.calendar-day').forEach(day => {
        day.classList.remove('today');
        if (parseInt(day.textContent) === today) {
            day.classList.add('today');
        }
    });
}

// تفاعل مع بطاقات لوحة التحكم
document.querySelectorAll('.dashboard-card').forEach(card => {
    card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-8px)';
    });
    
    card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(-4px)';
    });
});

// تحديث شريط التقدم
document.querySelectorAll('.progress-fill').forEach(progress => {
    const width = progress.style.width;
    progress.style.width = '0%';
    setTimeout(() => {
        progress.style.width = width;
    }, 500);
});
</script>
{% endblock %}
