{% extends "base.html" %}

{% block title %}مراقبة النظام - نظام إدارة الاشتراكات{% endblock %}
{% block page_title %}مراقبة النظام{% endblock %}
{% block page_description %}مراقبة الأداء والحالة العامة للنظام{% endblock %}

{% block extra_css %}
<style>
.system-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.monitor-card {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    transition: all var(--transition-medium);
}

.monitor-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: between;
    margin-bottom: 1rem;
}

.card-title {
    color: var(--text-primary);
    font-size: 1.125rem;
    font-weight: 700;
    margin: 0;
}

.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

.status-healthy {
    background: var(--success-color);
    box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
}

.status-warning {
    background: var(--warning-color);
    box-shadow: 0 0 10px rgba(245, 158, 11, 0.5);
}

.status-error {
    background: var(--error-color);
    box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
}

.metric-row {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.metric-row:last-child {
    border-bottom: none;
}

.metric-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.metric-value {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.875rem;
}

.performance-chart {
    height: 200px;
    margin-top: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-muted);
}

.alerts-section {
    grid-column: 1 / -1;
}

.alert-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.alert-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    color: white;
}

.alert-content {
    flex: 1;
}

.alert-title {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.alert-message {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.alert-time {
    color: var(--text-muted);
    font-size: 0.75rem;
}

.refresh-btn {
    padding: 0.5rem 1rem;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 0.875rem;
    cursor: pointer;
    transition: all var(--transition-medium);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.refresh-btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-colored);
}

.uptime-display {
    font-family: 'Courier New', monospace;
    font-size: 1.25rem;
    color: var(--success-color);
    text-align: center;
    padding: 1rem;
    background: rgba(16, 185, 129, 0.1);
    border-radius: 8px;
    margin-top: 1rem;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@media (max-width: 768px) {
    .system-grid {
        grid-template-columns: 1fr;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="system-grid">
    <!-- حالة قاعدة البيانات -->
    <div class="monitor-card">
        <div class="card-header">
            <h3 class="card-title">قاعدة البيانات</h3>
            <div class="status-indicator status-{{ 'healthy' if health_data.database_status == 'healthy' else 'error' }}"></div>
        </div>
        <div class="metric-row">
            <span class="metric-label">الحالة:</span>
            <span class="metric-value">{{ 'متصلة' if health_data.database_status == 'healthy' else 'خطأ' }}</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">إجمالي الاشتراكات:</span>
            <span class="metric-value">{{ health_data.total_subscriptions or 0 }}</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">المدفوعات المعلقة:</span>
            <span class="metric-value">{{ health_data.pending_payments or 0 }}</span>
        </div>
    </div>

    <!-- المستخدمون النشطون -->
    <div class="monitor-card">
        <div class="card-header">
            <h3 class="card-title">المستخدمون</h3>
            <div class="status-indicator status-healthy"></div>
        </div>
        <div class="metric-row">
            <span class="metric-label">المستخدمون النشطون:</span>
            <span class="metric-value">{{ health_data.active_users or 0 }}</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">الجلسات الحالية:</span>
            <span class="metric-value">{{ health_data.active_sessions or 0 }}</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">آخر تسجيل دخول:</span>
            <span class="metric-value">منذ دقائق</span>
        </div>
    </div>

    <!-- الأداء -->
    <div class="monitor-card">
        <div class="card-header">
            <h3 class="card-title">الأداء</h3>
            <div class="status-indicator status-{{ 'healthy' if health_data.cache_status == 'healthy' else 'warning' }}"></div>
        </div>
        <div class="metric-row">
            <span class="metric-label">التخزين المؤقت:</span>
            <span class="metric-value">{{ 'نشط' if health_data.cache_status == 'healthy' else 'معطل' }}</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">متوسط وقت الاستجابة:</span>
            <span class="metric-value">< 200ms</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">استخدام الذاكرة:</span>
            <span class="metric-value">65%</span>
        </div>
    </div>

    <!-- وقت التشغيل -->
    <div class="monitor-card">
        <div class="card-header">
            <h3 class="card-title">وقت التشغيل</h3>
            <button class="refresh-btn" onclick="refreshSystemData()">
                <i class="fas fa-sync-alt"></i>
                تحديث
            </button>
        </div>
        <div class="uptime-display" id="uptimeDisplay">
            {{ "%.2f"|format(health_data.system_uptime / 3600) }} ساعة
        </div>
        <div class="metric-row">
            <span class="metric-label">آخر إعادة تشغيل:</span>
            <span class="metric-value">{{ moment().format('YYYY-MM-DD HH:mm') }}</span>
        </div>
    </div>

    <!-- التنبيهات والإشعارات -->
    <div class="monitor-card alerts-section">
        <div class="card-header">
            <h3 class="card-title">التنبيهات الحديثة</h3>
            <div class="status-indicator status-healthy"></div>
        </div>
        
        <div class="alert-item">
            <div class="alert-icon" style="background: var(--success-color);">
                <i class="fas fa-check"></i>
            </div>
            <div class="alert-content">
                <div class="alert-title">النظام يعمل بشكل طبيعي</div>
                <div class="alert-message">جميع الخدمات تعمل بكفاءة عالية</div>
                <div class="alert-time">منذ دقيقتين</div>
            </div>
        </div>

        <div class="alert-item">
            <div class="alert-icon" style="background: var(--info-color);">
                <i class="fas fa-info"></i>
            </div>
            <div class="alert-content">
                <div class="alert-title">تم إنشاء نسخة احتياطية</div>
                <div class="alert-message">تم إنشاء نسخة احتياطية تلقائية بنجاح</div>
                <div class="alert-time">منذ 30 دقيقة</div>
            </div>
        </div>

        <div class="alert-item">
            <div class="alert-icon" style="background: var(--warning-color);">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <div class="alert-content">
                <div class="alert-title">استخدام مرتفع للذاكرة</div>
                <div class="alert-message">استخدام الذاكرة وصل إلى 85%</div>
                <div class="alert-time">منذ ساعة</div>
            </div>
        </div>
    </div>

    <!-- الرسوم البيانية -->
    <div class="monitor-card">
        <div class="card-header">
            <h3 class="card-title">أداء النظام</h3>
        </div>
        <div class="performance-chart">
            <i class="fas fa-chart-area" style="font-size: 2rem; margin-left: 1rem;"></i>
            الرسوم البيانية قيد التطوير
        </div>
    </div>

    <!-- إحصائيات الأمان -->
    <div class="monitor-card">
        <div class="card-header">
            <h3 class="card-title">الأمان</h3>
            <div class="status-indicator status-healthy"></div>
        </div>
        <div class="metric-row">
            <span class="metric-label">محاولات الدخول الفاشلة:</span>
            <span class="metric-value">0</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">IPs محظورة:</span>
            <span class="metric-value">3</span>
        </div>
        <div class="metric-row">
            <span class="metric-label">آخر فحص أمني:</span>
            <span class="metric-value">منذ 5 دقائق</span>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function refreshSystemData() {
    const btn = document.querySelector('.refresh-btn');
    const icon = btn.querySelector('i');
    
    // إضافة تأثير التحديث
    icon.classList.add('fa-spin');
    btn.disabled = true;
    
    fetch('/api/system/health')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // تحديث البيانات
                updateSystemDisplay(data.health);
                showNotification('تم تحديث بيانات النظام', 'success');
            } else {
                showNotification('فشل في تحديث البيانات', 'error');
            }
        })
        .catch(error => {
            showNotification('خطأ في الاتصال بالخادم', 'error');
        })
        .finally(() => {
            icon.classList.remove('fa-spin');
            btn.disabled = false;
        });
}

function updateSystemDisplay(healthData) {
    // تحديث عرض البيانات
    const uptimeDisplay = document.getElementById('uptimeDisplay');
    if (uptimeDisplay && healthData.system_uptime) {
        const hours = (healthData.system_uptime / 3600).toFixed(2);
        uptimeDisplay.textContent = `${hours} ساعة`;
    }
}

// تحديث تلقائي كل 30 ثانية
setInterval(refreshSystemData, 30000);

// تحديث وقت التشغيل كل ثانية
setInterval(() => {
    const uptimeDisplay = document.getElementById('uptimeDisplay');
    if (uptimeDisplay) {
        const currentText = uptimeDisplay.textContent;
        const hours = parseFloat(currentText);
        if (!isNaN(hours)) {
            const newHours = (hours + (1/3600)).toFixed(2);
            uptimeDisplay.textContent = `${newHours} ساعة`;
        }
    }
}, 1000);
</script>
{% endblock %}
