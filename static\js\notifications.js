/**
 * نظام الإشعارات المتقدم
 * تطوير: المهندس محمد ياسر الجبوري
 * نظام إدارة الاشتراكات المتطور
 */

class AdvancedNotificationSystem {
    constructor() {
        this.notifications = [];
        this.settings = {
            sound: true,
            desktop: true,
            email: false,
            autoHide: true,
            hideDelay: 5000
        };
        this.init();
    }

    init() {
        console.log('🔔 تم تحميل نظام الإشعارات المتقدم');
        this.requestPermission();
        this.setupEventListeners();
        this.loadSettings();
        this.startPeriodicCheck();
    }

    // طلب إذن الإشعارات
    async requestPermission() {
        if ('Notification' in window) {
            const permission = await Notification.requestPermission();
            if (permission === 'granted') {
                console.log('✅ تم منح إذن الإشعارات');
            }
        }
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // مستمع للإشعارات الجديدة
        document.addEventListener('newNotification', (e) => {
            this.handleNewNotification(e.detail);
        });

        // مستمع لتغيير الإعدادات
        document.addEventListener('notificationSettingsChanged', (e) => {
            this.updateSettings(e.detail);
        });
    }

    // إنشاء إشعار جديد
    createNotification(data) {
        const notification = {
            id: this.generateId(),
            title: data.title,
            message: data.message,
            type: data.type || 'info', // info, success, warning, error
            priority: data.priority || 'normal', // low, normal, high, urgent
            timestamp: new Date(),
            read: false,
            actions: data.actions || [],
            persistent: data.persistent || false,
            sound: data.sound !== false,
            desktop: data.desktop !== false
        };

        this.notifications.unshift(notification);
        this.displayNotification(notification);
        this.saveToStorage();
        
        return notification;
    }

    // عرض الإشعار
    displayNotification(notification) {
        // إشعار في الصفحة
        this.showInPageNotification(notification);
        
        // إشعار سطح المكتب
        if (this.settings.desktop && notification.desktop) {
            this.showDesktopNotification(notification);
        }
        
        // تشغيل الصوت
        if (this.settings.sound && notification.sound) {
            this.playNotificationSound(notification.type);
        }
        
        // تحديث العداد
        this.updateNotificationCounter();
    }

    // إشعار داخل الصفحة
    showInPageNotification(notification) {
        const container = this.getOrCreateContainer();
        const element = this.createNotificationElement(notification);
        
        container.appendChild(element);
        
        // تأثير الظهور
        setTimeout(() => {
            element.classList.add('show');
        }, 100);
        
        // إخفاء تلقائي
        if (this.settings.autoHide && !notification.persistent) {
            setTimeout(() => {
                this.hideNotification(element);
            }, this.settings.hideDelay);
        }
    }

    // إنشاء عنصر الإشعار
    createNotificationElement(notification) {
        const element = document.createElement('div');
        element.className = `notification notification-${notification.type} notification-${notification.priority}`;
        element.dataset.id = notification.id;
        
        const icon = this.getNotificationIcon(notification.type);
        const timeAgo = this.formatTimeAgo(notification.timestamp);
        
        element.innerHTML = `
            <div class="notification-content">
                <div class="notification-header">
                    <div class="notification-icon">
                        <i class="fas ${icon}"></i>
                    </div>
                    <div class="notification-meta">
                        <h4 class="notification-title">${notification.title}</h4>
                        <span class="notification-time">${timeAgo}</span>
                    </div>
                    <button class="notification-close" onclick="notificationSystem.dismissNotification('${notification.id}')">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="notification-body">
                    <p class="notification-message">${notification.message}</p>
                    ${this.createNotificationActions(notification.actions)}
                </div>
            </div>
            <div class="notification-progress"></div>
        `;
        
        return element;
    }

    // إنشاء أزرار الإجراءات
    createNotificationActions(actions) {
        if (!actions || actions.length === 0) return '';
        
        return `
            <div class="notification-actions">
                ${actions.map(action => `
                    <button class="notification-action-btn" onclick="${action.callback}">
                        ${action.icon ? `<i class="fas ${action.icon}"></i>` : ''}
                        ${action.label}
                    </button>
                `).join('')}
            </div>
        `;
    }

    // إشعار سطح المكتب
    showDesktopNotification(notification) {
        if ('Notification' in window && Notification.permission === 'granted') {
            const desktopNotification = new Notification(notification.title, {
                body: notification.message,
                icon: '/static/images/notification-icon.png',
                badge: '/static/images/badge-icon.png',
                tag: notification.id,
                requireInteraction: notification.priority === 'urgent'
            });
            
            desktopNotification.onclick = () => {
                window.focus();
                this.markAsRead(notification.id);
                desktopNotification.close();
            };
            
            // إغلاق تلقائي
            setTimeout(() => {
                desktopNotification.close();
            }, 10000);
        }
    }

    // تشغيل صوت الإشعار
    playNotificationSound(type) {
        const sounds = {
            info: '/static/sounds/info.mp3',
            success: '/static/sounds/success.mp3',
            warning: '/static/sounds/warning.mp3',
            error: '/static/sounds/error.mp3'
        };
        
        const audio = new Audio(sounds[type] || sounds.info);
        audio.volume = 0.5;
        audio.play().catch(e => console.log('لا يمكن تشغيل الصوت:', e));
    }

    // إخفاء الإشعار
    hideNotification(element) {
        element.classList.add('hide');
        setTimeout(() => {
            if (element.parentNode) {
                element.parentNode.removeChild(element);
            }
        }, 300);
    }

    // رفض الإشعار
    dismissNotification(id) {
        const element = document.querySelector(`[data-id="${id}"]`);
        if (element) {
            this.hideNotification(element);
        }
        
        this.markAsRead(id);
    }

    // تحديد كمقروء
    markAsRead(id) {
        const notification = this.notifications.find(n => n.id === id);
        if (notification) {
            notification.read = true;
            this.saveToStorage();
            this.updateNotificationCounter();
        }
    }

    // تحديد الكل كمقروء
    markAllAsRead() {
        this.notifications.forEach(n => n.read = true);
        this.saveToStorage();
        this.updateNotificationCounter();
    }

    // حذف الإشعار
    deleteNotification(id) {
        this.notifications = this.notifications.filter(n => n.id !== id);
        this.saveToStorage();
        this.updateNotificationCounter();
    }

    // حذف جميع الإشعارات
    clearAllNotifications() {
        this.notifications = [];
        this.saveToStorage();
        this.updateNotificationCounter();
        
        // إزالة الإشعارات المعروضة
        const container = document.getElementById('notificationContainer');
        if (container) {
            container.innerHTML = '';
        }
    }

    // تحديث عداد الإشعارات
    updateNotificationCounter() {
        const unreadCount = this.notifications.filter(n => !n.read).length;
        const badge = document.getElementById('notificationBadge');
        
        if (badge) {
            if (unreadCount > 0) {
                badge.textContent = unreadCount > 99 ? '99+' : unreadCount;
                badge.classList.remove('hidden');
            } else {
                badge.classList.add('hidden');
            }
        }
        
        // تحديث العنوان
        if (unreadCount > 0) {
            document.title = `(${unreadCount}) نظام إدارة الاشتراكات`;
        } else {
            document.title = 'نظام إدارة الاشتراكات';
        }
    }

    // الحصول على أو إنشاء حاوي الإشعارات
    getOrCreateContainer() {
        let container = document.getElementById('notificationContainer');
        if (!container) {
            container = document.createElement('div');
            container.id = 'notificationContainer';
            container.className = 'notification-container';
            document.body.appendChild(container);
        }
        return container;
    }

    // توليد معرف فريد
    generateId() {
        return 'notif_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // الحصول على أيقونة الإشعار
    getNotificationIcon(type) {
        const icons = {
            info: 'fa-info-circle',
            success: 'fa-check-circle',
            warning: 'fa-exclamation-triangle',
            error: 'fa-times-circle'
        };
        return icons[type] || icons.info;
    }

    // تنسيق الوقت
    formatTimeAgo(timestamp) {
        const now = new Date();
        const diff = now - timestamp;
        const minutes = Math.floor(diff / 60000);
        
        if (minutes < 1) return 'الآن';
        if (minutes < 60) return `منذ ${minutes} دقيقة`;
        
        const hours = Math.floor(minutes / 60);
        if (hours < 24) return `منذ ${hours} ساعة`;
        
        const days = Math.floor(hours / 24);
        return `منذ ${days} يوم`;
    }

    // حفظ في التخزين المحلي
    saveToStorage() {
        localStorage.setItem('notifications', JSON.stringify(this.notifications));
        localStorage.setItem('notificationSettings', JSON.stringify(this.settings));
    }

    // تحميل من التخزين المحلي
    loadFromStorage() {
        const stored = localStorage.getItem('notifications');
        if (stored) {
            this.notifications = JSON.parse(stored);
            this.updateNotificationCounter();
        }
    }

    // تحميل الإعدادات
    loadSettings() {
        const stored = localStorage.getItem('notificationSettings');
        if (stored) {
            this.settings = { ...this.settings, ...JSON.parse(stored) };
        }
    }

    // تحديث الإعدادات
    updateSettings(newSettings) {
        this.settings = { ...this.settings, ...newSettings };
        this.saveToStorage();
    }

    // فحص دوري للإشعارات الجديدة
    startPeriodicCheck() {
        setInterval(() => {
            this.checkForNewNotifications();
        }, 30000); // كل 30 ثانية
    }

    // فحص الإشعارات الجديدة من الخادم
    async checkForNewNotifications() {
        try {
            const response = await fetch('/api/notifications/unread');
            const data = await response.json();
            
            if (data.notifications && data.notifications.length > 0) {
                data.notifications.forEach(notification => {
                    // تحقق من عدم وجود الإشعار مسبقاً
                    if (!this.notifications.find(n => n.id === notification.id)) {
                        this.createNotification(notification);
                    }
                });
            }
        } catch (error) {
            console.error('خطأ في جلب الإشعارات:', error);
        }
    }

    // إشعارات ذكية للاشتراكات
    checkSubscriptionAlerts() {
        // فحص الاشتراكات المنتهية قريباً
        // فحص الفواتير المتأخرة
        // فحص الأخطاء في النظام
        // إشعارات الأمان
    }
}

// إنشاء مثيل النظام
const notificationSystem = new AdvancedNotificationSystem();

// تصدير للاستخدام العام
window.notificationSystem = notificationSystem;

// أمثلة على الاستخدام
window.showNotification = (title, message, type = 'info', options = {}) => {
    return notificationSystem.createNotification({
        title,
        message,
        type,
        ...options
    });
};

// تحميل الإشعارات عند بدء التشغيل
document.addEventListener('DOMContentLoaded', () => {
    notificationSystem.loadFromStorage();
});
