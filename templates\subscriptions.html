{% extends "base.html" %}

{% block title %}إدارة الاشتراكات المتطورة - نظام إدارة الاشتراكات{% endblock %}
{% block page_title %}إدارة الاشتراكات المتطورة{% endblock %}
{% block page_description %}نظام شامل ومتطور لإدارة الاشتراكات مع تحكم كامل وميزات متقدمة{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/subscriptions-advanced.css') }}">
{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3 space-x-reverse">
    <!-- تبديل الوضع -->
    <button id="themeToggle" class="w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center text-white hover:bg-opacity-30 transition-all duration-300" onclick="toggleTheme()">
        <i class="fas fa-moon" id="themeIcon"></i>
    </button>
    
    <!-- تبديل العرض -->
    <div class="flex bg-white bg-opacity-20 rounded-xl p-1">
        <button id="gridView" class="w-8 h-8 rounded-lg flex items-center justify-center text-white transition-all duration-300" onclick="switchView('grid')">
            <i class="fas fa-th text-sm"></i>
        </button>
        <button id="listView" class="w-8 h-8 rounded-lg flex items-center justify-center text-white bg-white bg-opacity-30 transition-all duration-300" onclick="switchView('list')">
            <i class="fas fa-list text-sm"></i>
        </button>
        <button id="cardView" class="w-8 h-8 rounded-lg flex items-center justify-center text-white transition-all duration-300" onclick="switchView('card')">
            <i class="fas fa-th-large text-sm"></i>
        </button>
    </div>
    
    <!-- إضافة اشتراك -->
    <a href="{{ url_for('add_subscription') }}" class="btn-primary">
        <i class="fas fa-plus ml-2"></i>
        إضافة اشتراك
    </a>
</div>
{% endblock %}

{% block content %}
<!-- شريط التحكم العلوي -->
<div class="glass-card p-4 mb-6" id="controlPanel">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <!-- البحث والفلاتر -->
        <div class="flex flex-col sm:flex-row gap-3 flex-1">
            <div class="relative flex-1 max-w-md">
                <input type="text" id="searchInput" placeholder="البحث في الاشتراكات..." 
                       class="input-field w-full pl-10 pr-4 py-2 text-sm" onkeyup="filterSubscriptions()">
                <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-white text-opacity-50"></i>
            </div>
            
            <select id="statusFilter" class="input-field text-sm min-w-32" onchange="filterSubscriptions()">
                <option value="">جميع الحالات</option>
                <option value="active">نشط</option>
                <option value="suspended">معلق</option>
                <option value="expired">منتهي</option>
            </select>
            
            <select id="providerFilter" class="input-field text-sm min-w-32" onchange="filterSubscriptions()">
                <option value="">جميع المزودين</option>
                {% for provider in providers %}
                <option value="{{ provider.id }}">{{ provider.name }}</option>
                {% endfor %}
            </select>
            
            <select id="typeFilter" class="input-field text-sm min-w-32" onchange="filterSubscriptions()">
                <option value="">جميع الأنواع</option>
                <option value="monthly">شهري</option>
                <option value="semi_annual">نصف سنوي</option>
                <option value="annual">سنوي</option>
            </select>
        </div>
        
        <!-- أدوات التحكم -->
        <div class="flex items-center gap-2">
            <button class="btn-secondary text-sm" onclick="exportSubscriptions()">
                <i class="fas fa-download ml-1"></i>
                تصدير
            </button>
            <button class="btn-secondary text-sm" onclick="bulkActions()" id="bulkBtn" disabled>
                <i class="fas fa-tasks ml-1"></i>
                إجراءات (<span id="selectedCount">0</span>)
            </button>
            <button class="btn-secondary text-sm" onclick="refreshData()">
                <i class="fas fa-sync-alt ml-1"></i>
                تحديث
            </button>
        </div>
    </div>
    
    <!-- شريط الإجراءات المتعددة -->
    <div id="bulkActionsBar" class="hidden mt-4 p-3 bg-white bg-opacity-10 rounded-lg">
        <div class="flex items-center justify-between">
            <span class="text-white text-sm">تم تحديد <span id="selectedCountText">0</span> اشتراك</span>
            <div class="flex items-center gap-2">
                <button class="btn-secondary text-xs" onclick="bulkRenew()">
                    <i class="fas fa-redo ml-1"></i>
                    تجديد المحدد
                </button>
                <button class="btn-secondary text-xs" onclick="bulkSuspend()">
                    <i class="fas fa-pause ml-1"></i>
                    تعليق المحدد
                </button>
                <button class="btn-secondary text-xs" onclick="bulkDelete()">
                    <i class="fas fa-trash ml-1"></i>
                    حذف المحدد
                </button>
                <button class="btn-secondary text-xs" onclick="clearSelection()">
                    <i class="fas fa-times ml-1"></i>
                    إلغاء التحديد
                </button>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
    <div class="glass-card p-4 text-center hover-lift">
        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center mx-auto mb-2">
            <i class="fas fa-server text-white text-lg"></i>
        </div>
        <div class="text-2xl font-bold text-white" id="totalCount">{{ subscriptions|length }}</div>
        <div class="text-white text-opacity-70 text-sm">إجمالي الاشتراكات</div>
    </div>
    
    <div class="glass-card p-4 text-center hover-lift">
        <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center mx-auto mb-2">
            <i class="fas fa-check-circle text-white text-lg"></i>
        </div>
        <div class="text-2xl font-bold text-white" id="activeCount">{{ active_count }}</div>
        <div class="text-white text-opacity-70 text-sm">نشط</div>
    </div>
    
    <div class="glass-card p-4 text-center hover-lift">
        <div class="w-12 h-12 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-xl flex items-center justify-center mx-auto mb-2">
            <i class="fas fa-pause-circle text-white text-lg"></i>
        </div>
        <div class="text-2xl font-bold text-white" id="suspendedCount">{{ suspended_count }}</div>
        <div class="text-white text-opacity-70 text-sm">معلق</div>
    </div>
    
    <div class="glass-card p-4 text-center hover-lift">
        <div class="w-12 h-12 bg-gradient-to-r from-red-500 to-pink-500 rounded-xl flex items-center justify-center mx-auto mb-2">
            <i class="fas fa-times-circle text-white text-lg"></i>
        </div>
        <div class="text-2xl font-bold text-white" id="expiredCount">{{ expired_count }}</div>
        <div class="text-white text-opacity-70 text-sm">منتهي</div>
    </div>
</div>

<!-- منطقة عرض الاشتراكات -->
<div id="subscriptionsContainer">
    <!-- عرض القائمة (افتراضي) -->
    <div id="listViewContainer" class="glass-card overflow-hidden">
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-white bg-opacity-10">
                    <tr>
                        <th class="p-3 text-right">
                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll()" class="rounded">
                        </th>
                        <th class="p-3 text-right text-white text-sm font-medium cursor-pointer" onclick="sortTable('name')">
                            اسم الاشتراك <i class="fas fa-sort ml-1"></i>
                        </th>
                        <th class="p-3 text-right text-white text-sm font-medium cursor-pointer" onclick="sortTable('provider')">
                            المزود <i class="fas fa-sort ml-1"></i>
                        </th>
                        <th class="p-3 text-right text-white text-sm font-medium cursor-pointer" onclick="sortTable('type')">
                            النوع <i class="fas fa-sort ml-1"></i>
                        </th>
                        <th class="p-3 text-right text-white text-sm font-medium cursor-pointer" onclick="sortTable('price')">
                            السعر <i class="fas fa-sort ml-1"></i>
                        </th>
                        <th class="p-3 text-right text-white text-sm font-medium cursor-pointer" onclick="sortTable('end_date')">
                            تاريخ الانتهاء <i class="fas fa-sort ml-1"></i>
                        </th>
                        <th class="p-3 text-right text-white text-sm font-medium cursor-pointer" onclick="sortTable('status')">
                            الحالة <i class="fas fa-sort ml-1"></i>
                        </th>
                        <th class="p-3 text-right text-white text-sm font-medium">الإجراءات</th>
                    </tr>
                </thead>
                <tbody id="subscriptionsTableBody">
                    {% for subscription in subscriptions %}
                    <tr class="subscription-row border-b border-white border-opacity-10 hover:bg-white hover:bg-opacity-5 transition-all duration-300" 
                        data-id="{{ subscription.id }}" 
                        data-status="{{ subscription.status }}" 
                        data-provider="{{ subscription.provider.id if subscription.provider else '' }}"
                        data-type="{{ subscription.subscription_type }}"
                        data-name="{{ subscription.name|lower }}"
                        data-price="{{ subscription.price }}"
                        data-end-date="{{ subscription.end_date }}">
                        <td class="p-3">
                            <input type="checkbox" class="subscription-checkbox rounded" value="{{ subscription.id }}" onchange="updateSelection()">
                        </td>
                        <td class="p-3">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg flex items-center justify-center ml-3">
                                    <i class="fas fa-server text-white text-xs"></i>
                                </div>
                                <div>
                                    <div class="text-white font-medium text-sm">{{ subscription.name }}</div>
                                    <div class="text-white text-opacity-60 text-xs">{{ subscription.cloud_ip or 'غير محدد' }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="p-3">
                            <div class="flex items-center">
                                {% if subscription.provider %}
                                <img src="{{ subscription.provider.logo_url or '/static/images/default-provider.png' }}" 
                                     alt="{{ subscription.provider.name }}" class="w-6 h-6 rounded ml-2">
                                <span class="text-white text-sm">{{ subscription.provider.name }}</span>
                                {% else %}
                                <span class="text-white text-opacity-60 text-sm">غير محدد</span>
                                {% endif %}
                            </div>
                        </td>
                        <td class="p-3">
                            <span class="badge-primary text-xs">
                                {% if subscription.subscription_type == 'monthly' %}شهري
                                {% elif subscription.subscription_type == 'semi_annual' %}نصف سنوي
                                {% elif subscription.subscription_type == 'annual' %}سنوي
                                {% else %}{{ subscription.subscription_type }}{% endif %}
                            </span>
                        </td>
                        <td class="p-3">
                            <span class="text-white font-medium">${{ "%.2f"|format(subscription.price) }}</span>
                        </td>
                        <td class="p-3">
                            <div class="text-white text-sm">{{ subscription.end_date.strftime('%Y-%m-%d') if subscription.end_date else 'غير محدد' }}</div>
                            {% if subscription.end_date %}
                            <div class="text-xs text-white text-opacity-60">
                                {% set days_left = (subscription.end_date - today).days %}
                                {% if days_left < 0 %}
                                منتهي منذ {{ -days_left }} يوم
                                {% elif days_left == 0 %}
                                ينتهي اليوم
                                {% elif days_left <= 7 %}
                                ينتهي خلال {{ days_left }} أيام
                                {% else %}
                                باقي {{ days_left }} يوم
                                {% endif %}
                            </div>
                            {% endif %}
                        </td>
                        <td class="p-3">
                            {% if subscription.status == 'active' %}
                            <span class="badge-success">نشط</span>
                            {% elif subscription.status == 'suspended' %}
                            <span class="badge-warning">معلق</span>
                            {% elif subscription.status == 'expired' %}
                            <span class="badge-danger">منتهي</span>
                            {% else %}
                            <span class="badge-primary">{{ subscription.status }}</span>
                            {% endif %}
                        </td>
                        <td class="p-3">
                            <div class="flex items-center gap-1">
                                <!-- تجديد سريع -->
                                <button class="w-8 h-8 bg-green-500 bg-opacity-20 rounded-lg flex items-center justify-center text-green-400 hover:bg-opacity-30 transition-all duration-300" 
                                        onclick="quickRenew({{ subscription.id }})" title="تجديد سريع">
                                    <i class="fas fa-redo text-xs"></i>
                                </button>
                                
                                <!-- تعديل -->
                                <a href="{{ url_for('edit_subscription', id=subscription.id) }}" 
                                   class="w-8 h-8 bg-blue-500 bg-opacity-20 rounded-lg flex items-center justify-center text-blue-400 hover:bg-opacity-30 transition-all duration-300" 
                                   title="تعديل">
                                    <i class="fas fa-edit text-xs"></i>
                                </a>
                                
                                <!-- عرض التفاصيل -->
                                <button class="w-8 h-8 bg-purple-500 bg-opacity-20 rounded-lg flex items-center justify-center text-purple-400 hover:bg-opacity-30 transition-all duration-300" 
                                        onclick="viewDetails({{ subscription.id }})" title="عرض التفاصيل">
                                    <i class="fas fa-eye text-xs"></i>
                                </button>
                                
                                <!-- حذف -->
                                <button class="w-8 h-8 bg-red-500 bg-opacity-20 rounded-lg flex items-center justify-center text-red-400 hover:bg-opacity-30 transition-all duration-300" 
                                        onclick="deleteSubscription({{ subscription.id }})" title="حذف">
                                    <i class="fas fa-trash text-xs"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    
    <!-- عرض الشبكة -->
    <div id="gridViewContainer" class="hidden grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
        <!-- سيتم ملؤها بـ JavaScript -->
    </div>
    
    <!-- عرض الكروت -->
    <div id="cardViewContainer" class="hidden grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- سيتم ملؤها بـ JavaScript -->
    </div>
</div>

<!-- شريط التنقل السفلي -->
<div class="fixed bottom-4 left-1/2 transform -translate-x-1/2 z-50" id="bottomNavigation">
    <div class="glass-card px-6 py-3 rounded-full">
        <div class="flex items-center gap-4">
            <button class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-white hover:bg-opacity-30 transition-all duration-300" 
                    onclick="navigatePage('prev')" id="prevBtn">
                <i class="fas fa-chevron-right"></i>
            </button>
            
            <div class="flex items-center gap-2" id="pageNumbers">
                <!-- سيتم ملؤها بـ JavaScript -->
            </div>
            
            <button class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center text-white hover:bg-opacity-30 transition-all duration-300" 
                    onclick="navigatePage('next')" id="nextBtn">
                <i class="fas fa-chevron-left"></i>
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// متغيرات عامة
let currentView = 'list';
let currentTheme = 'dark';
let selectedSubscriptions = new Set();
let currentPage = 1;
let itemsPerPage = 10;
let sortColumn = '';
let sortDirection = 'asc';
let subscriptionsData = {{ subscriptions|tojson }};

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    setupEventListeners();
    updatePagination();
});

// تهيئة الصفحة
function initializePage() {
    // تطبيق الثيم المحفوظ
    const savedTheme = localStorage.getItem('subscriptionsTheme') || 'dark';
    applyTheme(savedTheme);

    // تطبيق العرض المحفوظ
    const savedView = localStorage.getItem('subscriptionsView') || 'list';
    switchView(savedView);

    // تحديث العدادات
    updateCounters();
}

// إعداد مستمعي الأحداث
function setupEventListeners() {
    // البحث المباشر
    document.getElementById('searchInput').addEventListener('input', debounce(filterSubscriptions, 300));

    // تغيير حجم الصفحة
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey) {
            if (e.key === '+' || e.key === '=') {
                e.preventDefault();
                changeItemsPerPage(itemsPerPage + 5);
            } else if (e.key === '-') {
                e.preventDefault();
                changeItemsPerPage(Math.max(5, itemsPerPage - 5));
            }
        }
    });
}

// تبديل الثيم
function toggleTheme() {
    currentTheme = currentTheme === 'dark' ? 'light' : 'dark';
    applyTheme(currentTheme);
    localStorage.setItem('subscriptionsTheme', currentTheme);
}

// تطبيق الثيم
function applyTheme(theme) {
    const root = document.documentElement;
    const themeIcon = document.getElementById('themeIcon');

    if (theme === 'light') {
        root.style.setProperty('--glass-bg', 'rgba(255, 255, 255, 0.9)');
        root.style.setProperty('--text-primary', '#1f2937');
        root.style.setProperty('--text-secondary', '#6b7280');
        themeIcon.className = 'fas fa-sun';
    } else {
        root.style.setProperty('--glass-bg', 'rgba(255, 255, 255, 0.1)');
        root.style.setProperty('--text-primary', '#ffffff');
        root.style.setProperty('--text-secondary', 'rgba(255, 255, 255, 0.7)');
        themeIcon.className = 'fas fa-moon';
    }

    currentTheme = theme;
}

// تبديل العرض
function switchView(view) {
    // إخفاء جميع العروض
    document.getElementById('listViewContainer').classList.add('hidden');
    document.getElementById('gridViewContainer').classList.add('hidden');
    document.getElementById('cardViewContainer').classList.add('hidden');

    // إزالة الحالة النشطة من جميع الأزرار
    document.querySelectorAll('#gridView, #listView, #cardView').forEach(btn => {
        btn.classList.remove('bg-white', 'bg-opacity-30');
    });

    // عرض العرض المحدد
    if (view === 'grid') {
        document.getElementById('gridViewContainer').classList.remove('hidden');
        document.getElementById('gridView').classList.add('bg-white', 'bg-opacity-30');
        renderGridView();
    } else if (view === 'card') {
        document.getElementById('cardViewContainer').classList.remove('hidden');
        document.getElementById('cardView').classList.add('bg-white', 'bg-opacity-30');
        renderCardView();
    } else {
        document.getElementById('listViewContainer').classList.remove('hidden');
        document.getElementById('listView').classList.add('bg-white', 'bg-opacity-30');
    }

    currentView = view;
    localStorage.setItem('subscriptionsView', view);
}

// عرض الشبكة
function renderGridView() {
    const container = document.getElementById('gridViewContainer');
    const filteredData = getFilteredData();

    container.innerHTML = filteredData.map(subscription => `
        <div class="glass-card p-4 hover-lift subscription-card" data-id="${subscription.id}">
            <div class="flex items-center justify-between mb-3">
                <input type="checkbox" class="subscription-checkbox rounded" value="${subscription.id}" onchange="updateSelection()">
                <div class="flex items-center gap-1">
                    <button class="w-6 h-6 bg-green-500 bg-opacity-20 rounded flex items-center justify-center text-green-400 hover:bg-opacity-30"
                            onclick="quickRenew(${subscription.id})" title="تجديد سريع">
                        <i class="fas fa-redo text-xs"></i>
                    </button>
                    <button class="w-6 h-6 bg-blue-500 bg-opacity-20 rounded flex items-center justify-center text-blue-400 hover:bg-opacity-30"
                            onclick="editSubscription(${subscription.id})" title="تعديل">
                        <i class="fas fa-edit text-xs"></i>
                    </button>
                </div>
            </div>

            <div class="text-center mb-3">
                <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center mx-auto mb-2">
                    <i class="fas fa-server text-white"></i>
                </div>
                <h3 class="text-white font-medium text-sm">${subscription.name}</h3>
                <p class="text-white text-opacity-60 text-xs">${subscription.provider?.name || 'غير محدد'}</p>
            </div>

            <div class="space-y-2 text-xs">
                <div class="flex justify-between">
                    <span class="text-white text-opacity-70">السعر:</span>
                    <span class="text-white font-medium">$${subscription.price}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-white text-opacity-70">النوع:</span>
                    <span class="badge-primary text-xs">${getTypeLabel(subscription.subscription_type)}</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-white text-opacity-70">الحالة:</span>
                    <span class="badge-${getStatusClass(subscription.status)} text-xs">${getStatusLabel(subscription.status)}</span>
                </div>
            </div>
        </div>
    `).join('');
}

// عرض الكروت
function renderCardView() {
    const container = document.getElementById('cardViewContainer');
    const filteredData = getFilteredData();

    container.innerHTML = filteredData.map(subscription => `
        <div class="glass-card p-6 hover-lift subscription-card" data-id="${subscription.id}">
            <div class="flex items-start justify-between mb-4">
                <div class="flex items-center">
                    <input type="checkbox" class="subscription-checkbox rounded ml-3" value="${subscription.id}" onchange="updateSelection()">
                    <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center">
                        <i class="fas fa-server text-white text-xl"></i>
                    </div>
                    <div class="mr-4">
                        <h3 class="text-white font-bold text-lg">${subscription.name}</h3>
                        <p class="text-white text-opacity-70">${subscription.provider?.name || 'غير محدد'}</p>
                        <p class="text-white text-opacity-50 text-sm">${subscription.cloud_ip || 'غير محدد'}</p>
                    </div>
                </div>
                <span class="badge-${getStatusClass(subscription.status)}">${getStatusLabel(subscription.status)}</span>
            </div>

            <div class="grid grid-cols-2 gap-4 mb-4">
                <div class="text-center p-3 bg-white bg-opacity-10 rounded-lg">
                    <div class="text-white text-opacity-70 text-sm">السعر</div>
                    <div class="text-white font-bold text-lg">$${subscription.price}</div>
                </div>
                <div class="text-center p-3 bg-white bg-opacity-10 rounded-lg">
                    <div class="text-white text-opacity-70 text-sm">النوع</div>
                    <div class="text-white font-medium">${getTypeLabel(subscription.subscription_type)}</div>
                </div>
            </div>

            <div class="flex items-center justify-between">
                <div class="text-sm">
                    <div class="text-white text-opacity-70">ينتهي في:</div>
                    <div class="text-white">${formatDate(subscription.end_date)}</div>
                </div>
                <div class="flex items-center gap-2">
                    <button class="btn-secondary text-xs" onclick="quickRenew(${subscription.id})">
                        <i class="fas fa-redo ml-1"></i>
                        تجديد
                    </button>
                    <button class="btn-secondary text-xs" onclick="editSubscription(${subscription.id})">
                        <i class="fas fa-edit ml-1"></i>
                        تعديل
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

// فلترة الاشتراكات
function filterSubscriptions() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    const providerFilter = document.getElementById('providerFilter').value;
    const typeFilter = document.getElementById('typeFilter').value;

    const rows = document.querySelectorAll('.subscription-row');
    let visibleCount = 0;

    rows.forEach(row => {
        const name = row.dataset.name;
        const status = row.dataset.status;
        const provider = row.dataset.provider;
        const type = row.dataset.type;

        const matchesSearch = !searchTerm || name.includes(searchTerm);
        const matchesStatus = !statusFilter || status === statusFilter;
        const matchesProvider = !providerFilter || provider === providerFilter;
        const matchesType = !typeFilter || type === typeFilter;

        if (matchesSearch && matchesStatus && matchesProvider && matchesType) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });

    // تحديث العرض الحالي
    if (currentView !== 'list') {
        if (currentView === 'grid') {
            renderGridView();
        } else if (currentView === 'card') {
            renderCardView();
        }
    }

    updatePagination();
}

// الحصول على البيانات المفلترة
function getFilteredData() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    const providerFilter = document.getElementById('providerFilter').value;
    const typeFilter = document.getElementById('typeFilter').value;

    return subscriptionsData.filter(subscription => {
        const matchesSearch = !searchTerm || subscription.name.toLowerCase().includes(searchTerm);
        const matchesStatus = !statusFilter || subscription.status === statusFilter;
        const matchesProvider = !providerFilter || (subscription.provider && subscription.provider.id.toString() === providerFilter);
        const matchesType = !typeFilter || subscription.subscription_type === typeFilter;

        return matchesSearch && matchesStatus && matchesProvider && matchesType;
    });
}

// ترتيب الجدول
function sortTable(column) {
    if (sortColumn === column) {
        sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
    } else {
        sortColumn = column;
        sortDirection = 'asc';
    }

    const tbody = document.getElementById('subscriptionsTableBody');
    const rows = Array.from(tbody.querySelectorAll('tr'));

    rows.sort((a, b) => {
        let aVal, bVal;

        switch(column) {
            case 'name':
                aVal = a.dataset.name;
                bVal = b.dataset.name;
                break;
            case 'price':
                aVal = parseFloat(a.dataset.price);
                bVal = parseFloat(b.dataset.price);
                break;
            case 'end_date':
                aVal = new Date(a.dataset.endDate);
                bVal = new Date(b.dataset.endDate);
                break;
            default:
                aVal = a.dataset[column] || '';
                bVal = b.dataset[column] || '';
        }

        if (sortDirection === 'asc') {
            return aVal > bVal ? 1 : -1;
        } else {
            return aVal < bVal ? 1 : -1;
        }
    });

    rows.forEach(row => tbody.appendChild(row));

    // تحديث أيقونات الترتيب
    document.querySelectorAll('th i.fas').forEach(icon => {
        icon.className = 'fas fa-sort ml-1';
    });

    const currentHeader = document.querySelector(`th[onclick="sortTable('${column}')"] i`);
    if (currentHeader) {
        currentHeader.className = `fas fa-sort-${sortDirection === 'asc' ? 'up' : 'down'} ml-1`;
    }
}

// تحديث التحديد
function updateSelection() {
    const checkboxes = document.querySelectorAll('.subscription-checkbox:checked');
    selectedSubscriptions.clear();

    checkboxes.forEach(cb => {
        selectedSubscriptions.add(parseInt(cb.value));
    });

    const count = selectedSubscriptions.size;
    document.getElementById('selectedCount').textContent = count;
    document.getElementById('selectedCountText').textContent = count;
    document.getElementById('bulkBtn').disabled = count === 0;

    if (count > 0) {
        document.getElementById('bulkActionsBar').classList.remove('hidden');
    } else {
        document.getElementById('bulkActionsBar').classList.add('hidden');
    }
}

// تحديد الكل
function toggleSelectAll() {
    const selectAll = document.getElementById('selectAll');
    const checkboxes = document.querySelectorAll('.subscription-checkbox');

    checkboxes.forEach(cb => {
        cb.checked = selectAll.checked;
    });

    updateSelection();
}

// مسح التحديد
function clearSelection() {
    document.querySelectorAll('.subscription-checkbox').forEach(cb => {
        cb.checked = false;
    });
    document.getElementById('selectAll').checked = false;
    updateSelection();
}

// تجديد سريع
function quickRenew(subscriptionId) {
    if (confirm('هل تريد تجديد هذا الاشتراك؟')) {
        showLoading();

        fetch(`/api/subscriptions/${subscriptionId}/renew`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showNotification('تم تجديد الاشتراك بنجاح', 'success');
                refreshData();
            } else {
                showNotification('فشل في تجديد الاشتراك', 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showNotification('حدث خطأ أثناء التجديد', 'error');
        });
    }
}

// تجديد متعدد
function bulkRenew() {
    if (selectedSubscriptions.size === 0) return;

    if (confirm(`هل تريد تجديد ${selectedSubscriptions.size} اشتراك؟`)) {
        showLoading();

        fetch('/api/subscriptions/bulk-renew', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                subscription_ids: Array.from(selectedSubscriptions)
            })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showNotification(`تم تجديد ${data.renewed_count} اشتراك بنجاح`, 'success');
                clearSelection();
                refreshData();
            } else {
                showNotification('فشل في تجديد الاشتراكات', 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showNotification('حدث خطأ أثناء التجديد', 'error');
        });
    }
}

// تعليق متعدد
function bulkSuspend() {
    if (selectedSubscriptions.size === 0) return;

    if (confirm(`هل تريد تعليق ${selectedSubscriptions.size} اشتراك؟`)) {
        showLoading();

        fetch('/api/subscriptions/bulk-suspend', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                subscription_ids: Array.from(selectedSubscriptions)
            })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showNotification(`تم تعليق ${data.suspended_count} اشتراك بنجاح`, 'success');
                clearSelection();
                refreshData();
            } else {
                showNotification('فشل في تعليق الاشتراكات', 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showNotification('حدث خطأ أثناء التعليق', 'error');
        });
    }
}

// حذف متعدد
function bulkDelete() {
    if (selectedSubscriptions.size === 0) return;

    if (confirm(`هل تريد حذف ${selectedSubscriptions.size} اشتراك نهائياً؟ هذا الإجراء لا يمكن التراجع عنه.`)) {
        showLoading();

        fetch('/api/subscriptions/bulk-delete', {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                subscription_ids: Array.from(selectedSubscriptions)
            })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showNotification(`تم حذف ${data.deleted_count} اشتراك بنجاح`, 'success');
                clearSelection();
                refreshData();
            } else {
                showNotification('فشل في حذف الاشتراكات', 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showNotification('حدث خطأ أثناء الحذف', 'error');
        });
    }
}

// حذف اشتراك واحد
function deleteSubscription(subscriptionId) {
    if (confirm('هل تريد حذف هذا الاشتراك نهائياً؟')) {
        showLoading();

        fetch(`/api/subscriptions/${subscriptionId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            if (data.success) {
                showNotification('تم حذف الاشتراك بنجاح', 'success');
                refreshData();
            } else {
                showNotification('فشل في حذف الاشتراك', 'error');
            }
        })
        .catch(error => {
            hideLoading();
            showNotification('حدث خطأ أثناء الحذف', 'error');
        });
    }
}

// عرض التفاصيل
function viewDetails(subscriptionId) {
    // يمكن فتح modal أو الانتقال لصفحة التفاصيل
    window.location.href = `/subscription/${subscriptionId}`;
}

// تعديل الاشتراك
function editSubscription(subscriptionId) {
    window.location.href = `/edit_subscription/${subscriptionId}`;
}

// تصدير البيانات
function exportSubscriptions() {
    const format = prompt('اختر صيغة التصدير:\n1. PDF\n2. Excel\n3. CSV', '1');

    let url;
    switch(format) {
        case '1':
            url = '/export_subscriptions_pdf';
            break;
        case '2':
            url = '/export_subscriptions_excel';
            break;
        case '3':
            url = '/export_subscriptions_csv';
            break;
        default:
            return;
    }

    window.open(url, '_blank');
}

// تحديث البيانات
function refreshData() {
    showLoading();
    location.reload();
}

// تحديث العدادات
function updateCounters() {
    const rows = document.querySelectorAll('.subscription-row');
    let active = 0, suspended = 0, expired = 0;

    rows.forEach(row => {
        const status = row.dataset.status;
        if (status === 'active') active++;
        else if (status === 'suspended') suspended++;
        else if (status === 'expired') expired++;
    });

    document.getElementById('totalCount').textContent = rows.length;
    document.getElementById('activeCount').textContent = active;
    document.getElementById('suspendedCount').textContent = suspended;
    document.getElementById('expiredCount').textContent = expired;
}

// التنقل بين الصفحات
function navigatePage(direction) {
    if (direction === 'next') {
        currentPage++;
    } else if (direction === 'prev' && currentPage > 1) {
        currentPage--;
    }
    updatePagination();
}

// تحديث التنقل
function updatePagination() {
    const totalItems = document.querySelectorAll('.subscription-row:not([style*="display: none"])').length;
    const totalPages = Math.ceil(totalItems / itemsPerPage);

    // تحديث أزرار التنقل
    document.getElementById('prevBtn').disabled = currentPage === 1;
    document.getElementById('nextBtn').disabled = currentPage === totalPages;

    // تحديث أرقام الصفحات
    const pageNumbers = document.getElementById('pageNumbers');
    pageNumbers.innerHTML = '';

    for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
        const pageBtn = document.createElement('button');
        pageBtn.className = `w-8 h-8 rounded-full flex items-center justify-center text-white text-sm transition-all duration-300 ${
            i === currentPage ? 'bg-white bg-opacity-30' : 'hover:bg-white hover:bg-opacity-20'
        }`;
        pageBtn.textContent = i;
        pageBtn.onclick = () => {
            currentPage = i;
            updatePagination();
        };
        pageNumbers.appendChild(pageBtn);
    }

    // إخفاء/إظهار الصفوف حسب الصفحة الحالية
    const rows = document.querySelectorAll('.subscription-row:not([style*="display: none"])');
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;

    rows.forEach((row, index) => {
        if (index >= startIndex && index < endIndex) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// تغيير عدد العناصر في الصفحة
function changeItemsPerPage(newSize) {
    itemsPerPage = newSize;
    currentPage = 1;
    updatePagination();
    showNotification(`تم تغيير عدد العناصر إلى ${newSize} في الصفحة`, 'info');
}

// دوال مساعدة
function getStatusLabel(status) {
    const labels = {
        'active': 'نشط',
        'suspended': 'معلق',
        'expired': 'منتهي'
    };
    return labels[status] || status;
}

function getStatusClass(status) {
    const classes = {
        'active': 'success',
        'suspended': 'warning',
        'expired': 'danger'
    };
    return classes[status] || 'primary';
}

function getTypeLabel(type) {
    const labels = {
        'monthly': 'شهري',
        'semi_annual': 'نصف سنوي',
        'annual': 'سنوي'
    };
    return labels[type] || type;
}

function formatDate(dateString) {
    if (!dateString) return 'غير محدد';
    const date = new Date(dateString);
    return date.toLocaleDateString('ar-SA');
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function showLoading() {
    // إضافة مؤشر التحميل
    const loader = document.createElement('div');
    loader.id = 'pageLoader';
    loader.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
    loader.innerHTML = '<div class="spinner"></div>';
    document.body.appendChild(loader);
}

function hideLoading() {
    const loader = document.getElementById('pageLoader');
    if (loader) {
        loader.remove();
    }
}

function showNotification(message, type) {
    if (window.notificationSystem) {
        window.notificationSystem.createNotification({
            title: 'إدارة الاشتراكات',
            message: message,
            type: type
        });
    }
}
</script>

<style>
/* أنماط إضافية للصفحة */
.subscription-row {
    transition: all 0.3s ease;
}

.subscription-row:hover {
    transform: translateX(5px);
}

.subscription-card {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.subscription-card:hover {
    transform: translateY(-5px) scale(1.02);
}

.hover-lift:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

#bottomNavigation {
    animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translate(-50%, 100%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, 0);
    }
}

/* تحسينات للجوال */
@media (max-width: 768px) {
    .glass-card {
        margin: 0 10px;
    }

    #bottomNavigation {
        bottom: 10px;
    }

    .subscription-card {
        padding: 1rem;
    }

    .input-field {
        font-size: 16px; /* منع التكبير في iOS */
    }
}

/* تأثيرات الانيميشن */
.animate-in {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
{% endblock %}
