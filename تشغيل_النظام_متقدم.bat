@echo off
chcp 65001 >nul
title نظام إدارة الاشتراكات المتطور - تشغيل متقدم
color 0B

echo.
echo ===============================================================================
echo                🚀 نظام إدارة الاشتراكات المتطور - التشغيل المتقدم 🚀
echo ===============================================================================
echo.
echo 👨‍💻 المطور: المهندس محمد ياسر الجبوري
echo 📅 التاريخ: 2025-07-21
echo 🎯 النسخة: النهائية المتطورة مع التشغيل التلقائي
echo.

:: التحقق من Python
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت
    pause
    exit /b 1
)

:: الانتقال للمجلد
cd /d "D:\vps cloud mohammed"

:: التحقق من الملف
if not exist "app.py" (
    echo ❌ خطأ: ملف النظام غير موجود
    pause
    exit /b 1
)

echo ✅ جميع المتطلبات متوفرة
echo.
echo 🔄 بدء تشغيل النظام...
echo ⏳ سيتم فتح المتصفح تلقائياً خلال 10 ثوانٍ...
echo.

:: تشغيل النظام في الخلفية
start /b python app.py

:: انتظار 10 ثوانٍ لتشغيل النظام
timeout /t 10 /nobreak >nul

:: فتح المتصفح تلقائياً
echo 🌐 فتح المتصفح تلقائياً...
start http://localhost:9090

:: فتح صفحات إضافية (اختياري)
timeout /t 2 /nobreak >nul
start http://localhost:9090/dashboard
timeout /t 1 /nobreak >nul
start http://localhost:9090/subscriptions
timeout /t 1 /nobreak >nul
start http://localhost:9090/ui-controller

echo.
echo ===============================================================================
echo                           ✅ تم تشغيل النظام بنجاح!
echo ===============================================================================
echo.
echo 🌐 الرابط: http://localhost:9090
echo 👤 المستخدم: admin
echo 🔑 كلمة المرور: 123456
echo.
echo 📋 تم فتح الصفحات التالية:
echo    • الصفحة الرئيسية
echo    • لوحة التحكم
echo    • إدارة الاشتراكات
echo    • تحكم الواجهات
echo.
echo ⚠️ لإيقاف النظام: اضغط Ctrl+C أو أغلق هذه النافذة
echo.

:: إبقاء النافذة مفتوحة
cmd /k
