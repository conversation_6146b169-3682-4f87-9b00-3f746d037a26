/* 
 * أنماط نظام الإشعارات المتقدم
 * تطوير: المهندس محمد ياسر الجبوري
 * نظام إدارة الاشتراكات المتطور
 */

/* حاوي الإشعارات */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
    width: 100%;
    pointer-events: none;
}

/* الإشعار الأساسي */
.notification {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 15px;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    pointer-events: auto;
    overflow: hidden;
    position: relative;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}

.notification.show {
    opacity: 1;
    transform: translateX(0);
}

.notification.hide {
    opacity: 0;
    transform: translateX(100%) scale(0.8);
}

/* أنواع الإشعارات */
.notification-info {
    border-left: 4px solid #3b82f6;
}

.notification-success {
    border-left: 4px solid #10b981;
}

.notification-warning {
    border-left: 4px solid #f59e0b;
}

.notification-error {
    border-left: 4px solid #ef4444;
}

/* أولوية الإشعارات */
.notification-urgent {
    animation: urgentPulse 1s infinite;
    border-width: 2px;
}

.notification-high {
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

@keyframes urgentPulse {
    0%, 100% {
        box-shadow: 0 0 20px rgba(239, 68, 68, 0.5);
    }
    50% {
        box-shadow: 0 0 30px rgba(239, 68, 68, 0.8);
    }
}

/* محتوى الإشعار */
.notification-content {
    padding: 16px;
    color: white;
}

.notification-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: 8px;
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 12px;
    flex-shrink: 0;
}

.notification-info .notification-icon {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.notification-success .notification-icon {
    background: linear-gradient(135deg, #10b981, #059669);
}

.notification-warning .notification-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.notification-error .notification-icon {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.notification-meta {
    flex: 1;
}

.notification-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 4px 0;
    color: white;
}

.notification-time {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
}

.notification-close {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.7);
    cursor: pointer;
    transition: all 0.3s ease;
}

.notification-close:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    transform: scale(1.1);
}

.notification-body {
    margin-top: 8px;
}

.notification-message {
    font-size: 14px;
    line-height: 1.5;
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
}

/* أزرار الإجراءات */
.notification-actions {
    display: flex;
    gap: 8px;
    margin-top: 12px;
}

.notification-action-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 6px 12px;
    font-size: 12px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 4px;
}

.notification-action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

/* شريط التقدم */
.notification-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 0 0 15px 15px;
    animation: progressBar 5s linear forwards;
}

@keyframes progressBar {
    from { width: 100%; }
    to { width: 0%; }
}

.notification-persistent .notification-progress {
    display: none;
}

/* شارة العداد */
.notification-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background: linear-gradient(135deg, #ef4444, #dc2626);
    color: white;
    border-radius: 50%;
    min-width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 11px;
    font-weight: bold;
    animation: badgePulse 2s infinite;
}

@keyframes badgePulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.2);
    }
}

/* قائمة الإشعارات */
.notification-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 350px;
    max-height: 400px;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    overflow: hidden;
    z-index: 1000;
}

.notification-dropdown-header {
    padding: 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: between;
    align-items: center;
}

.notification-dropdown-title {
    font-size: 16px;
    font-weight: 600;
    color: white;
    margin: 0;
}

.notification-dropdown-actions {
    display: flex;
    gap: 8px;
}

.notification-dropdown-btn {
    background: rgba(255, 255, 255, 0.1);
    border: none;
    border-radius: 6px;
    padding: 4px 8px;
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    cursor: pointer;
    transition: all 0.3s ease;
}

.notification-dropdown-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: white;
}

.notification-list {
    max-height: 300px;
    overflow-y: auto;
}

.notification-item {
    padding: 12px 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    cursor: pointer;
    transition: all 0.3s ease;
}

.notification-item:hover {
    background: rgba(255, 255, 255, 0.05);
}

.notification-item.unread {
    background: rgba(59, 130, 246, 0.1);
    border-right: 3px solid #3b82f6;
}

.notification-item-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 4px;
}

.notification-item-title {
    font-size: 14px;
    font-weight: 500;
    color: white;
    margin: 0;
}

.notification-item-time {
    font-size: 11px;
    color: rgba(255, 255, 255, 0.6);
}

.notification-item-message {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.4;
    margin: 0;
}

/* حالة فارغة */
.notification-empty {
    padding: 40px 20px;
    text-align: center;
    color: rgba(255, 255, 255, 0.6);
}

.notification-empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.notification-empty-text {
    font-size: 14px;
    margin: 0;
}

/* تأثيرات الهاتف المحمول */
@media (max-width: 768px) {
    .notification-container {
        right: 10px;
        left: 10px;
        max-width: none;
    }
    
    .notification {
        margin-bottom: 10px;
    }
    
    .notification-dropdown {
        width: 100%;
        right: 0;
        left: 0;
    }
}

/* تأثيرات إضافية */
.notification-glow {
    box-shadow: 0 0 30px rgba(102, 126, 234, 0.5);
}

.notification-shake {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

/* تأثير التمرير */
.notification-list::-webkit-scrollbar {
    width: 6px;
}

.notification-list::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.notification-list::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.notification-list::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}
