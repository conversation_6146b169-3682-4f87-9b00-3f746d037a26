# نظام إدارة الاشتراكات المتطور - PowerShell
# المطور: المهندس محمد ياسر الجبوري

# إعداد النافذة
$Host.UI.RawUI.WindowTitle = "نظام إدارة الاشتراكات المتطور - PowerShell"
Clear-Host

# عرض معلومات النظام
Write-Host "===============================================================================" -ForegroundColor Cyan
Write-Host "                🚀 نظام إدارة الاشتراكات المتطور 🚀" -ForegroundColor Yellow
Write-Host "===============================================================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "👨‍💻 المطور: المهندس محمد ياسر الجبوري" -ForegroundColor Green
Write-Host "📅 التاريخ: 2025-07-21" -ForegroundColor Green
Write-Host "🎯 النسخة: النهائية المتطورة" -ForegroundColor Green
Write-Host ""

# التحقق من Python
Write-Host "🔍 التحقق من Python..." -ForegroundColor Yellow
try {
    $pythonVersion = python --version 2>&1
    Write-Host "✅ تم العثور على Python: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ خطأ: Python غير مثبت على النظام" -ForegroundColor Red
    Write-Host "📥 يرجى تثبيت Python من: https://python.org" -ForegroundColor Yellow
    Read-Host "اضغط Enter للخروج"
    exit
}

# الانتقال لمجلد النظام
Write-Host "📁 الانتقال لمجلد النظام..." -ForegroundColor Yellow
Set-Location "D:\vps cloud mohammed"

# التحقق من وجود ملف النظام
if (-not (Test-Path "app.py")) {
    Write-Host "❌ خطأ: ملف النظام app.py غير موجود" -ForegroundColor Red
    Write-Host "📍 المسار الحالي: $(Get-Location)" -ForegroundColor Yellow
    Read-Host "اضغط Enter للخروج"
    exit
}

Write-Host "✅ تم العثور على ملف النظام" -ForegroundColor Green
Write-Host ""

# عرض معلومات النظام
Write-Host "===============================================================================" -ForegroundColor Cyan
Write-Host "                           📋 معلومات النظام" -ForegroundColor Yellow
Write-Host "===============================================================================" -ForegroundColor Cyan
Write-Host "🌐 الرابط الرئيسي: http://localhost:9090" -ForegroundColor White
Write-Host "👤 اسم المستخدم: admin" -ForegroundColor White
Write-Host "🔑 كلمة المرور: 123456" -ForegroundColor White
Write-Host "📍 المسار: $(Get-Location)" -ForegroundColor White
Write-Host ""

# عرض الصفحات المتاحة
Write-Host "===============================================================================" -ForegroundColor Cyan
Write-Host "                           🌐 الصفحات المتاحة" -ForegroundColor Yellow
Write-Host "===============================================================================" -ForegroundColor Cyan

$pages = @(
    "🏠 الصفحة الرئيسية: http://localhost:9090",
    "📊 لوحة التحكم: http://localhost:9090/dashboard",
    "📈 لوحة التحكم المتقدمة: http://localhost:9090/advanced-dashboard",
    "📋 إدارة الاشتراكات: http://localhost:9090/subscriptions",
    "👥 إدارة المستخدمين: http://localhost:9090/advanced-users",
    "🏪 إدارة الموزعين: http://localhost:9090/distributors-management",
    "💳 أنظمة الدفع: http://localhost:9090/payment-gateways",
    "📊 التقارير المتقدمة: http://localhost:9090/advanced-reports",
    "🔍 مراقبة النظام: http://localhost:9090/system-monitor",
    "🎨 إعدادات الألوان: http://localhost:9090/theme-settings",
    "🧭 إعدادات التنقل: http://localhost:9090/navigation-settings",
    "🎛️ تحكم الواجهات: http://localhost:9090/ui-controller",
    "🔒 مركز الحماية: http://localhost:9090/security",
    "💰 إدارة الفواتير: http://localhost:9090/invoices"
)

foreach ($page in $pages) {
    Write-Host $page -ForegroundColor White
}

Write-Host ""

# بدء تشغيل النظام
Write-Host "===============================================================================" -ForegroundColor Cyan
Write-Host "                           🚀 بدء تشغيل النظام..." -ForegroundColor Yellow
Write-Host "===============================================================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "⏳ جاري تشغيل النظام... يرجى الانتظار" -ForegroundColor Yellow
Write-Host ""

# تشغيل النظام في الخلفية
$process = Start-Process python -ArgumentList "app.py" -PassThru -WindowStyle Hidden

# انتظار تشغيل النظام
Start-Sleep -Seconds 8

# فتح المتصفح
Write-Host "🌐 فتح النظام في المتصفح..." -ForegroundColor Green
Start-Process "http://localhost:9090"

# فتح صفحات إضافية
Start-Sleep -Seconds 2
Start-Process "http://localhost:9090/dashboard"
Start-Sleep -Seconds 1
Start-Process "http://localhost:9090/subscriptions"
Start-Sleep -Seconds 1
Start-Process "http://localhost:9090/ui-controller"

Write-Host ""
Write-Host "===============================================================================" -ForegroundColor Cyan
Write-Host "                           ✅ تم تشغيل النظام بنجاح!" -ForegroundColor Green
Write-Host "===============================================================================" -ForegroundColor Cyan
Write-Host ""
Write-Host "🌐 الرابط: http://localhost:9090" -ForegroundColor White
Write-Host "👤 المستخدم: admin" -ForegroundColor White
Write-Host "🔑 كلمة المرور: 123456" -ForegroundColor White
Write-Host ""
Write-Host "📋 تم فتح الصفحات التالية:" -ForegroundColor Yellow
Write-Host "   • الصفحة الرئيسية" -ForegroundColor White
Write-Host "   • لوحة التحكم" -ForegroundColor White
Write-Host "   • إدارة الاشتراكات" -ForegroundColor White
Write-Host "   • تحكم الواجهات" -ForegroundColor White
Write-Host ""
Write-Host "⚠️ لإيقاف النظام: اضغط Ctrl+C أو أغلق هذه النافذة" -ForegroundColor Red
Write-Host ""

# إبقاء النافذة مفتوحة
try {
    while ($true) {
        Start-Sleep -Seconds 1
    }
} catch {
    Write-Host "تم إيقاف النظام" -ForegroundColor Yellow
}
