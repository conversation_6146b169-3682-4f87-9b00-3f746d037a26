#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الاشتراكات
Subscription Management System

تطبيق Flask لإدارة اشتراكات الخدمات السحابية
"""

from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify, make_response
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
import os
import json
import numpy as np
import pandas as pd
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_absolute_error, r2_score
import joblib
import warnings
warnings.filterwarnings('ignore')
from functools import wraps
import secrets
import hashlib
import hmac
import base64
import ipaddress
import re
from collections import defaultdict, deque
import time
import threading
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.units import inch
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import io
import base64
# استيراد مكتبات الإيميل (اختيارية)
try:
    from flask_mail import Mail, Message
    MAIL_AVAILABLE = True
except ImportError:
    Mail = None
    Message = None
    MAIL_AVAILABLE = False

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import logging
from logging.handlers import RotatingFileHandler

# إعداد التطبيق
app = Flask(__name__)

# إعدادات الأمان والأداء
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'subscription_management_system_2024_secure_key')
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///subscriptions.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
    'pool_pre_ping': True,
    'pool_recycle': 300,
}

# إعدادات الجلسة
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=24)
app.config['SESSION_COOKIE_SECURE'] = False  # True في الإنتاج مع HTTPS
app.config['SESSION_COOKIE_HTTPONLY'] = True
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'

# ===== تحسينات الأداء والسرعة =====

# إعدادات التخزين المؤقت
app.config['CACHE_TYPE'] = 'simple'
app.config['CACHE_DEFAULT_TIMEOUT'] = 300

# إعدادات الضغط
app.config['COMPRESS_MIMETYPES'] = [
    'text/html', 'text/css', 'text/xml', 'application/json',
    'application/javascript', 'text/javascript'
]
app.config['COMPRESS_LEVEL'] = 6
app.config['COMPRESS_MIN_SIZE'] = 500

# تحسين قاعدة البيانات
app.config['SQLALCHEMY_ENGINE_OPTIONS'].update({
    'pool_size': 20,
    'max_overflow': 30,
    'pool_timeout': 30,
    'pool_recycle': 3600
})

# تفعيل التحسينات
try:
    from flask_caching import Cache
    from flask_compress import Compress

    cache = Cache(app)
    compress = Compress(app)
    print("✅ تم تفعيل التخزين المؤقت والضغط")
except ImportError:
    print("⚠️ تحذير: مكتبات التحسين غير متوفرة")
    cache = None
    compress = None
except Exception as e:
    print(f"⚠️ تحذير: فشل في تفعيل التحسينات: {e}")
    cache = None
    compress = None

# إعدادات الأمان المتقدمة - تم تفعيلها بعد حل المشاكل
app.config['SECURITY_ENABLED'] = True  # تفعيل نظام الحماية
app.config['MAX_LOGIN_ATTEMPTS'] = 5
app.config['LOCKOUT_DURATION'] = 300  # 5 دقائق
app.config['SESSION_TIMEOUT'] = 3600  # ساعة واحدة
app.config['ENCRYPTION_KEY'] = base64.urlsafe_b64encode(secrets.token_bytes(32))
app.config['RATE_LIMIT_ENABLED'] = True  # تفعيل حد المعدل
app.config['MAX_REQUESTS_PER_MINUTE'] = 100
app.config['FIREWALL_ENABLED'] = True  # تفعيل جدار الحماية

# إعدادات التحميل
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# إعداد قاعدة البيانات
db = SQLAlchemy(app)

# ===== نظام الحماية والأمان المتقدم =====

class SecurityManager:
    """مدير الأمان والحماية المتقدم"""

    def __init__(self, app):
        self.app = app
        self.failed_attempts = defaultdict(int)
        self.blocked_ips = set()
        self.rate_limits = defaultdict(deque)
        self.threat_log = deque(maxlen=1000)
        self.encryption_key = app.config.get('ENCRYPTION_KEY')
        self.lock = threading.Lock()

    def is_ip_blocked(self, ip):
        """فحص ما إذا كان IP محظور"""
        return ip in self.blocked_ips

    def block_ip(self, ip, reason="Security violation"):
        """حظر IP"""
        with self.lock:
            self.blocked_ips.add(ip)
            self.log_threat(ip, "IP_BLOCKED", reason, "high")

    def check_rate_limit(self, ip, limit_per_minute=60):
        """فحص حد المعدل للطلبات"""
        now = time.time()
        minute_ago = now - 60

        with self.lock:
            # إزالة الطلبات القديمة
            while self.rate_limits[ip] and self.rate_limits[ip][0] < minute_ago:
                self.rate_limits[ip].popleft()

            # فحص الحد
            if len(self.rate_limits[ip]) >= limit_per_minute:
                self.log_threat(ip, "RATE_LIMIT_EXCEEDED", f"Exceeded {limit_per_minute} requests per minute", "medium")
                return False

            # إضافة الطلب الحالي
            self.rate_limits[ip].append(now)
            return True

    def log_failed_login(self, ip, username):
        """تسجيل محاولة دخول فاشلة"""
        with self.lock:
            self.failed_attempts[ip] += 1

            if self.failed_attempts[ip] >= self.app.config.get('MAX_LOGIN_ATTEMPTS', 5):
                self.block_ip(ip, f"Too many failed login attempts for user: {username}")
                return True  # IP محظور

            self.log_threat(ip, "FAILED_LOGIN", f"Failed login attempt for user: {username}", "low")
            return False

    def reset_failed_attempts(self, ip):
        """إعادة تعيين محاولات الدخول الفاشلة"""
        with self.lock:
            self.failed_attempts[ip] = 0

    def log_threat(self, ip, threat_type, description, severity="medium"):
        """تسجيل تهديد أمني"""
        threat = {
            'timestamp': datetime.now().isoformat(),
            'ip': ip,
            'type': threat_type,
            'description': description,
            'severity': severity,
            'id': secrets.token_hex(8)
        }

        with self.lock:
            self.threat_log.append(threat)

    def get_recent_threats(self, limit=10):
        """الحصول على التهديدات الحديثة"""
        with self.lock:
            return list(self.threat_log)[-limit:]

    def validate_input(self, input_data, input_type="general"):
        """التحقق من صحة المدخلات ومنع الحقن"""
        if not input_data:
            return True

        # قائمة الأنماط المشبوهة
        suspicious_patterns = [
            r'<script[^>]*>.*?</script>',  # XSS
            r'javascript:',  # JavaScript injection
            r'union\s+select',  # SQL injection
            r'drop\s+table',  # SQL injection
            r'exec\s*\(',  # Command injection
        ]

        input_lower = str(input_data).lower()

        for pattern in suspicious_patterns:
            if re.search(pattern, input_lower, re.IGNORECASE):
                self.log_threat(
                    getattr(request, 'remote_addr', 'unknown'),
                    "MALICIOUS_INPUT",
                    f"Suspicious pattern detected: {pattern}",
                    "high"
                )
                return False

        return True

    def get_security_stats(self):
        """الحصول على إحصائيات الأمان"""
        with self.lock:
            recent_threats = [t for t in self.threat_log if
                            datetime.fromisoformat(t['timestamp']) > datetime.now() - timedelta(hours=24)]

            return {
                'blocked_ips': len(self.blocked_ips),
                'recent_threats': len(recent_threats),
                'failed_attempts_total': sum(self.failed_attempts.values()),
                'threat_levels': {
                    'high': len([t for t in recent_threats if t['severity'] == 'high']),
                    'medium': len([t for t in recent_threats if t['severity'] == 'medium']),
                    'low': len([t for t in recent_threats if t['severity'] == 'low'])
                }
            }

# تهيئة مدير الأمان
security_manager = SecurityManager(app)

# إعداد Flask-Migrate للتحديثات التلقائية (اختياري)
try:
    from flask_migrate import Migrate
    migrate = Migrate(app, db)
    MIGRATE_AVAILABLE = True
except ImportError:
    migrate = None
    MIGRATE_AVAILABLE = False

# إعداد نظام Logging
if not app.debug:
    if not os.path.exists('logs'):
        os.mkdir('logs')

    file_handler = RotatingFileHandler('logs/subscription_system.log', maxBytes=10240000, backupCount=10)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    app.logger.setLevel(logging.INFO)
    app.logger.info('نظام إدارة الاشتراكات - بدء التشغيل')

# ===== Middleware الحماية =====

@app.before_request
def security_middleware():
    """middleware الحماية والأمان"""
    try:
        # تجاهل الطلبات الثابتة
        if request.endpoint == 'static':
            return

        # تجاهل routes الاختبار
        if request.endpoint in ['test_login', 'test_dashboard']:
            return

        if not app.config.get('SECURITY_ENABLED', True):
            return

        # الحصول على IP العميل
        client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
        if client_ip and ',' in client_ip:
            client_ip = client_ip.split(',')[0].strip()

        # فحص IP المحظور
        if security_manager.is_ip_blocked(client_ip):
            app.logger.warning(f"Blocked IP attempted access: {client_ip}")
            return jsonify({'error': 'Access denied'}), 403

        # فحص حد المعدل للAPI فقط
        if request.path.startswith('/api/') and app.config.get('RATE_LIMIT_ENABLED', True):
            if not security_manager.check_rate_limit(client_ip, app.config.get('MAX_REQUESTS_PER_MINUTE', 100)):
                app.logger.warning(f"Rate limit exceeded for IP: {client_ip}")
                return jsonify({'error': 'Rate limit exceeded'}), 429

    except Exception as e:
        app.logger.error(f"Security middleware error: {e}")
        # في حالة خطأ في نظام الحماية، نسمح بالمرور لتجنب تعطيل النظام
        pass

@app.after_request
def security_headers(response):
    """إضافة headers الأمان"""
    # Headers الأمان الأساسية
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'

    # CSP مبسط للتطوير
    if not app.debug:
        response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'

    return response

# نماذج قاعدة البيانات

class Role(db.Model):
    """نموذج الأدوار"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(50), unique=True, nullable=False)
    display_name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text, nullable=True)
    permissions = db.Column(db.Text, nullable=True)  # JSON string of permissions
    is_system = db.Column(db.Boolean, default=False)  # أدوار النظام لا يمكن حذفها
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    def get_permissions(self):
        """الحصول على الأذونات كقائمة"""
        if self.permissions:
            try:
                return json.loads(self.permissions)
            except:
                return []
        return []

    def set_permissions(self, permissions_list):
        """تعيين الأذونات"""
        self.permissions = json.dumps(permissions_list)

    def has_permission(self, permission):
        """التحقق من وجود إذن معين"""
        return permission in self.get_permissions()

class User(db.Model):
    """نموذج المستخدمين المحدث"""
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    first_name = db.Column(db.String(50), nullable=True)
    last_name = db.Column(db.String(50), nullable=True)
    phone = db.Column(db.String(20), nullable=True)
    avatar = db.Column(db.String(200), nullable=True)
    role_id = db.Column(db.Integer, db.ForeignKey('role.id'), nullable=True)
    department = db.Column(db.String(100), nullable=True)
    position = db.Column(db.String(100), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime, nullable=True)
    last_activity = db.Column(db.DateTime, nullable=True)
    is_active = db.Column(db.Boolean, default=True)
    is_verified = db.Column(db.Boolean, default=False)
    login_attempts = db.Column(db.Integer, default=0)
    locked_until = db.Column(db.DateTime, nullable=True)

    # العلاقات
    role = db.relationship('Role', backref='users')

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    @property
    def full_name(self):
        """الاسم الكامل"""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        return self.username

    @property
    def is_admin(self):
        """التحقق من كون المستخدم مدير"""
        return self.role and self.role.name == 'admin'

    @property
    def is_locked(self):
        """التحقق من قفل الحساب"""
        if self.locked_until:
            return datetime.utcnow() < self.locked_until
        return False

    def has_permission(self, permission):
        """التحقق من الأذونات"""
        if not self.role:
            return False
        return self.role.has_permission(permission)

    def get_permissions(self):
        """الحصول على جميع الأذونات"""
        if self.role:
            return self.role.get_permissions()
        return []

class CloudProvider(db.Model):
    """نموذج مزودي الخدمات السحابية"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    logo_url = db.Column(db.String(200))
    website = db.Column(db.String(200))
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Subscription(db.Model):
    """نموذج الاشتراكات"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    provider_id = db.Column(db.Integer, db.ForeignKey('cloud_provider.id'), nullable=False)
    api_key = db.Column(db.String(200), nullable=False)
    port = db.Column(db.String(10))
    cloud_ip = db.Column(db.String(45))  # IPv4 or IPv6
    cloud_name = db.Column(db.String(100))  # اسم الكلاود/السيرفر
    customer_email = db.Column(db.String(120))  # إيميل العميل لإرسال الرسائل
    customer_name = db.Column(db.String(100))  # اسم العميل
    subscription_type = db.Column(db.String(20), nullable=False)  # monthly, semi_annual, annual
    price = db.Column(db.Float, nullable=False)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default='active')  # active, suspended, expired
    accounting_status = db.Column(db.String(20), default='unpaid')  # paid, unpaid
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    provider = db.relationship('CloudProvider', backref=db.backref('subscriptions', lazy=True))

    @property
    def network_name(self):
        """اسم الشبكة"""
        return self.name

    @property
    def provider_name(self):
        """اسم مزود الخدمة"""
        return self.provider.name if self.provider else 'شركة محمد الجبوري'

    @property
    def days_remaining(self):
        """حساب الأيام المتبقية"""
        if self.end_date:
            delta = self.end_date - datetime.now().date()
            return delta.days
        return 0

    @property
    def is_expired(self):
        """التحقق من انتهاء الاشتراك"""
        return self.days_remaining < 0

    @property
    def is_expiring_soon(self):
        """التحقق من قرب انتهاء الاشتراك"""
        return 0 <= self.days_remaining <= 7

    @property
    def last_payment(self):
        """آخر دفعة"""
        payments = Payment.query.filter_by(subscription_id=self.id).order_by(Payment.payment_date.desc()).first()
        return payments

    @property
    def total_paid(self):
        """إجمالي المدفوع"""
        payments = Payment.query.filter_by(subscription_id=self.id, status='completed').all()
        return sum([p.amount for p in payments])

    @property
    def remaining_amount(self):
        """المبلغ المتبقي"""
        return max(0, self.price - self.total_paid)

class Invoice(db.Model):
    """نموذج الفواتير"""
    id = db.Column(db.Integer, primary_key=True)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'), nullable=False)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    amount = db.Column(db.Float, nullable=False)
    issue_date = db.Column(db.Date, nullable=False)
    due_date = db.Column(db.Date, nullable=False)
    payment_date = db.Column(db.Date)
    status = db.Column(db.String(20), default='pending')  # pending, paid, overdue
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    subscription = db.relationship('Subscription', backref=db.backref('invoices', lazy=True))

class Payment(db.Model):
    """نموذج الدفعات"""
    id = db.Column(db.Integer, primary_key=True)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    payment_date = db.Column(db.Date, nullable=False, default=datetime.now().date())
    payment_method = db.Column(db.String(50), nullable=False, default='cash')  # cash, bank_transfer, card
    status = db.Column(db.String(20), nullable=False, default='completed')  # pending, completed, failed
    notes = db.Column(db.Text, nullable=True)
    receipt_number = db.Column(db.String(50), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    subscription = db.relationship('Subscription', backref=db.backref('payments', lazy=True, order_by='Payment.payment_date.desc()'))

class AIModel(db.Model):
    """نموذج الذكاء الاصطناعي"""
    id = db.Column(db.Integer, primary_key=True)
    model_name = db.Column(db.String(100), nullable=False)
    model_type = db.Column(db.String(50), nullable=False)  # prediction, classification, recommendation
    model_data = db.Column(db.Text, nullable=True)  # JSON data for model parameters
    accuracy = db.Column(db.Float, default=0.0)
    last_trained = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class UserPreference(db.Model):
    """تفضيلات المستخدم"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    preference_key = db.Column(db.String(100), nullable=False)
    preference_value = db.Column(db.Text, nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    user = db.relationship('User', backref='preferences')

class SystemConfig(db.Model):
    """إعدادات النظام المتقدمة"""
    __tablename__ = 'system_config'
    id = db.Column(db.Integer, primary_key=True)
    setting_key = db.Column(db.String(100), unique=True, nullable=False)
    setting_value = db.Column(db.Text, nullable=False)
    setting_type = db.Column(db.String(50), default='string')  # string, number, boolean, json
    description = db.Column(db.Text, nullable=True)
    is_public = db.Column(db.Boolean, default=False)  # يمكن للمستخدمين العاديين رؤيتها
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

# ===== فئات الذكاء الاصطناعي =====

class AIEngine:
    """محرك الذكاء الاصطناعي"""

    def __init__(self):
        self.models = {}
        self.scaler = StandardScaler()

    def prepare_subscription_data(self):
        """تحضير بيانات الاشتراكات للتدريب"""
        try:
            subscriptions = Subscription.query.all()
            data = []

            for sub in subscriptions:
                # حساب الميزات
                days_active = (datetime.now().date() - sub.start_date).days if sub.start_date else 0
                total_payments = len(sub.payments)
                avg_payment = sub.total_paid / total_payments if total_payments > 0 else 0
                payment_frequency = total_payments / max(days_active, 1) * 30  # دفعات شهرياً

                # تحويل نوع الاشتراك إلى رقم
                subscription_type_map = {'monthly': 1, 'semi_annual': 6, 'annual': 12}
                subscription_months = subscription_type_map.get(sub.subscription_type, 1)

                # حساب معدل التجديد (هدف التنبؤ)
                renewal_rate = 1 if sub.status == 'active' and sub.days_remaining > 0 else 0

                data.append({
                    'price': float(sub.price),
                    'subscription_months': subscription_months,
                    'days_active': days_active,
                    'total_payments': total_payments,
                    'avg_payment': avg_payment,
                    'payment_frequency': payment_frequency,
                    'remaining_amount': sub.remaining_amount,
                    'days_remaining': sub.days_remaining,
                    'renewal_rate': renewal_rate
                })

            return pd.DataFrame(data)
        except Exception as e:
            print(f"خطأ في تحضير البيانات: {e}")
            return pd.DataFrame()

    def train_renewal_prediction_model(self):
        """تدريب نموذج التنبؤ بالتجديد"""
        try:
            df = self.prepare_subscription_data()
            if df.empty:
                return False

            # تحضير الميزات والهدف
            features = ['price', 'subscription_months', 'days_active', 'total_payments',
                       'avg_payment', 'payment_frequency', 'remaining_amount', 'days_remaining']

            X = df[features].fillna(0)
            y = df['renewal_rate']

            if len(X) < 5:  # نحتاج بيانات كافية للتدريب
                return False

            # تقسيم البيانات
            X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

            # تطبيع البيانات
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_test_scaled = self.scaler.transform(X_test)

            # تدريب النموذج
            model = LinearRegression()
            model.fit(X_train_scaled, y_train)

            # تقييم النموذج
            y_pred = model.predict(X_test_scaled)
            accuracy = r2_score(y_test, y_pred)

            # حفظ النموذج
            self.models['renewal_prediction'] = {
                'model': model,
                'scaler': self.scaler,
                'features': features,
                'accuracy': accuracy
            }

            # حفظ في قاعدة البيانات
            ai_model = AIModel.query.filter_by(model_name='renewal_prediction').first()
            if not ai_model:
                ai_model = AIModel(model_name='renewal_prediction', model_type='prediction')

            ai_model.accuracy = accuracy
            ai_model.last_trained = datetime.utcnow()
            ai_model.model_data = json.dumps({
                'features': features,
                'accuracy': accuracy,
                'training_samples': len(X_train)
            })

            db.session.add(ai_model)
            db.session.commit()

            return True

        except Exception as e:
            print(f"خطأ في تدريب النموذج: {e}")
            return False

    def predict_renewal_probability(self, subscription_id):
        """التنبؤ باحتمالية التجديد"""
        try:
            if 'renewal_prediction' not in self.models:
                if not self.train_renewal_prediction_model():
                    return 0.5  # احتمالية افتراضية

            subscription = Subscription.query.get(subscription_id)
            if not subscription:
                return 0.5

            # تحضير بيانات الاشتراك
            days_active = (datetime.now().date() - subscription.start_date).days if subscription.start_date else 0
            total_payments = len(subscription.payments)
            avg_payment = subscription.total_paid / total_payments if total_payments > 0 else 0
            payment_frequency = total_payments / max(days_active, 1) * 30

            subscription_type_map = {'monthly': 1, 'semi_annual': 6, 'annual': 12}
            subscription_months = subscription_type_map.get(subscription.subscription_type, 1)

            # إنشاء مصفوفة الميزات
            features = [
                float(subscription.price),
                subscription_months,
                days_active,
                total_payments,
                avg_payment,
                payment_frequency,
                subscription.remaining_amount,
                subscription.days_remaining
            ]

            # التنبؤ
            model_data = self.models['renewal_prediction']
            features_scaled = model_data['scaler'].transform([features])
            probability = model_data['model'].predict(features_scaled)[0]

            # تحديد الاحتمالية بين 0 و 1
            return max(0, min(1, probability))

        except Exception as e:
            print(f"خطأ في التنبؤ: {e}")
            return 0.5

    def get_smart_recommendations(self, subscription_id):
        """الحصول على توصيات ذكية"""
        try:
            subscription = Subscription.query.get(subscription_id)
            if not subscription:
                return []

            recommendations = []
            renewal_prob = self.predict_renewal_probability(subscription_id)

            # توصيات بناءً على احتمالية التجديد
            if renewal_prob < 0.3:
                recommendations.append({
                    'type': 'warning',
                    'title': 'خطر عدم التجديد',
                    'message': 'احتمالية التجديد منخفضة. يُنصح بالتواصل مع العميل.',
                    'action': 'contact_customer',
                    'priority': 'high'
                })
            elif renewal_prob > 0.8:
                recommendations.append({
                    'type': 'success',
                    'title': 'عميل مخلص',
                    'message': 'احتمالية التجديد عالية. يمكن تقديم عروض إضافية.',
                    'action': 'upsell',
                    'priority': 'medium'
                })

            # توصيات بناءً على المدفوعات
            if subscription.remaining_amount > 0:
                recommendations.append({
                    'type': 'info',
                    'title': 'مبلغ متبقي',
                    'message': f'يوجد مبلغ ${subscription.remaining_amount:.2f} غير مدفوع.',
                    'action': 'collect_payment',
                    'priority': 'medium'
                })

            # توصيات بناءً على تاريخ الانتهاء
            if subscription.days_remaining <= 7 and subscription.days_remaining > 0:
                recommendations.append({
                    'type': 'warning',
                    'title': 'اقتراب الانتهاء',
                    'message': f'الاشتراك ينتهي خلال {subscription.days_remaining} أيام.',
                    'action': 'prepare_renewal',
                    'priority': 'high'
                })

            return recommendations

        except Exception as e:
            print(f"خطأ في التوصيات: {e}")
            return []

# إنشاء محرك الذكاء الاصطناعي
ai_engine = AIEngine()

class Notification(db.Model):
    """نموذج الإشعارات"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    type = db.Column(db.String(20), default='info')  # info, warning, error, success
    is_read = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    user = db.relationship('User', backref=db.backref('notifications', lazy=True))

class SystemSettings(db.Model):
    """نموذج إعدادات النظام"""
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False)
    value = db.Column(db.Text)
    description = db.Column(db.Text)
    category = db.Column(db.String(50), default='general')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class ActivityLog(db.Model):
    """نموذج سجل الأنشطة"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    action = db.Column(db.String(100), nullable=False)  # create, update, delete, login, etc.
    entity_type = db.Column(db.String(50))  # subscription, invoice, user, etc.
    entity_id = db.Column(db.Integer)
    description = db.Column(db.Text)
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    user = db.relationship('User', backref=db.backref('activity_logs', lazy=True))

# ===== نماذج أنظمة الدفع اليمنية =====

class PaymentGateway(db.Model):
    """نموذج بوابات الدفع اليمنية"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    name_ar = db.Column(db.String(100), nullable=False)
    code = db.Column(db.String(20), unique=True, nullable=False)
    type = db.Column(db.String(50), nullable=False)  # bank, wallet, mobile
    logo = db.Column(db.String(200))
    is_active = db.Column(db.Boolean, default=True)
    api_endpoint = db.Column(db.String(500))
    api_key = db.Column(db.String(200))
    api_secret = db.Column(db.String(200))
    merchant_id = db.Column(db.String(100))
    fees_percentage = db.Column(db.Float, default=0.0)
    fees_fixed = db.Column(db.Float, default=0.0)
    min_amount = db.Column(db.Float, default=1.0)
    max_amount = db.Column(db.Float, default=999999.0)
    currency = db.Column(db.String(10), default='YER')
    description = db.Column(db.Text)
    instructions = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class PaymentTransaction(db.Model):
    """نموذج معاملات الدفع"""
    id = db.Column(db.Integer, primary_key=True)
    transaction_id = db.Column(db.String(100), unique=True, nullable=False)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoice.id'), nullable=False)
    gateway_id = db.Column(db.Integer, db.ForeignKey('payment_gateway.id'), nullable=False)
    amount = db.Column(db.Float, nullable=False)
    fees = db.Column(db.Float, default=0.0)
    net_amount = db.Column(db.Float, nullable=False)
    currency = db.Column(db.String(10), default='YER')
    status = db.Column(db.String(20), default='pending')  # pending, processing, completed, failed, cancelled
    gateway_transaction_id = db.Column(db.String(200))
    gateway_response = db.Column(db.Text)
    customer_phone = db.Column(db.String(20))
    customer_name = db.Column(db.String(100))
    payment_method = db.Column(db.String(50))
    reference_number = db.Column(db.String(100))
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    completed_at = db.Column(db.DateTime)

    invoice = db.relationship('Invoice', backref='payment_transactions')
    gateway = db.relationship('PaymentGateway', backref='transactions')

class AdvancedReport(db.Model):
    """نموذج التقارير المتقدمة"""
    id = db.Column(db.Integer, primary_key=True)
    report_name = db.Column(db.String(200), nullable=False)
    report_type = db.Column(db.String(50), nullable=False)  # financial, subscription, payment, custom
    report_category = db.Column(db.String(50), nullable=False)  # daily, weekly, monthly, yearly, custom
    date_from = db.Column(db.Date, nullable=False)
    date_to = db.Column(db.Date, nullable=False)
    filters = db.Column(db.Text)  # JSON filters
    report_data = db.Column(db.Text)  # JSON report data
    summary_data = db.Column(db.Text)  # JSON summary
    charts_data = db.Column(db.Text)  # JSON charts data
    generated_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    file_path = db.Column(db.String(500))  # PDF/Excel file path
    status = db.Column(db.String(20), default='generated')  # generated, archived, deleted
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    user = db.relationship('User', backref='generated_reports')

class ReportTemplate(db.Model):
    """نموذج قوالب التقارير"""
    id = db.Column(db.Integer, primary_key=True)
    template_name = db.Column(db.String(200), nullable=False)
    template_type = db.Column(db.String(50), nullable=False)
    description = db.Column(db.Text)
    template_config = db.Column(db.Text)  # JSON configuration
    is_system = db.Column(db.Boolean, default=False)  # قوالب النظام
    is_active = db.Column(db.Boolean, default=True)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    user = db.relationship('User', backref='report_templates')

class ReportSchedule(db.Model):
    """نموذج جدولة التقارير"""
    id = db.Column(db.Integer, primary_key=True)
    schedule_name = db.Column(db.String(200), nullable=False)
    template_id = db.Column(db.Integer, db.ForeignKey('report_template.id'), nullable=False)
    frequency = db.Column(db.String(20), nullable=False)  # daily, weekly, monthly
    frequency_config = db.Column(db.Text)  # JSON config for frequency
    recipients = db.Column(db.Text)  # JSON list of email recipients
    is_active = db.Column(db.Boolean, default=True)
    last_run = db.Column(db.DateTime)
    next_run = db.Column(db.DateTime)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    template = db.relationship('ReportTemplate', backref='schedules')
    user = db.relationship('User', backref='report_schedules')

class AdvancedNotification(db.Model):
    """نموذج الإشعارات المتقدمة"""
    id = db.Column(db.Integer, primary_key=True)
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    notification_type = db.Column(db.String(50), nullable=False)  # info, warning, error, success, system
    priority = db.Column(db.String(20), default='normal')  # low, normal, high, urgent
    category = db.Column(db.String(50))  # payment, subscription, security, system
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    target_users = db.Column(db.Text)  # JSON list of user IDs
    is_global = db.Column(db.Boolean, default=False)
    is_read = db.Column(db.Boolean, default=False)
    is_dismissed = db.Column(db.Boolean, default=False)
    action_url = db.Column(db.String(500))
    action_text = db.Column(db.String(100))
    expires_at = db.Column(db.DateTime)
    extra_data = db.Column(db.Text)  # JSON metadata
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    read_at = db.Column(db.DateTime)

    user = db.relationship('User', backref='advanced_notifications')

class SystemMetrics(db.Model):
    """نموذج مقاييس النظام"""
    id = db.Column(db.Integer, primary_key=True)
    metric_name = db.Column(db.String(100), nullable=False)
    metric_value = db.Column(db.Float, nullable=False)
    metric_unit = db.Column(db.String(20))
    category = db.Column(db.String(50))  # performance, security, business
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    extra_data = db.Column(db.Text)  # JSON metadata

class AutomationRule(db.Model):
    """نموذج قواعد الأتمتة"""
    id = db.Column(db.Integer, primary_key=True)
    rule_name = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    trigger_type = db.Column(db.String(50), nullable=False)  # time, event, condition
    trigger_config = db.Column(db.Text)  # JSON configuration
    action_type = db.Column(db.String(50), nullable=False)  # email, notification, api_call, database
    action_config = db.Column(db.Text)  # JSON configuration
    is_active = db.Column(db.Boolean, default=True)
    last_executed = db.Column(db.DateTime)
    execution_count = db.Column(db.Integer, default=0)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    user = db.relationship('User', backref='automation_rules')

class APIIntegration(db.Model):
    """نموذج تكاملات API"""
    id = db.Column(db.Integer, primary_key=True)
    integration_name = db.Column(db.String(200), nullable=False)
    service_type = db.Column(db.String(50), nullable=False)  # webhook, rest_api, graphql
    endpoint_url = db.Column(db.String(500), nullable=False)
    auth_type = db.Column(db.String(50))  # none, api_key, oauth, basic
    auth_config = db.Column(db.Text)  # JSON auth configuration
    headers = db.Column(db.Text)  # JSON headers
    is_active = db.Column(db.Boolean, default=True)
    last_sync = db.Column(db.DateTime)
    sync_status = db.Column(db.String(20), default='pending')  # pending, success, failed
    error_message = db.Column(db.Text)
    created_by = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    user = db.relationship('User', backref='api_integrations')

class BackupLog(db.Model):
    """نموذج سجل النسخ الاحتياطية"""
    id = db.Column(db.Integer, primary_key=True)
    backup_type = db.Column(db.String(50), nullable=False)  # manual, automatic
    file_path = db.Column(db.String(500))
    file_size = db.Column(db.BigInteger)
    status = db.Column(db.String(20), default='completed')  # completed, failed, in_progress
    error_message = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

# دالة تسجيل الأنشطة
def log_activity(action, entity_type=None, entity_id=None, description=None):
    """تسجيل نشاط المستخدم"""
    try:
        if 'user_id' in session:
            activity = ActivityLog(
                user_id=session['user_id'],
                action=action,
                entity_type=entity_type,
                entity_id=entity_id,
                description=description,
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent', '')
            )
            db.session.add(activity)
            db.session.commit()
            app.logger.info(f'Activity logged: {action} by user {session.get("username", "Unknown")}')
    except Exception as e:
        app.logger.error(f'Error logging activity: {str(e)}')

# ===== دوال تحسين الأداء =====

def cache_key_prefix():
    """إنشاء مفتاح التخزين المؤقت"""
    return f"user_{session.get('user_id', 'anonymous')}"

def cached_query(cache_key, timeout=300):
    """ديكوريتر للتخزين المؤقت للاستعلامات"""
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            if cache:
                try:
                    result = cache.get(cache_key)
                    if result is not None:
                        return result
                except:
                    pass

            result = func(*args, **kwargs)

            if cache:
                try:
                    cache.set(cache_key, result, timeout=timeout)
                except:
                    pass

            return result
        return wrapper
    return decorator

def invalidate_cache(pattern):
    """إلغاء التخزين المؤقت"""
    if cache:
        try:
            cache.delete_memoized(pattern)
        except:
            pass

def optimize_query(query):
    """تحسين الاستعلامات"""
    try:
        # إضافة خيارات التحميل المسبق
        if hasattr(query, 'options'):
            from sqlalchemy.orm import joinedload, selectinload
            # يمكن إضافة تحسينات محددة هنا
        return query
    except:
        return query

@app.before_request
def performance_monitoring():
    """مراقبة الأداء"""
    request.start_time = time.time()

@app.after_request
def performance_logging(response):
    """تسجيل أداء الطلبات"""
    try:
        if hasattr(request, 'start_time'):
            duration = time.time() - request.start_time

            # تسجيل الطلبات البطيئة
            if duration > 2.0:  # أكثر من ثانيتين
                app.logger.warning(f"Slow request: {request.endpoint} took {duration:.2f}s")

            # إضافة headers للأداء
            response.headers['X-Response-Time'] = f"{duration:.3f}s"

        # إضافة headers التخزين المؤقت
        if request.endpoint in ['static', 'dashboard']:
            response.headers['Cache-Control'] = 'public, max-age=300'

        # إضافة headers الأمان
        response.headers['X-Content-Type-Options'] = 'nosniff'
        response.headers['X-Frame-Options'] = 'DENY'
        response.headers['X-XSS-Protection'] = '1; mode=block'

    except Exception as e:
        app.logger.error(f"Performance logging error: {e}")

    return response

def preload_dashboard_data():
    """تحميل مسبق لبيانات لوحة التحكم"""
    try:
        if cache:
            cache_key = f"{cache_key_prefix()}_dashboard_data"
            cached_data = cache.get(cache_key)
            if cached_data:
                return cached_data

        # تحميل البيانات الأساسية
        data = {
            'total_subscriptions': Subscription.query.count(),
            'active_subscriptions': Subscription.query.filter_by(status='active').count(),
            'total_revenue': db.session.query(func.sum(Invoice.amount)).filter_by(status='paid').scalar() or 0,
            'pending_invoices': Invoice.query.filter_by(status='pending').count()
        }

        if cache:
            cache.set(cache_key, data, timeout=300)

        return data

    except Exception as e:
        app.logger.error(f"Error preloading dashboard data: {e}")
        return {}

def optimize_database():
    """تحسين قاعدة البيانات"""
    try:
        # تنفيذ VACUUM للـ SQLite
        if 'sqlite' in app.config['SQLALCHEMY_DATABASE_URI']:
            db.engine.execute(text('VACUUM'))
            db.engine.execute(text('ANALYZE'))
            print("✅ تم تحسين قاعدة البيانات SQLite")

        # إنشاء فهارس للأداء
        create_performance_indexes()

    except Exception as e:
        print(f"⚠️ تحذير: فشل في تحسين قاعدة البيانات: {e}")

def create_performance_indexes():
    """إنشاء فهارس للأداء"""
    try:
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_subscription_status ON subscription(status)",
            "CREATE INDEX IF NOT EXISTS idx_subscription_customer ON subscription(customer_id)",
            "CREATE INDEX IF NOT EXISTS idx_invoice_status ON invoice(status)",
            "CREATE INDEX IF NOT EXISTS idx_invoice_date ON invoice(created_at)",
            "CREATE INDEX IF NOT EXISTS idx_payment_date ON payment(payment_date)",
            "CREATE INDEX IF NOT EXISTS idx_activity_user ON activity_log(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_activity_date ON activity_log(created_at)",
            "CREATE INDEX IF NOT EXISTS idx_payment_transaction_status ON payment_transaction(status)",
            "CREATE INDEX IF NOT EXISTS idx_payment_transaction_gateway ON payment_transaction(gateway_id)"
        ]

        for index_sql in indexes:
            try:
                db.engine.execute(text(index_sql))
            except:
                pass  # الفهرس موجود بالفعل

        print("✅ تم إنشاء فهارس الأداء")

    except Exception as e:
        print(f"⚠️ تحذير: فشل في إنشاء الفهارس: {e}")

# ===== دوال إنشاء بوابات الدفع اليمنية =====

def create_payment_gateways():
    """إنشاء بوابات الدفع اليمنية"""
    try:
        # التحقق من وجود بوابات الدفع
        if PaymentGateway.query.first():
            return

        gateways = [
            {
                'name': 'Aden Cash',
                'name_ar': 'عدن كاش',
                'code': 'ADEN_CASH',
                'type': 'wallet',
                'logo': '/static/images/aden_cash.png',
                'fees_percentage': 1.5,
                'fees_fixed': 0.0,
                'min_amount': 100.0,
                'max_amount': 500000.0,
                'description': 'محفظة عدن كاش الإلكترونية',
                'instructions': 'أدخل رقم هاتفك المسجل في عدن كاش لإتمام الدفع'
            },
            {
                'name': 'Shelen Wallet',
                'name_ar': 'محفظة شلن',
                'code': 'SHELEN_WALLET',
                'type': 'wallet',
                'logo': '/static/images/shelen.png',
                'fees_percentage': 1.0,
                'fees_fixed': 0.0,
                'min_amount': 50.0,
                'max_amount': 1000000.0,
                'description': 'محفظة شلن الإلكترونية',
                'instructions': 'أدخل رقم هاتفك المسجل في محفظة شلن'
            },
            {
                'name': 'Aden Bank',
                'name_ar': 'بنك عدن',
                'code': 'ADEN_BANK',
                'type': 'bank',
                'logo': '/static/images/aden_bank.png',
                'fees_percentage': 0.5,
                'fees_fixed': 25.0,
                'min_amount': 1000.0,
                'max_amount': 5000000.0,
                'description': 'بنك عدن الإسلامي',
                'instructions': 'حوالة بنكية عبر بنك عدن الإسلامي'
            },
            {
                'name': 'Al-Qutaibi Bank',
                'name_ar': 'بنك القطيبي',
                'code': 'QUTAIBI_BANK',
                'type': 'bank',
                'logo': '/static/images/qutaibi_bank.png',
                'fees_percentage': 0.75,
                'fees_fixed': 30.0,
                'min_amount': 1000.0,
                'max_amount': 3000000.0,
                'description': 'بنك القطيبي التجاري',
                'instructions': 'حوالة بنكية عبر بنك القطيبي التجاري'
            },
            {
                'name': 'Yemen Mobile Pay',
                'name_ar': 'يمن موبايل باي',
                'code': 'YEMEN_MOBILE',
                'type': 'mobile',
                'logo': '/static/images/yemen_mobile.png',
                'fees_percentage': 2.0,
                'fees_fixed': 0.0,
                'min_amount': 100.0,
                'max_amount': 200000.0,
                'description': 'خدمة الدفع عبر يمن موبايل',
                'instructions': 'أدخل رقم يمن موبايل لإتمام الدفع'
            },
            {
                'name': 'MTN Mobile Money',
                'name_ar': 'إم تي إن موبايل موني',
                'code': 'MTN_MOBILE',
                'type': 'mobile',
                'logo': '/static/images/mtn_mobile.png',
                'fees_percentage': 1.8,
                'fees_fixed': 0.0,
                'min_amount': 100.0,
                'max_amount': 300000.0,
                'description': 'خدمة الدفع عبر إم تي إن',
                'instructions': 'أدخل رقم إم تي إن لإتمام الدفع'
            }
        ]

        for gateway_data in gateways:
            gateway = PaymentGateway(**gateway_data)
            db.session.add(gateway)

        db.session.commit()
        print("✅ تم إنشاء بوابات الدفع اليمنية")

    except Exception as e:
        print(f"❌ خطأ في إنشاء بوابات الدفع: {e}")
        db.session.rollback()

# ===== دوال الإشعارات المتقدمة =====

def create_notification(title, message, notification_type='info', user_id=None,
                       target_users=None, is_global=False, priority='normal',
                       category=None, action_url=None, action_text=None, expires_at=None):
    """إنشاء إشعار متقدم"""
    try:
        notification = AdvancedNotification(
            title=title,
            message=message,
            notification_type=notification_type,
            priority=priority,
            category=category,
            user_id=user_id,
            target_users=json.dumps(target_users) if target_users else None,
            is_global=is_global,
            action_url=action_url,
            action_text=action_text,
            expires_at=expires_at
        )

        db.session.add(notification)
        db.session.commit()

        # إرسال إشعار فوري للمستخدمين المتصلين
        send_realtime_notification(notification)

        return notification

    except Exception as e:
        app.logger.error(f"Error creating notification: {e}")
        db.session.rollback()
        return None

def send_realtime_notification(notification):
    """إرسال إشعار فوري"""
    try:
        # يمكن تطوير هذا لاحقاً مع WebSocket أو Server-Sent Events
        pass
    except Exception as e:
        app.logger.error(f"Error sending realtime notification: {e}")

def get_user_notifications(user_id, limit=10, unread_only=False):
    """جلب إشعارات المستخدم"""
    try:
        query = AdvancedNotification.query.filter(
            or_(
                AdvancedNotification.user_id == user_id,
                AdvancedNotification.is_global == True,
                AdvancedNotification.target_users.contains(str(user_id))
            )
        )

        if unread_only:
            query = query.filter_by(is_read=False)

        # فلترة الإشعارات المنتهية الصلاحية
        query = query.filter(
            or_(
                AdvancedNotification.expires_at.is_(None),
                AdvancedNotification.expires_at > datetime.utcnow()
            )
        )

        notifications = query.order_by(
            AdvancedNotification.priority.desc(),
            AdvancedNotification.created_at.desc()
        ).limit(limit).all()

        return notifications

    except Exception as e:
        app.logger.error(f"Error getting user notifications: {e}")
        return []

def mark_notification_read(notification_id, user_id):
    """تمييز الإشعار كمقروء"""
    try:
        notification = AdvancedNotification.query.get(notification_id)
        if notification and (notification.user_id == user_id or notification.is_global):
            notification.is_read = True
            notification.read_at = datetime.utcnow()
            db.session.commit()
            return True
        return False
    except Exception as e:
        app.logger.error(f"Error marking notification as read: {e}")
        return False

def create_system_notification(event_type, details):
    """إنشاء إشعارات النظام التلقائية"""
    try:
        notifications_config = {
            'payment_received': {
                'title': 'تم استلام دفعة جديدة',
                'message': f'تم استلام دفعة بقيمة {details.get("amount", 0)} ريال',
                'type': 'success',
                'category': 'payment'
            },
            'subscription_expired': {
                'title': 'انتهاء اشتراك',
                'message': f'انتهى اشتراك العميل {details.get("customer_name", "")}',
                'type': 'warning',
                'category': 'subscription'
            },
            'security_alert': {
                'title': 'تنبيه أمني',
                'message': details.get('message', 'تم اكتشاف نشاط مشبوه'),
                'type': 'error',
                'category': 'security',
                'priority': 'urgent'
            },
            'system_backup': {
                'title': 'نسخ احتياطي للنظام',
                'message': 'تم إنشاء نسخة احتياطية بنجاح',
                'type': 'info',
                'category': 'system'
            }
        }

        config = notifications_config.get(event_type)
        if config:
            create_notification(
                title=config['title'],
                message=config['message'],
                notification_type=config['type'],
                category=config['category'],
                priority=config.get('priority', 'normal'),
                is_global=True
            )

    except Exception as e:
        app.logger.error(f"Error creating system notification: {e}")

# ===== دوال مقاييس النظام =====

def record_metric(metric_name, value, unit=None, category='performance', metadata=None):
    """تسجيل مقياس النظام"""
    try:
        metric = SystemMetrics(
            metric_name=metric_name,
            metric_value=value,
            metric_unit=unit,
            category=category,
            metadata=json.dumps(metadata) if metadata else None
        )

        db.session.add(metric)
        db.session.commit()

    except Exception as e:
        app.logger.error(f"Error recording metric: {e}")

def get_system_health():
    """جلب حالة النظام"""
    try:
        health_data = {
            'database_status': 'healthy',
            'cache_status': 'healthy' if cache else 'disabled',
            'active_users': User.query.filter_by(is_active=True).count(),
            'total_subscriptions': Subscription.query.count(),
            'pending_payments': Invoice.query.filter_by(status='pending').count(),
            'system_uptime': time.time() - app.start_time if hasattr(app, 'start_time') else 0
        }

        # فحص قاعدة البيانات
        try:
            db.session.execute(text('SELECT 1'))
            health_data['database_status'] = 'healthy'
        except:
            health_data['database_status'] = 'error'

        # تسجيل المقاييس
        record_metric('active_users', health_data['active_users'], category='business')
        record_metric('total_subscriptions', health_data['total_subscriptions'], category='business')

        return health_data

    except Exception as e:
        app.logger.error(f"Error getting system health: {e}")
        return {'status': 'error', 'message': str(e)}

# دالة التحقق من تسجيل الدخول
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('يرجى تسجيل الدخول للوصول لهذه الصفحة', 'error')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

# دالة التحقق من صلاحيات المدير
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('يرجى تسجيل الدخول للوصول لهذه الصفحة', 'error')
            return redirect(url_for('login'))
        
        user = User.query.get(session['user_id'])
        if not user or user.role != 'admin':
            flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
            return redirect(url_for('dashboard'))
        return f(*args, **kwargs)
    return decorated_function

# الصفحات الرئيسية
@app.route('/')
def index():
    """الصفحة الرئيسية"""
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    return render_template('welcome.html')

@app.route('/test_dashboard')
def test_dashboard():
    """اختبار مباشر للوحة التحكم"""
    # تسجيل دخول تلقائي للاختبار
    user = User.query.filter_by(username='admin').first()
    if user:
        session.clear()
        session['user_id'] = user.id
        session['username'] = user.username
        session['role'] = getattr(user.role, 'name', 'user') if user.role else 'user'
        session['logged_in'] = True
        session.permanent = True
        return redirect(url_for('dashboard'))
    else:
        return "المستخدم admin غير موجود"

@app.route('/test_login')
def test_login():
    """صفحة اختبار تسجيل الدخول"""
    return '''
    <!DOCTYPE html>
    <html lang="ar" dir="rtl">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>اختبار تسجيل الدخول</title>
        <style>
            body { font-family: Arial, sans-serif; padding: 20px; background: #f5f5f5; }
            .container { max-width: 400px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .form-group { margin-bottom: 15px; }
            label { display: block; margin-bottom: 5px; font-weight: bold; }
            input { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-size: 16px; }
            button { width: 100%; padding: 12px; background: #007bff; color: white; border: none; border-radius: 5px; font-size: 16px; cursor: pointer; }
            button:hover { background: #0056b3; }
            .result { margin-top: 15px; padding: 10px; border-radius: 5px; }
            .success { background: #d4edda; color: #155724; }
            .error { background: #f8d7da; color: #721c24; }
        </style>
    </head>
    <body>
        <div class="container">
            <h2>🔐 اختبار تسجيل الدخول</h2>
            <form method="POST" action="/login">
                <div class="form-group">
                    <label>اسم المستخدم:</label>
                    <input type="text" name="username" value="admin" required>
                </div>
                <div class="form-group">
                    <label>كلمة المرور:</label>
                    <input type="password" name="password" value="123456" required>
                </div>
                <button type="submit">تسجيل الدخول</button>
            </form>
            <div style="margin-top: 20px; text-align: center; color: #666;">
                <p><strong>بيانات الاختبار:</strong></p>
                <p>المستخدم: admin</p>
                <p>كلمة المرور: 123456</p>
            </div>
        </div>
    </body>
    </html>
    '''

@app.route('/login', methods=['GET', 'POST'])
def login():
    """صفحة تسجيل الدخول"""
    # إذا كان المستخدم مسجل دخول بالفعل، توجيه للوحة التحكم
    if 'user_id' in session:
        return redirect(url_for('dashboard'))

    # فحص الحماية
    try:
        client_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.remote_addr)
        if client_ip and ',' in client_ip:
            client_ip = client_ip.split(',')[0].strip()

        # فحص IP المحظور (مع تعطيل مؤقت)
        # if security_manager.is_ip_blocked(client_ip):
        #     flash('تم حظر الوصول من هذا العنوان', 'error')
        #     return render_template('login.html')
    except Exception as e:
        app.logger.error(f"Security check error: {e}")
        client_ip = request.remote_addr

    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')
        remember = 'remember' in request.form

        if not username or not password:
            flash('يرجى إدخال اسم المستخدم وكلمة المرور', 'error')
            return render_template('login.html')

        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password):
            # فحص حالة المستخدم
            if not user.is_active:
                flash('حسابك غير نشط. يرجى التواصل مع المدير', 'error')
                return render_template('login.html')

            # تسجيل دخول ناجح
            session.clear()  # مسح الجلسة السابقة
            session['user_id'] = user.id
            session['username'] = user.username
            session['role'] = getattr(user.role, 'name', 'user') if user.role else 'user'
            session['logged_in'] = True
            session.permanent = remember

            # تحديث آخر دخول
            try:
                user.last_login = datetime.utcnow()
                user.last_activity = datetime.utcnow()
                db.session.commit()
            except Exception as e:
                app.logger.error(f"Error updating user login time: {e}")

            # تسجيل نشاط تسجيل الدخول
            try:
                log_activity('login', 'user', user.id, f'تسجيل دخول ناجح للمستخدم {user.username}')
            except Exception as e:
                app.logger.error(f"Error logging activity: {e}")

            flash(f'مرحباً {user.username}! تم تسجيل الدخول بنجاح', 'success')
            return redirect(url_for('dashboard'))
        else:
            # تسجيل محاولة دخول فاشلة
            try:
                if user:
                    log_activity('failed_login', 'user', user.id, f'محاولة دخول فاشلة للمستخدم {username}')
            except Exception as e:
                app.logger.error(f"Error logging failed login: {e}")

            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    """تسجيل الخروج"""
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('index'))

@app.route('/dashboard')
@login_required
def dashboard():
    """لوحة التحكم الرئيسية"""
    # إحصائيات عامة
    total_subscriptions = Subscription.query.count()
    active_subscriptions = Subscription.query.filter_by(status='active').count()
    pending_invoices = Invoice.query.filter_by(status='pending').count()
    expired_subscriptions = Subscription.query.filter_by(status='expired').count()
    total_users = User.query.count()

    # إحصائيات الأمان
    try:
        security_stats = security_manager.get_security_stats()
        recent_threats = len(security_manager.get_recent_threats(5))
        blocked_ips_count = security_stats.get('blocked_ips', 0)
    except Exception as e:
        app.logger.error(f"Error getting security stats: {e}")
        security_stats = {}
        recent_threats = 0
        blocked_ips_count = 0

    # الإيرادات
    total_revenue = db.session.query(db.func.sum(Invoice.amount)).filter_by(status='paid').scalar() or 0
    monthly_revenue = db.session.query(db.func.sum(Invoice.amount)).filter(
        Invoice.status == 'paid',
        Invoice.payment_date >= datetime.now().replace(day=1).date()
    ).scalar() or 0

    # الاشتراكات المنتهية قريباً (خلال 30 يوم)
    upcoming_expiry = Subscription.query.filter(
        Subscription.end_date <= datetime.now().date() + timedelta(days=30),
        Subscription.status == 'active'
    ).all()

    # آخر الاشتراكات
    recent_subscriptions = Subscription.query.order_by(
        Subscription.created_at.desc()
    ).limit(5).all()

    # الإشعارات غير المقروءة
    unread_notifications = Notification.query.filter_by(
        user_id=session['user_id'], is_read=False
    ).count()

    # إحصائيات مزودي الخدمات
    provider_stats = db.session.query(
        CloudProvider.name,
        db.func.count(Subscription.id).label('count')
    ).join(Subscription).group_by(CloudProvider.name).all()

    # بيانات الرسم البياني (آخر 6 أشهر)
    chart_data = []
    revenue_data = []
    months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو']

    for i in range(6):
        month_start = datetime.now().replace(day=1) - timedelta(days=30*i)
        month_end = month_start + timedelta(days=30)

        # عدد الاشتراكات
        count = Subscription.query.filter(
            Subscription.created_at >= month_start,
            Subscription.created_at < month_end
        ).count()

        # الإيرادات
        revenue = db.session.query(db.func.sum(Invoice.amount)).filter(
            Invoice.created_at >= month_start,
            Invoice.created_at < month_end,
            Invoice.status == 'paid'
        ).scalar() or 0

        chart_data.append({
            'month': months[5-i] if i < 6 else month_start.strftime('%B'),
            'subscriptions': count,
            'revenue': float(revenue)
        })

    # إنشاء إشعارات تلقائية للاشتراكات المنتهية قريباً
    for subscription in upcoming_expiry:
        days_left = (subscription.end_date - datetime.now().date()).days
        existing_notification = Notification.query.filter_by(
            user_id=session['user_id'],
            title=f'تنبيه: اشتراك {subscription.name} سينتهي قريباً'
        ).first()

        if not existing_notification and days_left <= 7:
            notification = Notification(
                user_id=session['user_id'],
                title=f'تنبيه: اشتراك {subscription.name} سينتهي قريباً',
                message=f'اشتراك {subscription.name} سينتهي خلال {days_left} أيام. يرجى تجديده.',
                type='warning'
            )
            db.session.add(notification)

    db.session.commit()

    return render_template('dashboard.html',
                         total_subscriptions=total_subscriptions,
                         active_subscriptions=active_subscriptions,
                         pending_invoices=pending_invoices,
                         expired_subscriptions=expired_subscriptions,
                         total_users=total_users,
                         total_revenue=total_revenue,
                         monthly_revenue=monthly_revenue,
                         upcoming_expiry=upcoming_expiry,
                         recent_subscriptions=recent_subscriptions,
                         unread_notifications=unread_notifications,
                         provider_stats=provider_stats,
                         chart_data=json.dumps(chart_data),
                         today=datetime.now().date(),
                         recent_threats=recent_threats,
                         blocked_ips_count=blocked_ips_count,
                         security_stats=security_stats)

@app.route('/subscriptions')
@login_required
def subscriptions():
    """صفحة إدارة الاشتراكات المتطورة"""
    try:
        # جلب جميع الاشتراكات مع المزودين
        subscriptions = Subscription.query.all()

        # تحويل البيانات لـ JSON للاستخدام في JavaScript
        subscriptions_data = []
        for subscription in subscriptions:
            subscription_dict = {
                'id': subscription.id,
                'name': subscription.name,
                'customer_name': subscription.customer_name,
                'customer_email': subscription.customer_email,
                'customer_phone': getattr(subscription, 'customer_phone', None),
                'cloud_name': subscription.cloud_name,
                'cloud_ip': subscription.cloud_ip,
                'port': subscription.port,
                'price': float(subscription.price),
                'subscription_type': subscription.subscription_type,
                'status': subscription.status,
                'start_date': subscription.start_date.strftime('%Y-%m-%d') if subscription.start_date else None,
                'end_date': subscription.end_date.strftime('%Y-%m-%d') if subscription.end_date else None,
                'provider': {
                    'id': subscription.provider_id,
                    'name': subscription.provider.name if subscription.provider else 'شركة محمد الجبوري',
                    'logo_url': getattr(subscription.provider, 'logo_url', None) if subscription.provider else None
                },
                'days_remaining': subscription.days_remaining,
                'is_expired': subscription.is_expired,
                'is_expiring_soon': subscription.is_expiring_soon,
                'total_paid': subscription.total_paid,
                'remaining_amount': subscription.remaining_amount
            }
            subscriptions_data.append(subscription_dict)

        # حساب الإحصائيات
        total_subscriptions = len(subscriptions)
        active_count = len([s for s in subscriptions if s.status == 'active'])
        suspended_count = len([s for s in subscriptions if s.status == 'suspended'])
        expired_count = len([s for s in subscriptions if s.status == 'expired'])

        # حساب إجمالي التكلفة الشهرية
        monthly_cost = sum([
            s.price if s.subscription_type == 'monthly'
            else s.price / 6 if s.subscription_type == 'semi_annual'
            else s.price / 12 if s.subscription_type == 'annual'
            else 0
            for s in subscriptions if s.status == 'active'
        ])

        # جلب المزودين للفلاتر
        providers = CloudProvider.query.all()

        return render_template('subscriptions.html',
                             subscriptions=subscriptions_data,
                             total_subscriptions=total_subscriptions,
                             active_count=active_count,
                             suspended_count=suspended_count,
                             expired_count=expired_count,
                             monthly_cost=monthly_cost,
                             providers=providers,
                             today=datetime.now().date())
    except Exception as e:
        app.logger.error(f'خطأ في تحميل صفحة الاشتراكات: {str(e)}')
        flash('حدث خطأ في تحميل الاشتراكات', 'error')
        return redirect(url_for('dashboard'))

@app.route('/subscriptions/add', methods=['GET', 'POST'])
@login_required
def add_subscription():
    """إضافة اشتراك جديد محدث"""
    if request.method == 'POST':
        try:
            # إنشاء الاشتراك الجديد
            subscription = Subscription(
                name=request.form['name'],
                provider_id=request.form.get('provider_id') if request.form.get('provider_id') else None,
                api_key=request.form.get('api_key', ''),
                port=request.form['port'],
                cloud_ip=request.form['cloud_ip'],
                cloud_name=request.form['cloud_name'],
                customer_email=request.form.get('customer_email', ''),
                customer_name=request.form['customer_name'],
                subscription_type=request.form['subscription_type'],
                price=float(request.form['price']),
                start_date=datetime.strptime(request.form['start_date'], '%Y-%m-%d').date(),
                end_date=datetime.strptime(request.form['end_date'], '%Y-%m-%d').date(),
                status=request.form.get('status', 'active'),
                accounting_status=request.form.get('accounting_status', 'unpaid')
            )

            db.session.add(subscription)
            db.session.flush()  # للحصول على ID الاشتراك

            # إضافة دفعة أولى إذا كان الاشتراك مدفوع
            if request.form.get('accounting_status') == 'paid':
                payment = Payment(
                    subscription_id=subscription.id,
                    amount=float(request.form['price']),
                    payment_date=datetime.strptime(request.form['start_date'], '%Y-%m-%d').date(),
                    payment_method='cash',
                    status='completed',
                    notes='دفعة أولى عند إنشاء الاشتراك'
                )
                db.session.add(payment)

            db.session.commit()

            # تسجيل النشاط
            log_activity(
                user_id=session['user_id'],
                action='add_subscription',
                details=f'تم إضافة اشتراك جديد: {subscription.name} للعميل: {subscription.customer_name}'
            )

            flash('تم إضافة الاشتراك بنجاح', 'success')
            return redirect(url_for('subscriptions'))

        except Exception as e:
            db.session.rollback()
            flash(f'خطأ في إضافة الاشتراك: {str(e)}', 'error')

    providers = CloudProvider.query.all()
    return render_template('add_subscription_new.html',
                         providers=providers,
                         today=datetime.now().date())

@app.route('/subscriptions/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_subscription(id):
    """تعديل اشتراك"""
    subscription = Subscription.query.get_or_404(id)
    
    if request.method == 'POST':
        try:
            subscription.name = request.form['name']
            subscription.provider_id = request.form['provider_id']
            subscription.api_key = request.form['api_key']
            subscription.port = request.form['port']
            subscription.cloud_ip = request.form.get('cloud_ip', '')
            subscription.cloud_name = request.form.get('cloud_name', '')
            subscription.customer_email = request.form.get('customer_email', '')
            subscription.customer_name = request.form.get('customer_name', '')
            subscription.subscription_type = request.form['subscription_type']
            subscription.price = float(request.form['price'])
            subscription.start_date = datetime.strptime(request.form['start_date'], '%Y-%m-%d').date()
            subscription.end_date = datetime.strptime(request.form['end_date'], '%Y-%m-%d').date()
            subscription.status = request.form['status']
            subscription.accounting_status = request.form['accounting_status']
            subscription.updated_at = datetime.utcnow()
            
            db.session.commit()
            flash('تم تحديث الاشتراك بنجاح', 'success')
            return redirect(url_for('subscriptions'))

        except Exception as e:
            db.session.rollback()
            flash(f'خطأ في تحديث الاشتراك: {str(e)}', 'error')

    providers = CloudProvider.query.all()
    return render_template('add_subscription.html', providers=providers)

@app.route('/subscriptions/export/pdf')
@login_required
def export_subscriptions_pdf():
    """تصدير الاشتراكات إلى PDF"""
    try:
        # إنشاء buffer للـ PDF
        buffer = io.BytesIO()

        # إنشاء مستند PDF
        doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72,
                              topMargin=72, bottomMargin=18)

        # قائمة العناصر
        elements = []

        # العنوان
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=1,  # Center alignment
            textColor=colors.HexColor('#1f2937')
        )

        title = Paragraph("تقرير الاشتراكات", title_style)
        elements.append(title)

        # معلومات التقرير
        info_style = ParagraphStyle(
            'InfoStyle',
            parent=styles['Normal'],
            fontSize=10,
            spaceAfter=20,
            alignment=1
        )

        report_info = Paragraph(
            f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}<br/>"
            f"المهندس محمد ياسر الجبيري<br/>"
            f"نظام إدارة الاشتراكات",
            info_style
        )
        elements.append(report_info)
        elements.append(Spacer(1, 20))

        # جلب البيانات
        subscriptions = Subscription.query.all()

        # إنشاء الجدول
        data = [['#', 'اسم الاشتراك', 'المزود', 'النوع', 'السعر', 'الحالة', 'تاريخ الانتهاء']]

        for i, sub in enumerate(subscriptions, 1):
            data.append([
                str(i),
                sub.name[:20] + '...' if len(sub.name) > 20 else sub.name,
                sub.provider.name[:15] + '...' if len(sub.provider.name) > 15 else sub.provider.name,
                'شهري' if sub.subscription_type == 'monthly' else
                'نصف سنوي' if sub.subscription_type == 'semi_annual' else 'سنوي',
                f"${sub.price:.2f}",
                'نشط' if sub.status == 'active' else
                'معلق' if sub.status == 'suspended' else 'منتهي',
                sub.end_date.strftime('%Y-%m-%d')
            ])

        # تنسيق الجدول
        table = Table(data, colWidths=[0.5*inch, 2*inch, 1.5*inch, 1*inch, 0.8*inch, 0.8*inch, 1*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#3b82f6')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))

        elements.append(table)
        elements.append(Spacer(1, 20))

        # إحصائيات
        total_subs = len(subscriptions)
        active_subs = len([s for s in subscriptions if s.status == 'active'])
        total_cost = sum([s.price for s in subscriptions])

        stats_data = [
            ['إجمالي الاشتراكات', str(total_subs)],
            ['الاشتراكات النشطة', str(active_subs)],
            ['إجمالي التكلفة الشهرية', f"${total_cost:.2f}"],
            ['التكلفة السنوية المتوقعة', f"${total_cost * 12:.2f}"]
        ]

        stats_table = Table(stats_data, colWidths=[3*inch, 2*inch])
        stats_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.HexColor('#f3f4f6')),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ]))

        elements.append(Paragraph("ملخص الإحصائيات", styles['Heading2']))
        elements.append(Spacer(1, 10))
        elements.append(stats_table)

        # بناء PDF
        doc.build(elements)

        # إرجاع الملف
        buffer.seek(0)
        response = make_response(buffer.getvalue())
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'attachment; filename=subscriptions_report_{datetime.now().strftime("%Y%m%d_%H%M")}.pdf'

        return response

    except Exception as e:
        flash(f'خطأ في تصدير PDF: {str(e)}', 'error')
        return redirect(url_for('subscriptions'))

@app.route('/subscriptions/<int:id>/invoices')
@login_required
def subscription_invoices(id):
    """عرض فواتير اشتراك معين"""
    subscription = Subscription.query.get_or_404(id)
    page = request.args.get('page', 1, type=int)

    invoices = Invoice.query.filter_by(subscription_id=id).order_by(
        Invoice.created_at.desc()
    ).paginate(page=page, per_page=10, error_out=False)

    return render_template('subscription_invoices.html',
                         subscription=subscription,
                         invoices=invoices,
                         today=datetime.now().date())

@app.route('/subscriptions/<int:id>/create-invoice', methods=['POST'])
@login_required
def create_invoice(id):
    """إنشاء فاتورة جديدة لاشتراك"""
    subscription = Subscription.query.get_or_404(id)

    try:
        # إنشاء رقم فاتورة فريد
        invoice_count = Invoice.query.count() + 1
        invoice_number = f"INV-{datetime.now().strftime('%Y%m')}-{invoice_count:04d}"

        # إنشاء الفاتورة
        invoice = Invoice(
            subscription_id=subscription.id,
            invoice_number=invoice_number,
            amount=subscription.price,
            issue_date=datetime.now().date(),
            due_date=datetime.now().date() + timedelta(days=30),
            status='pending'
        )

        db.session.add(invoice)
        db.session.commit()

        flash(f'تم إنشاء الفاتورة {invoice_number} بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'خطأ في إنشاء الفاتورة: {str(e)}', 'error')

    return redirect(url_for('subscription_invoices', id=id))

@app.route('/subscriptions/<int:id>/delete', methods=['POST'])
@login_required
def delete_subscription(id):
    """حذف اشتراك"""
    subscription = Subscription.query.get_or_404(id)

    try:
        db.session.delete(subscription)
        db.session.commit()
        flash('تم حذف الاشتراك بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'خطأ في حذف الاشتراك: {str(e)}', 'error')

    return redirect(url_for('subscriptions'))

# API Routes
@app.route('/api/stats')
@login_required
def api_stats():
    """API للحصول على الإحصائيات"""
    stats = {
        'total_subscriptions': Subscription.query.count(),
        'active_subscriptions': Subscription.query.filter_by(status='active').count(),
        'pending_invoices': Invoice.query.filter_by(status='pending').count(),
        'expired_subscriptions': Subscription.query.filter_by(status='expired').count(),
        'total_revenue': db.session.query(db.func.sum(Invoice.amount)).filter_by(status='paid').scalar() or 0
    }
    return jsonify(stats)

@app.route('/api/chart-data')
@login_required
def api_chart_data():
    """API لبيانات الرسم البياني"""
    chart_data = []
    months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو']

    for i, month in enumerate(months):
        # محاكاة البيانات
        count = 65 + (i * 10) + (i % 3 * 5)
        revenue = count * 150  # متوسط السعر

        chart_data.append({
            'month': month,
            'subscriptions': count,
            'revenue': revenue / 1000  # بالآلاف
        })

    return jsonify(chart_data)

# صفحات الإعدادات
@app.route('/settings')
@login_required
def settings():
    """صفحة الإعدادات الرئيسية"""
    return render_template('settings/index.html')

@app.route('/settings/general', methods=['GET', 'POST'])
@admin_required
def settings_general():
    """الإعدادات العامة"""
    if request.method == 'POST':
        settings_data = {
            'company_name': request.form.get('company_name'),
            'company_email': request.form.get('company_email'),
            'currency': request.form.get('currency'),
            'timezone': request.form.get('timezone'),
            'language': request.form.get('language'),
            'date_format': request.form.get('date_format'),
            'notifications_enabled': 'notifications_enabled' in request.form,
            'email_notifications': 'email_notifications' in request.form,
            'backup_enabled': 'backup_enabled' in request.form,
            'backup_frequency': request.form.get('backup_frequency')
        }

        for key, value in settings_data.items():
            setting = SystemSettings.query.filter_by(key=key).first()
            if setting:
                setting.value = str(value)
                setting.updated_at = datetime.utcnow()
            else:
                setting = SystemSettings(key=key, value=str(value), category='general')
                db.session.add(setting)

        db.session.commit()
        flash('تم حفظ الإعدادات العامة بنجاح', 'success')
        return redirect(url_for('settings_general'))

    # جلب الإعدادات الحالية
    settings_dict = {}
    settings = SystemSettings.query.filter_by(category='general').all()
    for setting in settings:
        settings_dict[setting.key] = setting.value

    return render_template('settings/general.html', settings=settings_dict)

@app.route('/settings/users')
@admin_required
def settings_users():
    """إدارة المستخدمين"""
    page = request.args.get('page', 1, type=int)
    users = User.query.order_by(User.created_at.desc()).paginate(
        page=page, per_page=10, error_out=False
    )
    return render_template('settings/users.html', users=users)

@app.route('/settings/users/add', methods=['GET', 'POST'])
@admin_required
def add_user():
    """إضافة مستخدم جديد"""
    if request.method == 'POST':
        try:
            # التحقق من عدم وجود المستخدم
            existing_user = User.query.filter(
                (User.username == request.form['username']) |
                (User.email == request.form['email'])
            ).first()

            if existing_user:
                flash('اسم المستخدم أو البريد الإلكتروني موجود بالفعل', 'error')
                return render_template('settings/add_user.html')

            user = User(
                username=request.form['username'],
                email=request.form['email'],
                role=request.form['role'],
                is_active='is_active' in request.form
            )
            user.set_password(request.form['password'])

            db.session.add(user)
            db.session.commit()

            # إنشاء إشعار للمستخدم الجديد
            notification = Notification(
                user_id=user.id,
                title='مرحباً بك في النظام',
                message=f'تم إنشاء حسابك بنجاح. مرحباً بك {user.username}!',
                type='success'
            )
            db.session.add(notification)
            db.session.commit()

            flash(f'تم إضافة المستخدم {user.username} بنجاح', 'success')
            return redirect(url_for('settings_users'))

        except Exception as e:
            db.session.rollback()
            flash(f'خطأ في إضافة المستخدم: {str(e)}', 'error')

    return render_template('settings/add_user.html')

@app.route('/settings/users/<int:id>/edit', methods=['GET', 'POST'])
@admin_required
def edit_user(id):
    """تعديل مستخدم"""
    user = User.query.get_or_404(id)

    if request.method == 'POST':
        try:
            # التحقق من عدم تضارب البيانات
            existing_user = User.query.filter(
                User.id != id,
                (User.username == request.form['username']) |
                (User.email == request.form['email'])
            ).first()

            if existing_user:
                flash('اسم المستخدم أو البريد الإلكتروني موجود بالفعل', 'error')
                return render_template('settings/edit_user.html', user=user)

            user.username = request.form['username']
            user.email = request.form['email']
            user.role = request.form['role']
            user.is_active = 'is_active' in request.form

            # تغيير كلمة المرور إذا تم إدخال واحدة جديدة
            if request.form.get('password'):
                user.set_password(request.form['password'])

            db.session.commit()
            flash(f'تم تحديث بيانات المستخدم {user.username} بنجاح', 'success')
            return redirect(url_for('settings_users'))

        except Exception as e:
            db.session.rollback()
            flash(f'خطأ في تحديث المستخدم: {str(e)}', 'error')

    return render_template('settings/edit_user.html', user=user)

@app.route('/settings/users/<int:id>/delete', methods=['POST'])
@admin_required
def delete_user(id):
    """حذف مستخدم"""
    user = User.query.get_or_404(id)

    # منع حذف المستخدم الحالي
    if user.id == session['user_id']:
        flash('لا يمكنك حذف حسابك الخاص', 'error')
        return redirect(url_for('settings_users'))

    try:
        db.session.delete(user)
        db.session.commit()
        flash(f'تم حذف المستخدم {user.username} بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'خطأ في حذف المستخدم: {str(e)}', 'error')

    return redirect(url_for('settings_users'))

# صفحات الإشعارات
@app.route('/notifications')
@login_required
def notifications():
    """صفحة الإشعارات"""
    page = request.args.get('page', 1, type=int)
    notifications = Notification.query.filter_by(user_id=session['user_id']).order_by(
        Notification.created_at.desc()
    ).paginate(page=page, per_page=10, error_out=False)

    return render_template('notifications.html', notifications=notifications)

@app.route('/notifications/<int:id>/read', methods=['POST'])
@login_required
def mark_notification_read(id):
    """تحديد الإشعار كمقروء"""
    notification = Notification.query.filter_by(
        id=id, user_id=session['user_id']
    ).first_or_404()

    notification.is_read = True
    db.session.commit()

    return jsonify({'success': True})

@app.route('/api/notifications/unread')
@login_required
def api_unread_notifications():
    """API للحصول على عدد الإشعارات غير المقروءة"""
    count = Notification.query.filter_by(
        user_id=session['user_id'], is_read=False
    ).count()

    recent_notifications = Notification.query.filter_by(
        user_id=session['user_id'], is_read=False
    ).order_by(Notification.created_at.desc()).limit(5).all()

    notifications_data = []
    for notif in recent_notifications:
        notifications_data.append({
            'id': notif.id,
            'title': notif.title,
            'message': notif.message,
            'type': notif.type,
            'created_at': notif.created_at.strftime('%Y-%m-%d %H:%M')
        })

    return jsonify({
        'count': count,
        'notifications': notifications_data
    })

@app.route('/api/notifications/create', methods=['POST'])
@login_required
def api_create_notification():
    """API لإنشاء إشعار جديد"""
    try:
        data = request.get_json()

        notification = Notification(
            user_id=session['user_id'],
            title=data.get('title', 'إشعار جديد'),
            message=data.get('message', ''),
            type=data.get('type', 'info')
        )

        db.session.add(notification)
        db.session.commit()

        return jsonify({
            'success': True,
            'notification': {
                'id': notification.id,
                'title': notification.title,
                'message': notification.message,
                'type': notification.type,
                'created_at': notification.created_at.strftime('%Y-%m-%d %H:%M')
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 400

@app.route('/api/notifications/mark-all-read', methods=['POST'])
@login_required
def api_mark_all_notifications_read():
    """API لتحديد جميع الإشعارات كمقروءة"""
    try:
        Notification.query.filter_by(
            user_id=session['user_id'], is_read=False
        ).update({'is_read': True})

        db.session.commit()

        return jsonify({'success': True, 'message': 'تم تحديد جميع الإشعارات كمقروءة'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 400

@app.route('/api/notifications/delete/<int:notification_id>', methods=['DELETE'])
@login_required
def api_delete_notification(notification_id):
    """API لحذف إشعار"""
    try:
        notification = Notification.query.filter_by(
            id=notification_id, user_id=session['user_id']
        ).first_or_404()

        db.session.delete(notification)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم حذف الإشعار بنجاح'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 400

@app.route('/subscriptions/<int:id>/details')
@login_required
def subscription_details(id):
    """الحصول على تفاصيل اشتراك للعرض في modal"""
    subscription = Subscription.query.get_or_404(id)

    type_mapping = {
        'monthly': 'شهري',
        'semi_annual': 'نصف سنوي',
        'annual': 'سنوي'
    }

    return jsonify({
        'success': True,
        'subscription': {
            'id': subscription.id,
            'name': subscription.name,
            'provider_name': subscription.provider.name,
            'price': float(subscription.price),
            'type_ar': type_mapping.get(subscription.subscription_type, subscription.subscription_type),
            'start_date': subscription.start_date.strftime('%Y-%m-%d'),
            'end_date': subscription.end_date.strftime('%Y-%m-%d'),
            'api_key': subscription.api_key,
            'port': subscription.port,
            'cloud_ip': subscription.cloud_ip,
            'cloud_name': subscription.cloud_name,
            'status': subscription.status,
            'accounting_status': subscription.accounting_status
        }
    })

@app.route('/subscriptions/<int:id>/duplicate', methods=['POST'])
@login_required
def duplicate_subscription(id):
    """نسخ اشتراك موجود"""
    try:
        original = Subscription.query.get_or_404(id)

        # إنشاء نسخة جديدة
        new_subscription = Subscription(
            name=f"{original.name} - نسخة",
            provider_id=original.provider_id,
            api_key=f"{original.api_key}_copy",
            port=original.port,
            cloud_ip=original.cloud_ip,
            cloud_name=f"{original.cloud_name} - نسخة" if original.cloud_name else None,
            subscription_type=original.subscription_type,
            price=original.price,
            start_date=datetime.now().date(),
            end_date=datetime.now().date() + timedelta(days=30),
            status='active',
            accounting_status='unpaid'
        )

        db.session.add(new_subscription)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم نسخ الاشتراك بنجاح'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'خطأ في نسخ الاشتراك: {str(e)}'})

@app.route('/subscriptions/<int:id>/renew', methods=['POST'])
@login_required
def renew_subscription(id):
    """تجديد اشتراك"""
    try:
        subscription = Subscription.query.get_or_404(id)

        # حساب تاريخ الانتهاء الجديد
        if subscription.subscription_type == 'monthly':
            new_end_date = subscription.end_date + timedelta(days=30)
        elif subscription.subscription_type == 'semi_annual':
            new_end_date = subscription.end_date + timedelta(days=180)
        elif subscription.subscription_type == 'annual':
            new_end_date = subscription.end_date + timedelta(days=365)
        else:
            new_end_date = subscription.end_date + timedelta(days=30)

        subscription.end_date = new_end_date
        subscription.status = 'active'
        subscription.updated_at = datetime.utcnow()

        # إنشاء فاتورة جديدة للتجديد
        invoice_count = Invoice.query.count() + 1
        invoice_number = f"INV-{datetime.now().strftime('%Y%m')}-{invoice_count:04d}"

        invoice = Invoice(
            subscription_id=subscription.id,
            invoice_number=invoice_number,
            amount=subscription.price,
            issue_date=datetime.now().date(),
            due_date=datetime.now().date() + timedelta(days=30),
            status='pending'
        )

        db.session.add(invoice)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم تجديد الاشتراك بنجاح'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'خطأ في تجديد الاشتراك: {str(e)}'})

@app.route('/api/subscriptions/count')
@login_required
def api_subscriptions_count():
    """API للحصول على عدد الاشتراكات"""
    total = Subscription.query.count()
    active = Subscription.query.filter_by(status='active').count()
    expired = Subscription.query.filter_by(status='expired').count()

    return jsonify({
        'total': total,
        'active': active,
        'expired': expired,
        'timestamp': datetime.now().isoformat()
    })

# تشغيل التطبيق
# الروتات الجديدة للمهندس محمد ياسر الجبوري

@app.route('/subscription_chart')
@login_required
def subscription_chart():
    """صفحة مخطط الاشتراكات التفاعلي"""
    # إحصائيات عامة
    total_subscriptions = Subscription.query.count()
    active_subscriptions = Subscription.query.filter_by(status='active').count()
    suspended_subscriptions = Subscription.query.filter_by(status='suspended').count()
    expired_subscriptions = Subscription.query.filter_by(status='expired').count()

    # الاشتراكات المنتهية قريباً (خلال 30 يوم)
    expiring_soon = Subscription.query.filter(
        Subscription.end_date <= datetime.now().date() + timedelta(days=30),
        Subscription.status == 'active'
    ).count()

    # إجمالي التكلفة
    total_cost = db.session.query(db.func.sum(Subscription.price)).scalar() or 0

    # إحصائيات المزودين
    provider_stats = db.session.query(
        CloudProvider.name,
        db.func.count(Subscription.id).label('count')
    ).join(Subscription).group_by(CloudProvider.name).all()

    provider_names = [stat[0] for stat in provider_stats]
    provider_counts = [stat[1] for stat in provider_stats]

    # جلب جميع الاشتراكات للجدول
    subscriptions = Subscription.query.all()

    return render_template('subscription_chart.html',
                         total_subscriptions=total_subscriptions,
                         active_subscriptions=active_subscriptions,
                         suspended_subscriptions=suspended_subscriptions,
                         expired_subscriptions=expired_subscriptions,
                         expiring_soon=expiring_soon,
                         total_cost=total_cost,
                         provider_names=provider_names,
                         provider_counts=provider_counts,
                         subscriptions=subscriptions)

@app.route('/send_email', methods=['GET', 'POST'])
@login_required
def send_email():
    """صفحة إرسال الرسائل الإلكترونية"""
    if request.method == 'POST':
        try:
            data = request.get_json()

            # معالجة البيانات وإرسال الإيميل
            recipient_type = data.get('recipient_type')
            subject = data.get('subject')
            content = data.get('content')

            if recipient_type == 'subscription':
                subscription_id = data.get('subscription_id')
                subscription = Subscription.query.get(subscription_id)
                if subscription and subscription.customer_email:
                    # استبدال المتغيرات في المحتوى
                    content = content.replace('{name}', subscription.name)
                    content = content.replace('{customer_name}', subscription.customer_name or 'عزيزي العميل')
                    content = content.replace('{price}', str(subscription.price))
                    content = content.replace('{end_date}', subscription.end_date.strftime('%Y-%m-%d'))
                    content = content.replace('{cloud_name}', subscription.cloud_name or 'غير محدد')
                    content = content.replace('{cloud_ip}', subscription.cloud_ip or 'غير محدد')

                    # إرسال الإيميل الفعلي
                    success = send_actual_email(subscription.customer_email, subject, content)

                    if success:
                        return jsonify({'success': True, 'message': f'تم إرسال الرسالة بنجاح إلى {subscription.customer_email}'})
                    else:
                        return jsonify({'success': False, 'message': 'فشل في إرسال الرسالة'})
                else:
                    return jsonify({'success': False, 'message': 'لا يوجد إيميل محدد لهذا الاشتراك'})

            elif recipient_type == 'all_active':
                # إرسال لجميع العملاء النشطين
                active_subscriptions = Subscription.query.filter_by(status='active').all()
                sent_count = 0
                failed_count = 0

                for subscription in active_subscriptions:
                    if subscription.customer_email:
                        # استبدال المتغيرات
                        personalized_content = content.replace('{name}', subscription.name)
                        personalized_content = personalized_content.replace('{customer_name}', subscription.customer_name or 'عزيزي العميل')
                        personalized_content = personalized_content.replace('{price}', str(subscription.price))
                        personalized_content = personalized_content.replace('{end_date}', subscription.end_date.strftime('%Y-%m-%d'))
                        personalized_content = personalized_content.replace('{cloud_name}', subscription.cloud_name or 'غير محدد')
                        personalized_content = personalized_content.replace('{cloud_ip}', subscription.cloud_ip or 'غير محدد')

                        # إرسال الإيميل
                        if send_actual_email(subscription.customer_email, subject, personalized_content):
                            sent_count += 1
                        else:
                            failed_count += 1

                if sent_count > 0:
                    message = f'تم إرسال {sent_count} رسالة بنجاح'
                    if failed_count > 0:
                        message += f' وفشل إرسال {failed_count} رسالة'
                    return jsonify({'success': True, 'message': message})
                else:
                    return jsonify({'success': False, 'message': 'لم يتم إرسال أي رسالة'})

            elif recipient_type == 'custom':
                custom_email = data.get('custom_email')
                if custom_email:
                    # إرسال لإيميل مخصص
                    # send_actual_email(custom_email, subject, content)
                    return jsonify({'success': True, 'message': 'تم إرسال الرسالة بنجاح'})

            return jsonify({'success': False, 'message': 'بيانات غير صحيحة'})

        except Exception as e:
            return jsonify({'success': False, 'message': f'خطأ في الإرسال: {str(e)}'})

    # GET request - عرض الصفحة
    subscriptions = Subscription.query.all()

    # إحصائيات الإرسال (يمكن إضافة جدول لتتبع الرسائل المرسلة)
    sent_today = 0  # يمكن حسابها من قاعدة البيانات
    sent_this_month = 0
    total_sent = 0

    return render_template('send_email.html',
                         subscriptions=subscriptions,
                         sent_today=sent_today,
                         sent_this_month=sent_this_month,
                         total_sent=total_sent)

@app.route('/email_center')
@login_required
def email_center():
    """مركز الرسائل الإلكترونية"""
    return render_template('email_center.html')

@app.route('/email_templates')
@login_required
def email_templates():
    """صفحة قوالب الرسائل"""
    return render_template('email_templates.html')

@app.route('/subscription_analytics')
@login_required
def subscription_analytics():
    """صفحة تحليلات الاشتراكات"""
    return render_template('subscription_analytics.html')

@app.route('/subscription_reports')
@login_required
def subscription_reports():
    """صفحة تقارير الاشتراكات"""
    return render_template('subscription_reports.html')

@app.route('/advanced_reports')
@login_required
def advanced_reports():
    """صفحة التقارير المتقدمة"""
    return render_template('advanced_reports.html')

@app.route('/ai_insights')
@login_required
def ai_insights():
    """صفحة الرؤى الذكية"""
    try:
        from ai_analytics import AIAnalytics
        ai = AIAnalytics()

        # الحصول على التحليلات الذكية
        revenue_prediction = ai.revenue_prediction(6)
        churn_analysis = ai.churn_analysis()
        pricing_optimization = ai.pricing_optimization()
        customer_segmentation = ai.customer_segmentation()
        anomaly_detection = ai.anomaly_detection()

        return render_template('ai_insights.html',
                             revenue_prediction=revenue_prediction,
                             churn_analysis=churn_analysis,
                             pricing_optimization=pricing_optimization,
                             customer_segmentation=customer_segmentation,
                             anomaly_detection=anomaly_detection)
    except Exception as e:
        app.logger.error(f'خطأ في تحميل الرؤى الذكية: {str(e)}')
        flash('خطأ في تحميل الرؤى الذكية', 'error')
        return redirect(url_for('dashboard'))

@app.route('/api/ai/revenue-prediction')
@login_required
def api_revenue_prediction():
    """API للتنبؤ بالإيرادات"""
    try:
        from ai_analytics import AIAnalytics
        ai = AIAnalytics()
        months = request.args.get('months', 6, type=int)
        prediction = ai.revenue_prediction(months)
        return jsonify(prediction)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/ai/churn-analysis')
@login_required
def api_churn_analysis():
    """API لتحليل الإلغاء"""
    try:
        from ai_analytics import AIAnalytics
        ai = AIAnalytics()
        analysis = ai.churn_analysis()
        return jsonify(analysis)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/subscriptions/<int:subscription_id>/renew', methods=['POST'])
@login_required
def api_renew_subscription(subscription_id):
    """API لتجديد اشتراك"""
    try:
        subscription = Subscription.query.get_or_404(subscription_id)

        # تحديث تاريخ الانتهاء حسب نوع الاشتراك
        if subscription.subscription_type == 'monthly':
            new_end_date = subscription.end_date + timedelta(days=30)
        elif subscription.subscription_type == 'semi_annual':
            new_end_date = subscription.end_date + timedelta(days=180)
        elif subscription.subscription_type == 'annual':
            new_end_date = subscription.end_date + timedelta(days=365)
        else:
            new_end_date = subscription.end_date + timedelta(days=30)

        subscription.end_date = new_end_date
        subscription.status = 'active'

        db.session.commit()

        # تسجيل النشاط
        log_activity(
            user_id=session['user_id'],
            action='renew_subscription',
            details=f'تم تجديد الاشتراك: {subscription.name}'
        )

        return jsonify({
            'success': True,
            'message': 'تم تجديد الاشتراك بنجاح',
            'new_end_date': new_end_date.strftime('%Y-%m-%d')
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/subscriptions/bulk-renew', methods=['POST'])
@login_required
def api_bulk_renew_subscriptions():
    """API لتجديد اشتراكات متعددة"""
    try:
        data = request.get_json()
        subscription_ids = data.get('subscription_ids', [])

        if not subscription_ids:
            return jsonify({'success': False, 'error': 'لم يتم تحديد اشتراكات'}), 400

        subscriptions = Subscription.query.filter(Subscription.id.in_(subscription_ids)).all()
        renewed_count = 0

        for subscription in subscriptions:
            # تحديث تاريخ الانتهاء
            if subscription.subscription_type == 'monthly':
                new_end_date = subscription.end_date + timedelta(days=30)
            elif subscription.subscription_type == 'semi_annual':
                new_end_date = subscription.end_date + timedelta(days=180)
            elif subscription.subscription_type == 'annual':
                new_end_date = subscription.end_date + timedelta(days=365)
            else:
                new_end_date = subscription.end_date + timedelta(days=30)

            subscription.end_date = new_end_date
            subscription.status = 'active'
            renewed_count += 1

        db.session.commit()

        # تسجيل النشاط
        log_activity(
            user_id=session['user_id'],
            action='bulk_renew_subscriptions',
            details=f'تم تجديد {renewed_count} اشتراك'
        )

        return jsonify({
            'success': True,
            'renewed_count': renewed_count,
            'message': f'تم تجديد {renewed_count} اشتراك بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/subscriptions/bulk-suspend', methods=['POST'])
@login_required
def api_bulk_suspend_subscriptions():
    """API لتعليق اشتراكات متعددة"""
    try:
        data = request.get_json()
        subscription_ids = data.get('subscription_ids', [])

        if not subscription_ids:
            return jsonify({'success': False, 'error': 'لم يتم تحديد اشتراكات'}), 400

        subscriptions = Subscription.query.filter(Subscription.id.in_(subscription_ids)).all()
        suspended_count = 0

        for subscription in subscriptions:
            subscription.status = 'suspended'
            suspended_count += 1

        db.session.commit()

        # تسجيل النشاط
        log_activity(
            user_id=session['user_id'],
            action='bulk_suspend_subscriptions',
            details=f'تم تعليق {suspended_count} اشتراك'
        )

        return jsonify({
            'success': True,
            'suspended_count': suspended_count,
            'message': f'تم تعليق {suspended_count} اشتراك بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/subscriptions/bulk-delete', methods=['DELETE'])
@login_required
def api_bulk_delete_subscriptions():
    """API لحذف اشتراكات متعددة"""
    try:
        data = request.get_json()
        subscription_ids = data.get('subscription_ids', [])

        if not subscription_ids:
            return jsonify({'success': False, 'error': 'لم يتم تحديد اشتراكات'}), 400

        subscriptions = Subscription.query.filter(Subscription.id.in_(subscription_ids)).all()
        deleted_count = len(subscriptions)

        for subscription in subscriptions:
            db.session.delete(subscription)

        db.session.commit()

        # تسجيل النشاط
        log_activity(
            user_id=session['user_id'],
            action='bulk_delete_subscriptions',
            details=f'تم حذف {deleted_count} اشتراك'
        )

        return jsonify({
            'success': True,
            'deleted_count': deleted_count,
            'message': f'تم حذف {deleted_count} اشتراك بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/subscriptions/<int:subscription_id>', methods=['DELETE'])
@login_required
def api_delete_subscription(subscription_id):
    """API لحذف اشتراك واحد"""
    try:
        subscription = Subscription.query.get_or_404(subscription_id)
        subscription_name = subscription.name

        db.session.delete(subscription)
        db.session.commit()

        # تسجيل النشاط
        log_activity(
            user_id=session['user_id'],
            action='delete_subscription',
            details=f'تم حذف الاشتراك: {subscription_name}'
        )

        return jsonify({
            'success': True,
            'message': 'تم حذف الاشتراك بنجاح'
        })

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/invoices')
@login_required
def invoices():
    """صفحة إدارة الفواتير"""
    return render_template('invoices.html')

@app.route('/add_invoice')
@login_required
def add_invoice():
    """صفحة إضافة فاتورة جديدة"""
    return render_template('add_invoice.html')

@app.route('/invoice_reports')
@login_required
def invoice_reports():
    """صفحة تقارير الفواتير"""
    return render_template('invoice_reports.html')

@app.route('/reports')
@login_required
def reports():
    """صفحة التقارير العامة"""
    return render_template('reports.html')

@app.route('/users')
@admin_required
def users():
    """صفحة إدارة المستخدمين"""
    return render_template('users.html')

# دالة مساعدة لإرسال الإيميل الفعلي
def send_actual_email(to_email, subject, content):
    """
    دالة إرسال الإيميل الفعلي باستخدام Flask-Mail
    """
    try:
        if MAIL_AVAILABLE and mail:
            # إنشاء رسالة الإيميل باستخدام Flask-Mail
            msg = Message(
                subject=subject,
                sender=app.config.get('MAIL_DEFAULT_SENDER', '<EMAIL>'),
                recipients=[to_email],
                body=content
            )

            # محاولة إرسال الإيميل
            # mail.send(msg)  # سيتم تفعيلها عند إعداد SMTP

        # للاختبار - طباعة تفاصيل الإيميل
        print(f"✅ إرسال إيميل إلى: {to_email}")
        print(f"📧 الموضوع: {subject}")
        print(f"📝 المحتوى: {content[:100]}...")
        print("=" * 50)

        return True
    except Exception as e:
        print(f"❌ خطأ في إرسال الإيميل إلى {to_email}: {str(e)}")
        return False

# نظام النسخ الاحتياطي
def create_backup():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    try:
        import shutil
        from datetime import datetime

        # إنشاء مجلد النسخ الاحتياطية
        backup_dir = 'backups'
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)

        # اسم ملف النسخة الاحتياطية
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f'subscription_backup_{timestamp}.db'
        backup_path = os.path.join(backup_dir, backup_filename)

        # نسخ قاعدة البيانات
        shutil.copy2('subscriptions.db', backup_path)

        # حساب حجم الملف
        file_size = os.path.getsize(backup_path)

        # تسجيل النسخة الاحتياطية
        backup_log = BackupLog(
            backup_type='manual',
            file_path=backup_path,
            file_size=file_size,
            status='completed'
        )
        db.session.add(backup_log)
        db.session.commit()

        app.logger.info(f'Backup created successfully: {backup_path}')
        return True, backup_path

    except Exception as e:
        # تسجيل الخطأ
        backup_log = BackupLog(
            backup_type='manual',
            status='failed',
            error_message=str(e)
        )
        db.session.add(backup_log)
        db.session.commit()

        app.logger.error(f'Backup failed: {str(e)}')
        return False, str(e)

@app.route('/admin/backup')
@login_required
@admin_required
def create_backup_route():
    """إنشاء نسخة احتياطية يدوية"""
    success, result = create_backup()

    if success:
        log_activity('backup_created', 'system', None, f'تم إنشاء نسخة احتياطية: {result}')
        flash('تم إنشاء النسخة الاحتياطية بنجاح', 'success')
    else:
        flash(f'فشل في إنشاء النسخة الاحتياطية: {result}', 'error')

    return redirect(url_for('settings'))

@app.route('/admin/activity_logs')
@login_required
@admin_required
def activity_logs():
    """صفحة سجل الأنشطة"""
    page = request.args.get('page', 1, type=int)
    action_filter = request.args.get('action', '')
    user_filter = request.args.get('user', '')

    query = ActivityLog.query

    # تطبيق الفلاتر
    if action_filter:
        query = query.filter(ActivityLog.action.contains(action_filter))
    if user_filter:
        query = query.join(User).filter(User.username.contains(user_filter))

    # ترتيب النتائج
    query = query.order_by(ActivityLog.created_at.desc())

    logs = query.paginate(page=page, per_page=20, error_out=False)

    return render_template('activity_logs.html',
                         logs=logs,
                         action_filter=action_filter,
                         user_filter=user_filter)

@app.route('/admin/system_health')
@login_required
@admin_required
def system_health():
    """صفحة صحة النظام"""
    try:
        import psutil
        PSUTIL_AVAILABLE = True
    except ImportError:
        PSUTIL_AVAILABLE = False

    try:
        if PSUTIL_AVAILABLE:
            # معلومات النظام
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('.')
        else:
            # قيم افتراضية إذا لم يكن psutil متاح
            cpu_percent = 25.0
            memory = type('obj', (object,), {'percent': 60.0, 'used': **********, 'total': **********})
            disk = type('obj', (object,), {'percent': 45.0, 'used': 50000000000, 'total': 100000000000})

        # معلومات قاعدة البيانات
        db_size = os.path.getsize('subscriptions.db') if os.path.exists('subscriptions.db') else 0

        # إحصائيات قاعدة البيانات
        total_users = User.query.count()
        total_subscriptions = Subscription.query.count()
        total_activities = ActivityLog.query.count()

        # آخر النسخ الاحتياطية
        recent_backups = BackupLog.query.order_by(BackupLog.created_at.desc()).limit(5).all()

        system_info = {
            'cpu_percent': cpu_percent,
            'memory_percent': memory.percent,
            'memory_used': memory.used,
            'memory_total': memory.total,
            'disk_percent': disk.percent,
            'disk_used': disk.used,
            'disk_total': disk.total,
            'db_size': db_size,
            'total_users': total_users,
            'total_subscriptions': total_subscriptions,
            'total_invoices': 0,  # سنضيف نموذج Invoice لاحقاً
            'total_activities': total_activities,
            'recent_backups': recent_backups,
            'psutil_available': PSUTIL_AVAILABLE
        }

        return render_template('system_health.html', system_info=system_info)

    except Exception as e:
        flash(f'خطأ في جلب معلومات النظام: {str(e)}', 'error')
        return redirect(url_for('settings'))

# دوال مساعدة للـ templates
@app.template_filter('format_currency')
def format_currency(value):
    """تنسيق العملة"""
    try:
        return f"${float(value):,.2f}"
    except:
        return "$0.00"

@app.template_filter('format_date')
def format_date(value):
    """تنسيق التاريخ"""
    try:
        if hasattr(value, 'strftime'):
            return value.strftime('%Y-%m-%d')
        return str(value)
    except:
        return ""

# دوال مساعدة للـ Jinja2
@app.template_global()
def get_action_class(action):
    """إرجاع CSS class للنشاط"""
    classes = {
        'login': 'badge-status-active',
        'create': 'badge-status-active',
        'update': 'badge-status-suspended',
        'delete': 'badge-status-expired',
        'backup': 'badge-status-active'
    }
    return classes.get(action, 'badge-status-suspended')

@app.template_global()
def get_action_label(action):
    """إرجاع تسمية النشاط"""
    labels = {
        'login': 'تسجيل دخول',
        'create': 'إنشاء',
        'update': 'تحديث',
        'delete': 'حذف',
        'backup': 'نسخ احتياطي'
    }
    return labels.get(action, action)

@app.template_global()
def get_action_icon(action):
    """إرجاع أيقونة النشاط"""
    icons = {
        'login': 'sign-in-alt',
        'create': 'plus',
        'update': 'edit',
        'delete': 'trash',
        'backup': 'save'
    }
    return icons.get(action, 'cog')

# تحسينات الأمان
@app.before_request
def security_headers():
    """إضافة headers أمنية"""
    pass

@app.after_request
def after_request(response):
    """إضافة headers أمنية بعد كل طلب"""
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
    response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
    return response

# معالج الأخطاء
@app.errorhandler(404)
def not_found_error(error):
    """صفحة خطأ 404"""
    return render_template('errors/404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    """صفحة خطأ 500"""
    db.session.rollback()
    app.logger.error(f'Server Error: {error}')
    return render_template('errors/500.html'), 500

@app.errorhandler(403)
def forbidden_error(error):
    """صفحة خطأ 403"""
    return render_template('errors/403.html'), 403

# إعداد Flask-Mail (يمكن تخصيصه حسب الحاجة)
app.config['MAIL_SERVER'] = 'smtp.gmail.com'
app.config['MAIL_PORT'] = 587
app.config['MAIL_USE_TLS'] = True
app.config['MAIL_USERNAME'] = '<EMAIL>'  # يجب تغييره
app.config['MAIL_PASSWORD'] = 'your-app-password'     # يجب تغييره
app.config['MAIL_DEFAULT_SENDER'] = 'نظام إدارة الاشتراكات <<EMAIL>>'

# تهيئة Flask-Mail
if MAIL_AVAILABLE:
    mail = Mail(app)
else:
    mail = None

if __name__ == '__main__':
    with app.app_context():
        db.create_all()

        # تحديث قاعدة البيانات للأعمدة الجديدة
        try:
            from sqlalchemy import text
            with db.engine.connect() as conn:
                # التحقق من وجود الأعمدة الجديدة في جدول المستخدمين
                result = conn.execute(text("PRAGMA table_info(user)"))
                columns = [row[1] for row in result.fetchall()]

                # إضافة الأعمدة المفقودة
                new_columns = [
                    ('first_name', 'VARCHAR(50)'),
                    ('last_name', 'VARCHAR(50)'),
                    ('phone', 'VARCHAR(20)'),
                    ('avatar', 'VARCHAR(200)'),
                    ('role_id', 'INTEGER'),
                    ('department', 'VARCHAR(100)'),
                    ('position', 'VARCHAR(100)'),
                    ('last_login', 'DATETIME'),
                    ('last_activity', 'DATETIME'),
                    ('is_verified', 'BOOLEAN DEFAULT 0'),
                    ('login_attempts', 'INTEGER DEFAULT 0'),
                    ('locked_until', 'DATETIME')
                ]

                for column_name, column_type in new_columns:
                    if column_name not in columns:
                        try:
                            conn.execute(text(f'ALTER TABLE user ADD COLUMN {column_name} {column_type}'))
                            print(f"✅ تم إضافة العمود {column_name} لجدول المستخدمين")
                        except Exception as e:
                            print(f"⚠️ خطأ في إضافة العمود {column_name}: {e}")

                conn.commit()

        except Exception as e:
            print(f"⚠️ خطأ في تحديث قاعدة البيانات: {e}")

        # تحديث قاعدة البيانات للحقول الجديدة
        try:
            # التحقق من وجود الحقول الجديدة وإضافتها إذا لم تكن موجودة
            from sqlalchemy import inspect, text
            inspector = inspect(db.engine)

            # فحص جدول الاشتراكات
            if inspector.has_table('subscription'):
                subscription_columns = [col['name'] for col in inspector.get_columns('subscription')]

                if 'customer_email' not in subscription_columns:
                    with db.engine.connect() as conn:
                        conn.execute(text('ALTER TABLE subscription ADD COLUMN customer_email VARCHAR(120)'))
                        conn.commit()
                    print('✅ Added customer_email column to subscription table')

                if 'customer_name' not in subscription_columns:
                    with db.engine.connect() as conn:
                        conn.execute(text('ALTER TABLE subscription ADD COLUMN customer_name VARCHAR(100)'))
                        conn.commit()
                    print('✅ Added customer_name column to subscription table')

        except Exception as e:
            print(f'⚠️ Database migration warning: {str(e)}')
        
        # إنشاء الأدوار الافتراضية
        if not Role.query.first():
            roles = [
                {
                    'name': 'admin',
                    'display_name': 'مدير النظام',
                    'description': 'صلاحيات كاملة لإدارة النظام',
                    'permissions': [
                        'users.view', 'users.create', 'users.edit', 'users.delete',
                        'subscriptions.view', 'subscriptions.create', 'subscriptions.edit', 'subscriptions.delete',
                        'payments.view', 'payments.create', 'payments.edit', 'payments.delete',
                        'reports.view', 'reports.create', 'settings.view', 'settings.edit',
                        'ai.view', 'ai.manage', 'dashboard.view', 'dashboard.manage',
                        'security.view', 'security.manage', 'security.scan', 'security.config'
                    ],
                    'is_system': True
                },
                {
                    'name': 'manager',
                    'display_name': 'مدير',
                    'description': 'صلاحيات إدارية محدودة',
                    'permissions': [
                        'subscriptions.view', 'subscriptions.create', 'subscriptions.edit',
                        'payments.view', 'payments.create', 'payments.edit',
                        'reports.view', 'dashboard.view', 'security.view'
                    ],
                    'is_system': True
                },
                {
                    'name': 'employee',
                    'display_name': 'موظف',
                    'description': 'صلاحيات أساسية للموظفين',
                    'permissions': [
                        'subscriptions.view', 'subscriptions.create',
                        'payments.view', 'payments.create',
                        'dashboard.view'
                    ],
                    'is_system': True
                }
            ]

            for role_data in roles:
                role = Role(
                    name=role_data['name'],
                    display_name=role_data['display_name'],
                    description=role_data['description'],
                    is_system=role_data['is_system']
                )
                role.set_permissions(role_data['permissions'])
                db.session.add(role)

            db.session.commit()
            print("✅ تم إنشاء الأدوار الافتراضية")

        # إنشاء مستخدم مدير افتراضي
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin_role = Role.query.filter_by(name='admin').first()
            admin = User(
                username='admin',
                email='<EMAIL>',
                first_name='مدير',
                last_name='النظام',
                role_id=admin_role.id if admin_role else None,
                department='تقنية المعلومات',
                position='مدير النظام',
                is_active=True,
                is_verified=True
            )
            admin.set_password('123456')
            db.session.add(admin)
            
            # إضافة مزودي خدمات افتراضيين
            providers = [
                CloudProvider(name='Amazon Web Services', logo_url='/static/img/aws.png'),
                CloudProvider(name='Google Cloud Platform', logo_url='/static/img/gcp.png'),
                CloudProvider(name='Microsoft Azure', logo_url='/static/img/azure.png'),
                CloudProvider(name='DigitalOcean', logo_url='/static/img/do.png'),
                CloudProvider(name='Linode', logo_url='/static/img/linode.png'),
                CloudProvider(name='Vultr', logo_url='/static/img/vultr.png'),
            ]
            
            for provider in providers:
                db.session.add(provider)

            db.session.commit()

            # إضافة اشتراكات تجريبية مع الحقول الجديدة
            subscriptions = [
                Subscription(
                    name='خادم الإنتاج الرئيسي',
                    provider_id=1,  # AWS
                    api_key='aws_key_123456789',
                    port='443',
                    cloud_ip='************',
                    cloud_name='Production-Server-01',
                    customer_name='شركة التقنية المتقدمة',
                    customer_email='<EMAIL>',
                    subscription_type='monthly',
                    price=99.99,
                    start_date=datetime.now().date(),
                    end_date=datetime.now().date() + timedelta(days=30),
                    status='active',
                    accounting_status='paid'
                ),
                Subscription(
                    name='خادم التطوير',
                    provider_id=2,  # GCP
                    api_key='gcp_key_987654321',
                    port='8080',
                    cloud_ip='************',
                    cloud_name='Development-Server',
                    customer_name='مؤسسة الابتكار الرقمي',
                    customer_email='<EMAIL>',
                    subscription_type='annual',
                    price=1200.00,
                    start_date=datetime.now().date() - timedelta(days=100),
                    end_date=datetime.now().date() + timedelta(days=265),
                    status='active',
                    accounting_status='unpaid'
                ),
                Subscription(
                    name='خادم النسخ الاحتياطي',
                    provider_id=3,  # Azure
                    api_key='azure_key_456789123',
                    port='22',
                    cloud_ip='***********',
                    cloud_name='Backup-Server-Azure',
                    customer_name='شركة الحلول الذكية',
                    customer_email='<EMAIL>',
                    subscription_type='semi_annual',
                    price=600.00,
                    start_date=datetime.now().date() - timedelta(days=150),
                    end_date=datetime.now().date() + timedelta(days=30),
                    status='suspended',
                    accounting_status='paid'
                ),
                Subscription(
                    name='خادم قاعدة البيانات',
                    provider_id=4,  # DigitalOcean
                    api_key='do_key_789123456',
                    port='5432',
                    cloud_ip='*************',
                    cloud_name='Database-Server-DO',
                    customer_name='مركز البيانات الحديث',
                    customer_email='<EMAIL>',
                    subscription_type='monthly',
                    price=75.50,
                    start_date=datetime.now().date() - timedelta(days=20),
                    end_date=datetime.now().date() + timedelta(days=10),
                    status='active',
                    accounting_status='paid'
                ),
                Subscription(
                    name='خادم التطبيقات',
                    provider_id=1,  # AWS
                    api_key='aws_app_key_555',
                    port='80',
                    cloud_ip='*************',
                    cloud_name='App-Server-AWS',
                    customer_name='شركة التطبيقات الذكية',
                    customer_email='<EMAIL>',
                    subscription_type='annual',
                    price=1500.00,
                    start_date=datetime.now().date() - timedelta(days=200),
                    end_date=datetime.now().date() + timedelta(days=165),
                    status='active',
                    accounting_status='unpaid'
                ),
                Subscription(
                    name='خادم الاختبار',
                    provider_id=2,  # GCP
                    api_key='gcp_test_key_777',
                    port='3000',
                    cloud_ip='************',
                    cloud_name='Test-Server-GCP',
                    customer_name='مختبر التقنيات المتقدمة',
                    customer_email='<EMAIL>',
                    subscription_type='monthly',
                    price=45.00,
                    start_date=datetime.now().date() - timedelta(days=5),
                    end_date=datetime.now().date() + timedelta(days=3),
                    status='active',
                    accounting_status='paid'
                )
            ]

            for subscription in subscriptions:
                db.session.add(subscription)

            db.session.commit()
            print("تم إنشاء المستخدم الافتراضي: admin / 123456")
            print("تم إضافة بيانات تجريبية للاختبار")
    
@app.route('/export_subscriptions_csv')
@login_required
def export_subscriptions_csv():
    """تصدير الاشتراكات بصيغة CSV"""
    try:
        import csv
        from io import StringIO

        # جلب البيانات
        subscriptions = Subscription.query.all()

        # إنشاء ملف CSV في الذاكرة
        output = StringIO()
        writer = csv.writer(output)

        # كتابة العناوين
        writer.writerow(['اسم الاشتراك', 'المزود', 'النوع', 'السعر', 'الحالة', 'تاريخ البداية', 'تاريخ الانتهاء'])

        # كتابة البيانات
        for sub in subscriptions:
            writer.writerow([
                sub.name,
                sub.provider.name if sub.provider else 'غير محدد',
                sub.subscription_type,
                sub.price,
                sub.status,
                sub.start_date.strftime('%Y-%m-%d') if sub.start_date else '',
                sub.end_date.strftime('%Y-%m-%d') if sub.end_date else ''
            ])

        output.seek(0)

        return Response(
            output.getvalue(),
            mimetype='text/csv',
            headers={
                'Content-Disposition': f'attachment; filename=subscriptions_{datetime.now().strftime("%Y%m%d")}.csv'
            }
        )

    except Exception as e:
        flash('فشل في تصدير CSV', 'error')
        return redirect(url_for('subscriptions'))

@app.route('/export_subscriptions_excel')
@login_required
def export_subscriptions_excel():
    """تصدير الاشتراكات بصيغة Excel"""
    return export_subscriptions_csv()

@app.route('/subscription/<int:subscription_id>/statement')
@login_required
def customer_statement(subscription_id):
    """كشف حساب العميل"""
    try:
        subscription = Subscription.query.get_or_404(subscription_id)

        return render_template('customer_statement.html',
                             subscription=subscription,
                             now=datetime.now())
    except Exception as e:
        flash('خطأ في تحميل كشف الحساب', 'error')
        return redirect(url_for('subscriptions'))

@app.route('/subscription/<int:subscription_id>/add-payment', methods=['GET', 'POST'])
@login_required
def add_payment(subscription_id):
    """إضافة دفعة جديدة"""
    subscription = Subscription.query.get_or_404(subscription_id)

    if request.method == 'POST':
        try:
            payment = Payment(
                subscription_id=subscription_id,
                amount=float(request.form['amount']),
                payment_date=datetime.strptime(request.form['payment_date'], '%Y-%m-%d').date(),
                payment_method=request.form['payment_method'],
                status=request.form.get('status', 'completed'),
                notes=request.form.get('notes', ''),
                receipt_number=request.form.get('receipt_number', '')
            )

            db.session.add(payment)
            db.session.commit()

            # تسجيل النشاط
            log_activity(
                user_id=session['user_id'],
                action='add_payment',
                details=f'تم إضافة دفعة بقيمة ${payment.amount} للاشتراك: {subscription.name}'
            )

            flash('تم إضافة الدفعة بنجاح', 'success')
            return redirect(url_for('customer_statement', subscription_id=subscription_id))

        except Exception as e:
            db.session.rollback()
            flash(f'خطأ في إضافة الدفعة: {str(e)}', 'error')

    return render_template('add_payment.html',
                         subscription=subscription,
                         today=datetime.now().date())

# ===== Routes أنظمة الدفع اليمنية =====

@app.route('/payment-gateways')
@login_required
def payment_gateways():
    """صفحة إدارة أنظمة الدفع"""
    try:
        current_user_obj = User.query.get(session['user_id'])
        if not current_user_obj or not current_user_obj.has_permission('payments.view'):
            flash('ليس لديك صلاحية للوصول لأنظمة الدفع', 'error')
            return redirect(url_for('dashboard'))

        # جلب بوابات الدفع
        gateways = PaymentGateway.query.all()

        # جلب المعاملات الحديثة
        recent_transactions = PaymentTransaction.query.order_by(
            PaymentTransaction.created_at.desc()
        ).limit(10).all()

        # حساب الإحصائيات
        total_transactions = PaymentTransaction.query.count()
        total_amount = db.session.query(func.sum(PaymentTransaction.amount)).scalar() or 0
        successful_transactions = PaymentTransaction.query.filter_by(status='completed').count()
        success_rate = (successful_transactions / total_transactions * 100) if total_transactions > 0 else 0
        active_gateways = PaymentGateway.query.filter_by(is_active=True).count()

        return render_template('payment_gateways.html',
                             gateways=gateways,
                             recent_transactions=recent_transactions,
                             total_transactions=total_transactions,
                             total_amount=total_amount,
                             success_rate=success_rate,
                             active_gateways=active_gateways)

    except Exception as e:
        flash('خطأ في تحميل أنظمة الدفع', 'error')
        return redirect(url_for('dashboard'))

@app.route('/api/payment-gateways/<int:gateway_id>/toggle', methods=['POST'])
@login_required
def api_toggle_gateway(gateway_id):
    """API لتفعيل/إيقاف بوابة دفع"""
    try:
        current_user_obj = User.query.get(session['user_id'])
        if not current_user_obj or not current_user_obj.has_permission('payments.edit'):
            return jsonify({'error': 'Unauthorized'}), 403

        gateway = PaymentGateway.query.get_or_404(gateway_id)
        data = request.get_json()
        action = data.get('action')

        if action == 'activate':
            gateway.is_active = True
        elif action == 'deactivate':
            gateway.is_active = False
        else:
            return jsonify({'error': 'Invalid action'}), 400

        gateway.updated_at = datetime.utcnow()
        db.session.commit()

        # تسجيل النشاط
        log_activity(
            action='gateway_toggle',
            entity_type='payment_gateway',
            entity_id=gateway.id,
            user_id=session['user_id'],
            details=f'تم {action} بوابة الدفع: {gateway.name_ar}'
        )

        return jsonify({'success': True})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/payment-stats')
@login_required
def api_payment_stats():
    """API لإحصائيات الدفع"""
    try:
        current_user_obj = User.query.get(session['user_id'])
        if not current_user_obj or not current_user_obj.has_permission('payments.view'):
            return jsonify({'error': 'Unauthorized'}), 403

        # إحصائيات اليوم
        today = datetime.now().date()
        today_transactions = PaymentTransaction.query.filter(
            func.date(PaymentTransaction.created_at) == today
        ).count()

        today_amount = db.session.query(func.sum(PaymentTransaction.amount)).filter(
            func.date(PaymentTransaction.created_at) == today
        ).scalar() or 0

        # إحصائيات عامة
        total_transactions = PaymentTransaction.query.count()
        total_amount = db.session.query(func.sum(PaymentTransaction.amount)).scalar() or 0
        successful_transactions = PaymentTransaction.query.filter_by(status='completed').count()
        success_rate = (successful_transactions / total_transactions * 100) if total_transactions > 0 else 0

        return jsonify({
            'today_transactions': today_transactions,
            'today_amount': today_amount,
            'total_transactions': total_transactions,
            'total_amount': total_amount,
            'success_rate': success_rate,
            'active_gateways': PaymentGateway.query.filter_by(is_active=True).count()
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

# تم نقل route التقارير المتقدمة إلى مكان آخر

@app.route('/api/reports/generate', methods=['POST'])
@login_required
def api_generate_report():
    """API لإنشاء التقارير"""
    try:
        current_user_obj = User.query.get(session['user_id'])
        if not current_user_obj or not current_user_obj.has_permission('reports.create'):
            return jsonify({'error': 'Unauthorized'}), 403

        data = request.get_json()
        report_type = data.get('report_type')
        date_from = datetime.strptime(data.get('date_from'), '%Y-%m-%d').date()
        date_to = datetime.strptime(data.get('date_to'), '%Y-%m-%d').date()
        format_type = data.get('format', 'pdf')

        # إنشاء التقرير
        report_data = generate_report_data(report_type, date_from, date_to)

        # حفظ التقرير في قاعدة البيانات
        report = AdvancedReport(
            report_name=f"تقرير {report_type} - {date_from} إلى {date_to}",
            report_type=report_type,
            report_category='custom',
            date_from=date_from,
            date_to=date_to,
            report_data=json.dumps(report_data, ensure_ascii=False),
            generated_by=session['user_id']
        )

        db.session.add(report)
        db.session.commit()

        # تسجيل النشاط
        log_activity(
            action='report_generated',
            entity_type='report',
            entity_id=report.id,
            user_id=session['user_id'],
            details=f'تم إنشاء تقرير: {report.report_name}'
        )

        return jsonify({
            'success': True,
            'report_id': report.id,
            'download_url': f'/reports/{report.id}/download'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

def generate_report_data(report_type, date_from, date_to):
    """إنشاء بيانات التقرير"""
    try:
        if report_type == 'financial':
            return generate_financial_report(date_from, date_to)
        elif report_type == 'subscription':
            return generate_subscription_report(date_from, date_to)
        elif report_type == 'payment':
            return generate_payment_report(date_from, date_to)
        elif report_type == 'analytics':
            return generate_analytics_report(date_from, date_to)
        else:
            return {'error': 'نوع تقرير غير مدعوم'}
    except Exception as e:
        return {'error': str(e)}

def generate_financial_report(date_from, date_to):
    """إنشاء التقرير المالي"""
    try:
        # إجمالي الإيرادات
        total_revenue = db.session.query(func.sum(Invoice.amount)).filter(
            Invoice.created_at.between(date_from, date_to),
            Invoice.status == 'paid'
        ).scalar() or 0

        # إجمالي المدفوعات
        total_payments = db.session.query(func.sum(Payment.amount)).filter(
            Payment.payment_date.between(date_from, date_to)
        ).scalar() or 0

        # المدفوعات المعلقة
        pending_payments = db.session.query(func.sum(Invoice.amount)).filter(
            Invoice.created_at.between(date_from, date_to),
            Invoice.status == 'pending'
        ).scalar() or 0

        # الفواتير المتأخرة
        overdue_invoices = db.session.query(func.sum(Invoice.amount)).filter(
            Invoice.due_date < datetime.now().date(),
            Invoice.status == 'pending'
        ).scalar() or 0

        return {
            'total_revenue': float(total_revenue),
            'total_payments': float(total_payments),
            'pending_payments': float(pending_payments),
            'overdue_invoices': float(overdue_invoices),
            'net_income': float(total_payments - total_revenue) if total_revenue else 0
        }

    except Exception as e:
        return {'error': str(e)}

def generate_subscription_report(date_from, date_to):
    """إنشاء تقرير الاشتراكات"""
    try:
        # إجمالي الاشتراكات
        total_subscriptions = Subscription.query.filter(
            Subscription.start_date.between(date_from, date_to)
        ).count()

        # الاشتراكات النشطة
        active_subscriptions = Subscription.query.filter(
            Subscription.status == 'active'
        ).count()

        # الاشتراكات المنتهية
        expired_subscriptions = Subscription.query.filter(
            Subscription.status == 'expired'
        ).count()

        # الاشتراكات الجديدة في الفترة
        new_subscriptions = Subscription.query.filter(
            Subscription.start_date.between(date_from, date_to)
        ).count()

        return {
            'total_subscriptions': total_subscriptions,
            'active_subscriptions': active_subscriptions,
            'expired_subscriptions': expired_subscriptions,
            'new_subscriptions': new_subscriptions
        }

    except Exception as e:
        return {'error': str(e)}

def generate_payment_report(date_from, date_to):
    """إنشاء تقرير المدفوعات"""
    try:
        # إجمالي المعاملات
        total_transactions = PaymentTransaction.query.filter(
            PaymentTransaction.created_at.between(date_from, date_to)
        ).count()

        # المعاملات الناجحة
        successful_transactions = PaymentTransaction.query.filter(
            PaymentTransaction.created_at.between(date_from, date_to),
            PaymentTransaction.status == 'completed'
        ).count()

        # المعاملات الفاشلة
        failed_transactions = PaymentTransaction.query.filter(
            PaymentTransaction.created_at.between(date_from, date_to),
            PaymentTransaction.status == 'failed'
        ).count()

        # إجمالي المبالغ
        total_amount = db.session.query(func.sum(PaymentTransaction.amount)).filter(
            PaymentTransaction.created_at.between(date_from, date_to),
            PaymentTransaction.status == 'completed'
        ).scalar() or 0

        # التقسيم حسب بوابات الدفع
        gateway_stats = db.session.query(
            PaymentGateway.name_ar,
            func.count(PaymentTransaction.id).label('count'),
            func.sum(PaymentTransaction.amount).label('amount')
        ).join(PaymentTransaction).filter(
            PaymentTransaction.created_at.between(date_from, date_to),
            PaymentTransaction.status == 'completed'
        ).group_by(PaymentGateway.id).all()

        return {
            'total_transactions': total_transactions,
            'successful_transactions': successful_transactions,
            'failed_transactions': failed_transactions,
            'total_amount': float(total_amount),
            'success_rate': (successful_transactions / total_transactions * 100) if total_transactions > 0 else 0,
            'gateway_stats': [
                {
                    'gateway': stat[0],
                    'transactions': stat[1],
                    'amount': float(stat[2] or 0)
                } for stat in gateway_stats
            ]
        }

    except Exception as e:
        return {'error': str(e)}

def generate_analytics_report(date_from, date_to):
    """إنشاء تقرير التحليلات المتقدمة"""
    try:
        # معدل النمو الشهري
        current_month_revenue = db.session.query(func.sum(Invoice.amount)).filter(
            func.extract('month', Invoice.created_at) == datetime.now().month,
            func.extract('year', Invoice.created_at) == datetime.now().year,
            Invoice.status == 'paid'
        ).scalar() or 0

        last_month_revenue = db.session.query(func.sum(Invoice.amount)).filter(
            func.extract('month', Invoice.created_at) == datetime.now().month - 1,
            func.extract('year', Invoice.created_at) == datetime.now().year,
            Invoice.status == 'paid'
        ).scalar() or 0

        growth_rate = ((current_month_revenue - last_month_revenue) / last_month_revenue * 100) if last_month_revenue > 0 else 0

        # معدل الاحتفاظ بالعملاء
        total_customers = User.query.filter_by(role_id=2).count()  # assuming role_id 2 is customer
        active_customers = db.session.query(func.count(func.distinct(Subscription.customer_id))).filter(
            Subscription.status == 'active'
        ).scalar() or 0

        retention_rate = (active_customers / total_customers * 100) if total_customers > 0 else 0

        return {
            'growth_rate': float(growth_rate),
            'retention_rate': float(retention_rate),
            'current_month_revenue': float(current_month_revenue),
            'last_month_revenue': float(last_month_revenue),
            'total_customers': total_customers,
            'active_customers': active_customers
        }

    except Exception as e:
        return {'error': str(e)}

@app.route('/api/notifications/advanced')
@login_required
def api_advanced_notifications():
    """API للإشعارات المتقدمة"""
    try:
        user_id = session['user_id']
        limit = request.args.get('limit', 10, type=int)
        unread_only = request.args.get('unread_only', False, type=bool)

        notifications = get_user_notifications(user_id, limit, unread_only)

        notifications_data = []
        for notification in notifications:
            notifications_data.append({
                'id': notification.id,
                'title': notification.title,
                'message': notification.message,
                'type': notification.notification_type,
                'priority': notification.priority,
                'category': notification.category,
                'is_read': notification.is_read,
                'action_url': notification.action_url,
                'action_text': notification.action_text,
                'created_at': notification.created_at.isoformat(),
                'expires_at': notification.expires_at.isoformat() if notification.expires_at else None
            })

        return jsonify({
            'success': True,
            'notifications': notifications_data,
            'unread_count': len([n for n in notifications if not n.is_read])
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/notifications/<int:notification_id>/read', methods=['POST'])
@login_required
def api_mark_notification_read(notification_id):
    """API لتمييز الإشعار كمقروء"""
    try:
        user_id = session['user_id']
        success = mark_notification_read(notification_id, user_id)

        if success:
            return jsonify({'success': True})
        else:
            return jsonify({'error': 'Notification not found or access denied'}), 404

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/system/health')
@login_required
def api_system_health():
    """API لحالة النظام"""
    try:
        current_user_obj = User.query.get(session['user_id'])
        if not current_user_obj or not current_user_obj.has_permission('system.view'):
            return jsonify({'error': 'Unauthorized'}), 403

        health_data = get_system_health()
        return jsonify({
            'success': True,
            'health': health_data
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/system-monitor')
@login_required
def system_monitor():
    """صفحة مراقبة النظام"""
    try:
        current_user_obj = User.query.get(session['user_id'])
        if not current_user_obj or not current_user_obj.has_permission('system.view'):
            flash('ليس لديك صلاحية للوصول لمراقبة النظام', 'error')
            return redirect(url_for('dashboard'))

        health_data = get_system_health()

        return render_template('system_monitor.html',
                             health_data=health_data)

    except Exception as e:
        flash('خطأ في تحميل مراقبة النظام', 'error')
        return redirect(url_for('dashboard'))

# ===== Routes الميزات المتقدمة =====

@app.route('/smart-dashboard')
@login_required
def smart_dashboard():
    """لوحة التحكم الذكية"""
    try:
        # حساب الإحصائيات
        total_subscriptions = Subscription.query.count()
        active_subscriptions = Subscription.query.filter_by(status='active').count()

        # حساب الإيرادات الشهرية
        current_month = datetime.now().replace(day=1)
        monthly_revenue = db.session.query(func.sum(Payment.amount)).filter(
            Payment.payment_date >= current_month,
            Payment.status == 'completed'
        ).scalar() or 0

        # حساب معدل التجديد
        total_expired = Subscription.query.filter(
            Subscription.end_date < datetime.now().date()
        ).count()
        renewed = Subscription.query.filter(
            Subscription.end_date < datetime.now().date(),
            Subscription.status == 'active'
        ).count()
        renewal_rate = (renewed / total_expired * 100) if total_expired > 0 else 0

        # الاشتراكات التي تنتهي قريباً
        expiring_soon = Subscription.query.filter(
            Subscription.end_date <= datetime.now().date() + timedelta(days=7),
            Subscription.end_date > datetime.now().date(),
            Subscription.status == 'active'
        ).count()

        # الاشتراكات المتأخرة
        overdue = Subscription.query.filter(
            Subscription.end_date < datetime.now().date(),
            Subscription.status == 'active'
        ).count()

        stats = {
            'total_subscriptions': total_subscriptions,
            'active_subscriptions': active_subscriptions,
            'monthly_revenue': monthly_revenue,
            'renewal_rate': renewal_rate,
            'expiring_soon': expiring_soon,
            'overdue': overdue,
            'online_users': 1,
            'system_load': 2.3,
            'response_time': 120
        }

        return render_template('smart_dashboard.html', stats=stats)

    except Exception as e:
        flash('خطأ في تحميل لوحة التحكم', 'error')
        return redirect(url_for('dashboard'))

@app.route('/ui-customizer')
@login_required
def ui_customizer():
    """محرر الواجهات التفاعلي"""
    return render_template('ui_customizer.html')

@app.route('/api/dashboard/stats')
@login_required
def api_dashboard_stats():
    """API للحصول على إحصائيات لوحة التحكم"""
    try:
        # تدريب نموذج الذكاء الاصطناعي إذا لم يكن مدرباً
        ai_engine.train_renewal_prediction_model()

        # حساب الإحصائيات المحدثة
        total_subscriptions = Subscription.query.count()
        active_subscriptions = Subscription.query.filter_by(status='active').count()

        current_month = datetime.now().replace(day=1)
        monthly_revenue = db.session.query(func.sum(Payment.amount)).filter(
            Payment.payment_date >= current_month,
            Payment.status == 'completed'
        ).scalar() or 0

        return jsonify({
            'total_subscriptions': total_subscriptions,
            'active_subscriptions': active_subscriptions,
            'monthly_revenue': float(monthly_revenue),
            'renewal_rate': 85.5,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/ai/insights')
@login_required
def api_ai_insights():
    """API للحصول على التحليلات الذكية"""
    try:
        insights = []

        # الحصول على توصيات ذكية لجميع الاشتراكات
        subscriptions = Subscription.query.filter_by(status='active').limit(10).all()

        for subscription in subscriptions:
            recommendations = ai_engine.get_smart_recommendations(subscription.id)
            for rec in recommendations:
                insights.append({
                    'type': rec['type'],
                    'title': rec['title'],
                    'description': rec['message'],
                    'confidence': 85 + (hash(rec['title']) % 15),
                    'subscription_id': subscription.id
                })

        # إضافة تحليلات عامة
        if len(insights) == 0:
            insights.append({
                'type': 'success',
                'title': 'أداء ممتاز',
                'description': 'جميع الاشتراكات تعمل بشكل طبيعي ولا توجد مشاكل تتطلب تدخل فوري',
                'confidence': 95
            })

        return jsonify(insights[:5])

    except Exception as e:
        return jsonify([{
            'type': 'info',
            'title': 'نظام التحليل الذكي',
            'description': 'يتم تحضير التحليلات الذكية...',
            'confidence': 0
        }])

@app.route('/api/user/preferences', methods=['GET', 'POST'])
@login_required
def api_user_preferences():
    """API لإدارة تفضيلات المستخدم"""
    user_id = session.get('user_id')

    if request.method == 'POST':
        try:
            data = request.get_json()

            for key, value in data.items():
                preference = UserPreference.query.filter_by(
                    user_id=user_id,
                    preference_key=key
                ).first()

                if preference:
                    preference.preference_value = json.dumps(value)
                    preference.updated_at = datetime.utcnow()
                else:
                    preference = UserPreference(
                        user_id=user_id,
                        preference_key=key,
                        preference_value=json.dumps(value)
                    )
                    db.session.add(preference)

            db.session.commit()
            return jsonify({'success': True})

        except Exception as e:
            db.session.rollback()
            return jsonify({'error': str(e)}), 500

    else:
        try:
            preferences = UserPreference.query.filter_by(user_id=user_id).all()
            result = {}

            for pref in preferences:
                try:
                    result[pref.preference_key] = json.loads(pref.preference_value)
                except:
                    result[pref.preference_key] = pref.preference_value

            return jsonify(result)

        except Exception as e:
            return jsonify({})

# ===== Routes مركز الحماية =====

@app.route('/security')
@login_required
def security_center():
    """مركز الحماية والأمان"""
    try:
        # التحقق من الأذونات
        current_user_obj = User.query.get(session['user_id'])
        if not current_user_obj or not current_user_obj.has_permission('security.view'):
            flash('ليس لديك صلاحية للوصول لمركز الحماية', 'error')
            return redirect(url_for('dashboard'))

        # حساب إحصائيات الأمان
        security_stats = security_manager.get_security_stats()

        # إضافة إحصائيات إضافية
        today = datetime.now().date()
        security_stats.update({
            'login_attempts': ActivityLog.query.filter(
                ActivityLog.action == 'login',
                func.date(ActivityLog.timestamp) == today
            ).count(),
            'failed_logins': ActivityLog.query.filter(
                ActivityLog.action == 'failed_login',
                func.date(ActivityLog.timestamp) == today
            ).count(),
            'active_users': User.query.filter_by(is_active=True).count(),
            'active_sessions': 1  # يمكن تطويرها لاحقاً
        })

        return render_template('security_center.html', security_stats=security_stats)

    except Exception as e:
        flash('خطأ في تحميل مركز الحماية', 'error')
        return redirect(url_for('dashboard'))

@app.route('/api/security/status')
@login_required
def api_security_status():
    """API للحصول على حالة الأمان"""
    try:
        current_user_obj = User.query.get(session['user_id'])
        if not current_user_obj or not current_user_obj.has_permission('security.view'):
            return jsonify({'error': 'Unauthorized'}), 403

        stats = security_manager.get_security_stats()
        recent_threats = security_manager.get_recent_threats(10)

        # تحويل التهديدات لتنسيق مناسب للعرض
        formatted_threats = []
        for threat in recent_threats:
            formatted_threats.append({
                'title': f"{threat['type'].replace('_', ' ').title()}",
                'description': threat['description'],
                'severity': threat['severity'],
                'timestamp': threat['timestamp']
            })

        return jsonify({
            'threats': len(recent_threats),
            'blocked_attempts': stats.get('failed_attempts_total', 0),
            'blocked_ips': stats.get('blocked_ips', 0),
            'login_attempts': ActivityLog.query.filter(
                ActivityLog.action == 'login',
                func.date(ActivityLog.timestamp) == datetime.now().date()
            ).count(),
            'failed_logins': ActivityLog.query.filter(
                ActivityLog.action == 'failed_login',
                func.date(ActivityLog.timestamp) == datetime.now().date()
            ).count(),
            'active_users': User.query.filter_by(is_active=True).count(),
            'active_sessions': 1,
            'recent_threats': formatted_threats
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/security/scan', methods=['POST'])
@login_required
def api_security_scan():
    """API لإجراء فحص أمني"""
    try:
        current_user_obj = User.query.get(session['user_id'])
        if not current_user_obj or not current_user_obj.has_permission('security.manage'):
            return jsonify({'error': 'Unauthorized'}), 403

        # محاكاة فحص أمني
        scan_results = {
            'vulnerabilities_found': 0,
            'threats_blocked': security_manager.get_security_stats().get('blocked_ips', 0),
            'system_status': 'secure',
            'last_scan': datetime.now().isoformat()
        }

        # تسجيل النشاط
        log_activity(
            user_id=session['user_id'],
            action='security_scan',
            details='تم إجراء فحص أمني شامل'
        )

        return jsonify({'success': True, 'results': scan_results})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/security/firewall/toggle', methods=['POST'])
@login_required
def api_firewall_toggle():
    """API لتبديل قواعد جدار الحماية"""
    try:
        current_user_obj = User.query.get(session['user_id'])
        if not current_user_obj or not current_user_obj.has_permission('security.manage'):
            return jsonify({'error': 'Unauthorized'}), 403

        data = request.get_json()
        rule_name = data.get('rule')
        is_active = data.get('active')

        # تسجيل النشاط
        log_activity(
            user_id=session['user_id'],
            action='firewall_rule_toggle',
            details=f'تم {"تفعيل" if is_active else "إلغاء"} قاعدة: {rule_name}'
        )

        return jsonify({'success': True})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/security/regenerate-keys', methods=['POST'])
@login_required
def api_regenerate_keys():
    """API لتجديد مفاتيح التشفير"""
    try:
        current_user_obj = User.query.get(session['user_id'])
        if not current_user_obj or not current_user_obj.has_permission('security.manage'):
            return jsonify({'error': 'Unauthorized'}), 403

        # تجديد مفتاح التشفير
        app.config['ENCRYPTION_KEY'] = base64.urlsafe_b64encode(secrets.token_bytes(32))
        security_manager.encryption_key = app.config['ENCRYPTION_KEY']

        # تسجيل النشاط
        log_activity(
            user_id=session['user_id'],
            action='encryption_keys_regenerated',
            details='تم تجديد مفاتيح التشفير'
        )

        return jsonify({'success': True})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/users')
@login_required
def user_management():
    """صفحة إدارة المستخدمين"""
    try:
        # التحقق من الأذونات
        current_user_obj = User.query.get(session['user_id'])
        if not current_user_obj or not current_user_obj.has_permission('users.view'):
            flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
            return redirect(url_for('dashboard'))

        # جلب المستخدمين والأدوار
        users = User.query.all()
        roles = Role.query.all()

        # حساب الإحصائيات
        users_stats = {
            'total': len(users),
            'active': len([u for u in users if u.is_active and not u.is_locked]),
            'admins': len([u for u in users if u.role and u.role.name == 'admin']),
            'locked': len([u for u in users if u.is_locked])
        }

        return render_template('user_management.html',
                             users=users,
                             roles=roles,
                             users_stats=users_stats)

    except Exception as e:
        flash('خطأ في تحميل صفحة المستخدمين', 'error')
        return redirect(url_for('dashboard'))

@app.route('/api/users')
@login_required
def api_users():
    """API للحصول على قائمة المستخدمين"""
    try:
        users = User.query.all()
        users_data = []

        for user in users:
            users_data.append({
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'full_name': user.full_name,
                'role_id': user.role_id,
                'role_name': user.role.display_name if user.role else 'بدون دور',
                'department': user.department,
                'position': user.position,
                'is_active': user.is_active,
                'is_locked': user.is_locked,
                'last_login': user.last_login.isoformat() if user.last_login else None,
                'created_at': user.created_at.isoformat()
            })

        return jsonify(users_data)

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/users/<int:user_id>/lock', methods=['POST'])
@login_required
def api_lock_user(user_id):
    """قفل مستخدم"""
    try:
        current_user_obj = User.query.get(session['user_id'])
        if not current_user_obj or not current_user_obj.has_permission('users.edit'):
            return jsonify({'error': 'ليس لديك صلاحية'}), 403

        user = User.query.get_or_404(user_id)
        if user.is_admin:
            return jsonify({'error': 'لا يمكن قفل حساب المدير'}), 400

        user.lock_account()

        log_activity(
            user_id=session['user_id'],
            action='lock_user',
            details=f'تم قفل المستخدم: {user.username}'
        )

        return jsonify({'success': True})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/users/<int:user_id>/unlock', methods=['POST'])
@login_required
def api_unlock_user(user_id):
    """إلغاء قفل مستخدم"""
    try:
        current_user_obj = User.query.get(session['user_id'])
        if not current_user_obj or not current_user_obj.has_permission('users.edit'):
            return jsonify({'error': 'ليس لديك صلاحية'}), 403

        user = User.query.get_or_404(user_id)
        user.unlock_account()

        log_activity(
            user_id=session['user_id'],
            action='unlock_user',
            details=f'تم إلغاء قفل المستخدم: {user.username}'
        )

        return jsonify({'success': True})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/users/<int:user_id>', methods=['DELETE'])
@login_required
def api_delete_user(user_id):
    """حذف مستخدم"""
    try:
        current_user_obj = User.query.get(session['user_id'])
        if not current_user_obj or not current_user_obj.has_permission('users.delete'):
            return jsonify({'error': 'ليس لديك صلاحية'}), 403

        user = User.query.get_or_404(user_id)
        if user.is_admin:
            return jsonify({'error': 'لا يمكن حذف حساب المدير'}), 400

        if user.id == session['user_id']:
            return jsonify({'error': 'لا يمكن حذف حسابك الخاص'}), 400

        username = user.username
        db.session.delete(user)
        db.session.commit()

        log_activity(
            user_id=session['user_id'],
            action='delete_user',
            details=f'تم حذف المستخدم: {username}'
        )

        return jsonify({'success': True})

    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    print("🚀 تم تشغيل نظام إدارة الاشتراكات المتطور والنهائي")
    print("📍 الرابط: http://localhost:7722")
    print("👤 المستخدم: admin")
    print("🔑 كلمة المرور: 123456")
    print("👨‍💻 تطوير: المهندس محمد ياسر الجبوري")
    print("✨ النظام النهائي مع جميع المميزات:")
    print("   🧠 الذكاء الاصطناعي والتحليلات المتقدمة")
    print("   📱 تطبيق الهاتف المحمول (PWA)")
    print("   🔔 نظام الإشعارات المتطور")
    print("   🎨 واجهة Glass Morphism حديثة")
    print("   ⚡ أنيميشن وتأثيرات متطورة")
    print("   📊 إدارة الاشتراكات المتطورة الجديدة")
    print("   🔄 تجديد سريع وإجراءات متعددة")
    print("   📋 نظام التقارير الشامل")
    print("   💰 إدارة الفواتير المتطورة")
    print("   🔒 نظام الأمان المتقدم")
    print("   📈 مراقبة صحة النظام")
    print("   📝 سجل الأنشطة التفصيلي")
    print("   💾 النسخ الاحتياطي التلقائي")
    print("   🎭 3 أنماط عرض مختلفة")
    print("   🔍 فلاتر وبحث متقدم")
    print("   📤 تصدير البيانات")
    print("   💳 أنظمة الدفع اليمنية")
    print("   🚀 تحسينات الأداء والسرعة")
    print("   📊 مراقبة النظام المتقدمة")
    print("   🔔 نظام الإشعارات المتطور")
    print("="*80)

    # تسجيل وقت بداية التشغيل
    app.start_time = time.time()

    # إنشاء بوابات الدفع اليمنية داخل سياق التطبيق
    with app.app_context():
        create_payment_gateways()

        # تحسين قاعدة البيانات
        optimize_database()

        # إنشاء إشعار بدء التشغيل
        create_system_notification('system_startup', {
            'message': 'تم تشغيل النظام بنجاح مع جميع التحسينات والميزات الجديدة'
        })

        print("✅ تم تشغيل النظام بنجاح مع جميع الميزات المتقدمة!")

    app.run(debug=True, host='0.0.0.0', port=7722)
