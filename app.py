#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إدارة الاشتراكات
Subscription Management System

تطبيق Flask لإدارة اشتراكات الخدمات السحابية
"""

from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify, make_response
from flask_sqlalchemy import SQLAlchemy
from werkzeug.security import generate_password_hash, check_password_hash
from datetime import datetime, timedelta
import os
import json
from functools import wraps
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.units import inch
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import io
import base64
# استيراد مكتبات الإيميل (اختيارية)
try:
    from flask_mail import Mail, Message
    MAIL_AVAILABLE = True
except ImportError:
    Mail = None
    Message = None
    MAIL_AVAILABLE = False

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import logging
from logging.handlers import RotatingFileHandler

# إعداد التطبيق
app = Flask(__name__)

# إعدادات الأمان والأداء
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'subscription_management_system_2024_secure_key')
app.config['SQLALCHEMY_DATABASE_URI'] = os.environ.get('DATABASE_URL', 'sqlite:///subscriptions.db')
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['SQLALCHEMY_ENGINE_OPTIONS'] = {
    'pool_pre_ping': True,
    'pool_recycle': 300,
}

# إعدادات الجلسة
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=24)
app.config['SESSION_COOKIE_SECURE'] = False  # True في الإنتاج مع HTTPS
app.config['SESSION_COOKIE_HTTPONLY'] = True
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'

# إعدادات التحميل
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# إعداد قاعدة البيانات
db = SQLAlchemy(app)

# إعداد Flask-Migrate للتحديثات التلقائية (اختياري)
try:
    from flask_migrate import Migrate
    migrate = Migrate(app, db)
    MIGRATE_AVAILABLE = True
except ImportError:
    migrate = None
    MIGRATE_AVAILABLE = False

# إعداد نظام Logging
if not app.debug:
    if not os.path.exists('logs'):
        os.mkdir('logs')

    file_handler = RotatingFileHandler('logs/subscription_system.log', maxBytes=10240000, backupCount=10)
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
    ))
    file_handler.setLevel(logging.INFO)
    app.logger.addHandler(file_handler)
    app.logger.setLevel(logging.INFO)
    app.logger.info('نظام إدارة الاشتراكات - بدء التشغيل')

# نماذج قاعدة البيانات
class User(db.Model):
    """نموذج المستخدمين"""
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    role = db.Column(db.String(20), default='user')  # admin, user
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_active = db.Column(db.Boolean, default=True)

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class CloudProvider(db.Model):
    """نموذج مزودي الخدمات السحابية"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    logo_url = db.Column(db.String(200))
    website = db.Column(db.String(200))
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Subscription(db.Model):
    """نموذج الاشتراكات"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    provider_id = db.Column(db.Integer, db.ForeignKey('cloud_provider.id'), nullable=False)
    api_key = db.Column(db.String(200), nullable=False)
    port = db.Column(db.String(10))
    cloud_ip = db.Column(db.String(45))  # IPv4 or IPv6
    cloud_name = db.Column(db.String(100))  # اسم الكلاود/السيرفر
    customer_email = db.Column(db.String(120))  # إيميل العميل لإرسال الرسائل
    customer_name = db.Column(db.String(100))  # اسم العميل
    subscription_type = db.Column(db.String(20), nullable=False)  # monthly, semi_annual, annual
    price = db.Column(db.Float, nullable=False)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    status = db.Column(db.String(20), default='active')  # active, suspended, expired
    accounting_status = db.Column(db.String(20), default='unpaid')  # paid, unpaid
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    provider = db.relationship('CloudProvider', backref=db.backref('subscriptions', lazy=True))

class Invoice(db.Model):
    """نموذج الفواتير"""
    id = db.Column(db.Integer, primary_key=True)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'), nullable=False)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    amount = db.Column(db.Float, nullable=False)
    issue_date = db.Column(db.Date, nullable=False)
    due_date = db.Column(db.Date, nullable=False)
    payment_date = db.Column(db.Date)
    status = db.Column(db.String(20), default='pending')  # pending, paid, overdue
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    subscription = db.relationship('Subscription', backref=db.backref('invoices', lazy=True))

class Notification(db.Model):
    """نموذج الإشعارات"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    type = db.Column(db.String(20), default='info')  # info, warning, error, success
    is_read = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    user = db.relationship('User', backref=db.backref('notifications', lazy=True))

class SystemSettings(db.Model):
    """نموذج إعدادات النظام"""
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), unique=True, nullable=False)
    value = db.Column(db.Text)
    description = db.Column(db.Text)
    category = db.Column(db.String(50), default='general')
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class ActivityLog(db.Model):
    """نموذج سجل الأنشطة"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    action = db.Column(db.String(100), nullable=False)  # create, update, delete, login, etc.
    entity_type = db.Column(db.String(50))  # subscription, invoice, user, etc.
    entity_id = db.Column(db.Integer)
    description = db.Column(db.Text)
    ip_address = db.Column(db.String(45))
    user_agent = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    user = db.relationship('User', backref=db.backref('activity_logs', lazy=True))

class BackupLog(db.Model):
    """نموذج سجل النسخ الاحتياطية"""
    id = db.Column(db.Integer, primary_key=True)
    backup_type = db.Column(db.String(50), nullable=False)  # manual, automatic
    file_path = db.Column(db.String(500))
    file_size = db.Column(db.BigInteger)
    status = db.Column(db.String(20), default='completed')  # completed, failed, in_progress
    error_message = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

# دالة تسجيل الأنشطة
def log_activity(action, entity_type=None, entity_id=None, description=None):
    """تسجيل نشاط المستخدم"""
    try:
        if 'user_id' in session:
            activity = ActivityLog(
                user_id=session['user_id'],
                action=action,
                entity_type=entity_type,
                entity_id=entity_id,
                description=description,
                ip_address=request.remote_addr,
                user_agent=request.headers.get('User-Agent', '')
            )
            db.session.add(activity)
            db.session.commit()
            app.logger.info(f'Activity logged: {action} by user {session.get("username", "Unknown")}')
    except Exception as e:
        app.logger.error(f'Error logging activity: {str(e)}')

# دالة التحقق من تسجيل الدخول
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('يرجى تسجيل الدخول للوصول لهذه الصفحة', 'error')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

# دالة التحقق من صلاحيات المدير
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            flash('يرجى تسجيل الدخول للوصول لهذه الصفحة', 'error')
            return redirect(url_for('login'))
        
        user = User.query.get(session['user_id'])
        if not user or user.role != 'admin':
            flash('ليس لديك صلاحية للوصول لهذه الصفحة', 'error')
            return redirect(url_for('dashboard'))
        return f(*args, **kwargs)
    return decorated_function

# الصفحات الرئيسية
@app.route('/')
def index():
    """الصفحة الرئيسية"""
    if 'user_id' in session:
        return redirect(url_for('dashboard'))
    return render_template('welcome.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    """صفحة تسجيل الدخول"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        remember = 'remember' in request.form
        
        user = User.query.filter_by(username=username).first()
        
        if user and user.check_password(password) and user.is_active:
            session['user_id'] = user.id
            session['username'] = user.username
            session['role'] = user.role
            session.permanent = remember

            # تسجيل نشاط تسجيل الدخول
            log_activity('login', 'user', user.id, f'تسجيل دخول ناجح للمستخدم {user.username}')

            flash(f'مرحباً {user.username}! تم تسجيل الدخول بنجاح', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')
    
    return render_template('login.html')

@app.route('/logout')
def logout():
    """تسجيل الخروج"""
    session.clear()
    flash('تم تسجيل الخروج بنجاح', 'success')
    return redirect(url_for('index'))

@app.route('/dashboard')
@login_required
def dashboard():
    """لوحة التحكم الرئيسية"""
    # إحصائيات عامة
    total_subscriptions = Subscription.query.count()
    active_subscriptions = Subscription.query.filter_by(status='active').count()
    pending_invoices = Invoice.query.filter_by(status='pending').count()
    expired_subscriptions = Subscription.query.filter_by(status='expired').count()
    total_users = User.query.count()

    # الإيرادات
    total_revenue = db.session.query(db.func.sum(Invoice.amount)).filter_by(status='paid').scalar() or 0
    monthly_revenue = db.session.query(db.func.sum(Invoice.amount)).filter(
        Invoice.status == 'paid',
        Invoice.payment_date >= datetime.now().replace(day=1).date()
    ).scalar() or 0

    # الاشتراكات المنتهية قريباً (خلال 30 يوم)
    upcoming_expiry = Subscription.query.filter(
        Subscription.end_date <= datetime.now().date() + timedelta(days=30),
        Subscription.status == 'active'
    ).all()

    # آخر الاشتراكات
    recent_subscriptions = Subscription.query.order_by(
        Subscription.created_at.desc()
    ).limit(5).all()

    # الإشعارات غير المقروءة
    unread_notifications = Notification.query.filter_by(
        user_id=session['user_id'], is_read=False
    ).count()

    # إحصائيات مزودي الخدمات
    provider_stats = db.session.query(
        CloudProvider.name,
        db.func.count(Subscription.id).label('count')
    ).join(Subscription).group_by(CloudProvider.name).all()

    # بيانات الرسم البياني (آخر 6 أشهر)
    chart_data = []
    revenue_data = []
    months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو']

    for i in range(6):
        month_start = datetime.now().replace(day=1) - timedelta(days=30*i)
        month_end = month_start + timedelta(days=30)

        # عدد الاشتراكات
        count = Subscription.query.filter(
            Subscription.created_at >= month_start,
            Subscription.created_at < month_end
        ).count()

        # الإيرادات
        revenue = db.session.query(db.func.sum(Invoice.amount)).filter(
            Invoice.created_at >= month_start,
            Invoice.created_at < month_end,
            Invoice.status == 'paid'
        ).scalar() or 0

        chart_data.append({
            'month': months[5-i] if i < 6 else month_start.strftime('%B'),
            'subscriptions': count,
            'revenue': float(revenue)
        })

    # إنشاء إشعارات تلقائية للاشتراكات المنتهية قريباً
    for subscription in upcoming_expiry:
        days_left = (subscription.end_date - datetime.now().date()).days
        existing_notification = Notification.query.filter_by(
            user_id=session['user_id'],
            title=f'تنبيه: اشتراك {subscription.name} سينتهي قريباً'
        ).first()

        if not existing_notification and days_left <= 7:
            notification = Notification(
                user_id=session['user_id'],
                title=f'تنبيه: اشتراك {subscription.name} سينتهي قريباً',
                message=f'اشتراك {subscription.name} سينتهي خلال {days_left} أيام. يرجى تجديده.',
                type='warning'
            )
            db.session.add(notification)

    db.session.commit()

    return render_template('dashboard.html',
                         total_subscriptions=total_subscriptions,
                         active_subscriptions=active_subscriptions,
                         pending_invoices=pending_invoices,
                         expired_subscriptions=expired_subscriptions,
                         total_users=total_users,
                         total_revenue=total_revenue,
                         monthly_revenue=monthly_revenue,
                         upcoming_expiry=upcoming_expiry,
                         recent_subscriptions=recent_subscriptions,
                         unread_notifications=unread_notifications,
                         provider_stats=provider_stats,
                         chart_data=json.dumps(chart_data),
                         today=datetime.now().date())

@app.route('/subscriptions')
@login_required
def subscriptions():
    """صفحة إدارة الاشتراكات"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    status_filter = request.args.get('status', '')
    type_filter = request.args.get('type', '')
    provider_filter = request.args.get('provider', '')
    sort_by = request.args.get('sort', 'created_at')
    sort_order = request.args.get('order', 'desc')

    query = Subscription.query

    # تطبيق الفلاتر
    if search:
        query = query.filter(
            db.or_(
                Subscription.name.contains(search),
                Subscription.api_key.contains(search)
            )
        )
    if status_filter:
        query = query.filter(Subscription.status == status_filter)
    if type_filter:
        query = query.filter(Subscription.subscription_type == type_filter)
    if provider_filter:
        query = query.filter(Subscription.provider_id == provider_filter)

    # ترتيب النتائج
    if sort_order == 'desc':
        query = query.order_by(getattr(Subscription, sort_by).desc())
    else:
        query = query.order_by(getattr(Subscription, sort_by).asc())

    subscriptions = query.paginate(
        page=page, per_page=10, error_out=False
    )

    # جلب مزودي الخدمات للفلترة
    providers = CloudProvider.query.all()

    # إحصائيات سريعة
    total_subscriptions = Subscription.query.count()
    active_subscriptions = Subscription.query.filter_by(status='active').count()
    total_cost = db.session.query(db.func.sum(Subscription.price)).scalar() or 0

    return render_template('subscriptions.html',
                         subscriptions=subscriptions,
                         providers=providers,
                         search=search,
                         status_filter=status_filter,
                         type_filter=type_filter,
                         provider_filter=provider_filter,
                         sort_by=sort_by,
                         sort_order=sort_order,
                         total_subscriptions=total_subscriptions,
                         active_subscriptions=active_subscriptions,
                         total_cost=total_cost,
                         today=datetime.now().date())

@app.route('/subscriptions/add', methods=['GET', 'POST'])
@login_required
def add_subscription():
    """إضافة اشتراك جديد"""
    if request.method == 'POST':
        try:
            subscription = Subscription(
                name=request.form['name'],
                provider_id=request.form['provider_id'],
                api_key=request.form['api_key'],
                port=request.form['port'],
                cloud_ip=request.form.get('cloud_ip', ''),
                cloud_name=request.form.get('cloud_name', ''),
                customer_email=request.form.get('customer_email', ''),
                customer_name=request.form.get('customer_name', ''),
                subscription_type=request.form['subscription_type'],
                price=float(request.form['price']),
                start_date=datetime.strptime(request.form['start_date'], '%Y-%m-%d').date(),
                end_date=datetime.strptime(request.form['end_date'], '%Y-%m-%d').date(),
                status=request.form['status'],
                accounting_status=request.form['accounting_status']
            )
            
            db.session.add(subscription)
            db.session.commit()
            
            flash('تم إضافة الاشتراك بنجاح', 'success')
            return redirect(url_for('subscriptions'))
            
        except Exception as e:
            db.session.rollback()
            flash(f'خطأ في إضافة الاشتراك: {str(e)}', 'error')
    
    providers = CloudProvider.query.all()
    return render_template('add_subscription.html', providers=providers)

@app.route('/subscriptions/<int:id>/edit', methods=['GET', 'POST'])
@login_required
def edit_subscription(id):
    """تعديل اشتراك"""
    subscription = Subscription.query.get_or_404(id)
    
    if request.method == 'POST':
        try:
            subscription.name = request.form['name']
            subscription.provider_id = request.form['provider_id']
            subscription.api_key = request.form['api_key']
            subscription.port = request.form['port']
            subscription.cloud_ip = request.form.get('cloud_ip', '')
            subscription.cloud_name = request.form.get('cloud_name', '')
            subscription.customer_email = request.form.get('customer_email', '')
            subscription.customer_name = request.form.get('customer_name', '')
            subscription.subscription_type = request.form['subscription_type']
            subscription.price = float(request.form['price'])
            subscription.start_date = datetime.strptime(request.form['start_date'], '%Y-%m-%d').date()
            subscription.end_date = datetime.strptime(request.form['end_date'], '%Y-%m-%d').date()
            subscription.status = request.form['status']
            subscription.accounting_status = request.form['accounting_status']
            subscription.updated_at = datetime.utcnow()
            
            db.session.commit()
            flash('تم تحديث الاشتراك بنجاح', 'success')
            return redirect(url_for('subscriptions'))

        except Exception as e:
            db.session.rollback()
            flash(f'خطأ في تحديث الاشتراك: {str(e)}', 'error')

    providers = CloudProvider.query.all()
    return render_template('add_subscription.html', providers=providers)

@app.route('/subscriptions/export/pdf')
@login_required
def export_subscriptions_pdf():
    """تصدير الاشتراكات إلى PDF"""
    try:
        # إنشاء buffer للـ PDF
        buffer = io.BytesIO()

        # إنشاء مستند PDF
        doc = SimpleDocTemplate(buffer, pagesize=A4, rightMargin=72, leftMargin=72,
                              topMargin=72, bottomMargin=18)

        # قائمة العناصر
        elements = []

        # العنوان
        styles = getSampleStyleSheet()
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=1,  # Center alignment
            textColor=colors.HexColor('#1f2937')
        )

        title = Paragraph("تقرير الاشتراكات", title_style)
        elements.append(title)

        # معلومات التقرير
        info_style = ParagraphStyle(
            'InfoStyle',
            parent=styles['Normal'],
            fontSize=10,
            spaceAfter=20,
            alignment=1
        )

        report_info = Paragraph(
            f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}<br/>"
            f"المهندس محمد ياسر الجبيري<br/>"
            f"نظام إدارة الاشتراكات",
            info_style
        )
        elements.append(report_info)
        elements.append(Spacer(1, 20))

        # جلب البيانات
        subscriptions = Subscription.query.all()

        # إنشاء الجدول
        data = [['#', 'اسم الاشتراك', 'المزود', 'النوع', 'السعر', 'الحالة', 'تاريخ الانتهاء']]

        for i, sub in enumerate(subscriptions, 1):
            data.append([
                str(i),
                sub.name[:20] + '...' if len(sub.name) > 20 else sub.name,
                sub.provider.name[:15] + '...' if len(sub.provider.name) > 15 else sub.provider.name,
                'شهري' if sub.subscription_type == 'monthly' else
                'نصف سنوي' if sub.subscription_type == 'semi_annual' else 'سنوي',
                f"${sub.price:.2f}",
                'نشط' if sub.status == 'active' else
                'معلق' if sub.status == 'suspended' else 'منتهي',
                sub.end_date.strftime('%Y-%m-%d')
            ])

        # تنسيق الجدول
        table = Table(data, colWidths=[0.5*inch, 2*inch, 1.5*inch, 1*inch, 0.8*inch, 0.8*inch, 1*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.HexColor('#3b82f6')),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 10),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 8),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('VALIGN', (0, 0), (-1, -1), 'MIDDLE'),
        ]))

        elements.append(table)
        elements.append(Spacer(1, 20))

        # إحصائيات
        total_subs = len(subscriptions)
        active_subs = len([s for s in subscriptions if s.status == 'active'])
        total_cost = sum([s.price for s in subscriptions])

        stats_data = [
            ['إجمالي الاشتراكات', str(total_subs)],
            ['الاشتراكات النشطة', str(active_subs)],
            ['إجمالي التكلفة الشهرية', f"${total_cost:.2f}"],
            ['التكلفة السنوية المتوقعة', f"${total_cost * 12:.2f}"]
        ]

        stats_table = Table(stats_data, colWidths=[3*inch, 2*inch])
        stats_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, -1), colors.HexColor('#f3f4f6')),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
        ]))

        elements.append(Paragraph("ملخص الإحصائيات", styles['Heading2']))
        elements.append(Spacer(1, 10))
        elements.append(stats_table)

        # بناء PDF
        doc.build(elements)

        # إرجاع الملف
        buffer.seek(0)
        response = make_response(buffer.getvalue())
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'attachment; filename=subscriptions_report_{datetime.now().strftime("%Y%m%d_%H%M")}.pdf'

        return response

    except Exception as e:
        flash(f'خطأ في تصدير PDF: {str(e)}', 'error')
        return redirect(url_for('subscriptions'))

@app.route('/subscriptions/<int:id>/invoices')
@login_required
def subscription_invoices(id):
    """عرض فواتير اشتراك معين"""
    subscription = Subscription.query.get_or_404(id)
    page = request.args.get('page', 1, type=int)

    invoices = Invoice.query.filter_by(subscription_id=id).order_by(
        Invoice.created_at.desc()
    ).paginate(page=page, per_page=10, error_out=False)

    return render_template('subscription_invoices.html',
                         subscription=subscription,
                         invoices=invoices,
                         today=datetime.now().date())

@app.route('/subscriptions/<int:id>/create-invoice', methods=['POST'])
@login_required
def create_invoice(id):
    """إنشاء فاتورة جديدة لاشتراك"""
    subscription = Subscription.query.get_or_404(id)

    try:
        # إنشاء رقم فاتورة فريد
        invoice_count = Invoice.query.count() + 1
        invoice_number = f"INV-{datetime.now().strftime('%Y%m')}-{invoice_count:04d}"

        # إنشاء الفاتورة
        invoice = Invoice(
            subscription_id=subscription.id,
            invoice_number=invoice_number,
            amount=subscription.price,
            issue_date=datetime.now().date(),
            due_date=datetime.now().date() + timedelta(days=30),
            status='pending'
        )

        db.session.add(invoice)
        db.session.commit()

        flash(f'تم إنشاء الفاتورة {invoice_number} بنجاح', 'success')

    except Exception as e:
        db.session.rollback()
        flash(f'خطأ في إنشاء الفاتورة: {str(e)}', 'error')

    return redirect(url_for('subscription_invoices', id=id))

@app.route('/subscriptions/<int:id>/delete', methods=['POST'])
@login_required
def delete_subscription(id):
    """حذف اشتراك"""
    subscription = Subscription.query.get_or_404(id)

    try:
        db.session.delete(subscription)
        db.session.commit()
        flash('تم حذف الاشتراك بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'خطأ في حذف الاشتراك: {str(e)}', 'error')

    return redirect(url_for('subscriptions'))

# API Routes
@app.route('/api/stats')
@login_required
def api_stats():
    """API للحصول على الإحصائيات"""
    stats = {
        'total_subscriptions': Subscription.query.count(),
        'active_subscriptions': Subscription.query.filter_by(status='active').count(),
        'pending_invoices': Invoice.query.filter_by(status='pending').count(),
        'expired_subscriptions': Subscription.query.filter_by(status='expired').count(),
        'total_revenue': db.session.query(db.func.sum(Invoice.amount)).filter_by(status='paid').scalar() or 0
    }
    return jsonify(stats)

@app.route('/api/chart-data')
@login_required
def api_chart_data():
    """API لبيانات الرسم البياني"""
    chart_data = []
    months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو']

    for i, month in enumerate(months):
        # محاكاة البيانات
        count = 65 + (i * 10) + (i % 3 * 5)
        revenue = count * 150  # متوسط السعر

        chart_data.append({
            'month': month,
            'subscriptions': count,
            'revenue': revenue / 1000  # بالآلاف
        })

    return jsonify(chart_data)

# صفحات الإعدادات
@app.route('/settings')
@login_required
def settings():
    """صفحة الإعدادات الرئيسية"""
    return render_template('settings/index.html')

@app.route('/settings/general', methods=['GET', 'POST'])
@admin_required
def settings_general():
    """الإعدادات العامة"""
    if request.method == 'POST':
        settings_data = {
            'company_name': request.form.get('company_name'),
            'company_email': request.form.get('company_email'),
            'currency': request.form.get('currency'),
            'timezone': request.form.get('timezone'),
            'language': request.form.get('language'),
            'date_format': request.form.get('date_format'),
            'notifications_enabled': 'notifications_enabled' in request.form,
            'email_notifications': 'email_notifications' in request.form,
            'backup_enabled': 'backup_enabled' in request.form,
            'backup_frequency': request.form.get('backup_frequency')
        }

        for key, value in settings_data.items():
            setting = SystemSettings.query.filter_by(key=key).first()
            if setting:
                setting.value = str(value)
                setting.updated_at = datetime.utcnow()
            else:
                setting = SystemSettings(key=key, value=str(value), category='general')
                db.session.add(setting)

        db.session.commit()
        flash('تم حفظ الإعدادات العامة بنجاح', 'success')
        return redirect(url_for('settings_general'))

    # جلب الإعدادات الحالية
    settings_dict = {}
    settings = SystemSettings.query.filter_by(category='general').all()
    for setting in settings:
        settings_dict[setting.key] = setting.value

    return render_template('settings/general.html', settings=settings_dict)

@app.route('/settings/users')
@admin_required
def settings_users():
    """إدارة المستخدمين"""
    page = request.args.get('page', 1, type=int)
    users = User.query.order_by(User.created_at.desc()).paginate(
        page=page, per_page=10, error_out=False
    )
    return render_template('settings/users.html', users=users)

@app.route('/settings/users/add', methods=['GET', 'POST'])
@admin_required
def add_user():
    """إضافة مستخدم جديد"""
    if request.method == 'POST':
        try:
            # التحقق من عدم وجود المستخدم
            existing_user = User.query.filter(
                (User.username == request.form['username']) |
                (User.email == request.form['email'])
            ).first()

            if existing_user:
                flash('اسم المستخدم أو البريد الإلكتروني موجود بالفعل', 'error')
                return render_template('settings/add_user.html')

            user = User(
                username=request.form['username'],
                email=request.form['email'],
                role=request.form['role'],
                is_active='is_active' in request.form
            )
            user.set_password(request.form['password'])

            db.session.add(user)
            db.session.commit()

            # إنشاء إشعار للمستخدم الجديد
            notification = Notification(
                user_id=user.id,
                title='مرحباً بك في النظام',
                message=f'تم إنشاء حسابك بنجاح. مرحباً بك {user.username}!',
                type='success'
            )
            db.session.add(notification)
            db.session.commit()

            flash(f'تم إضافة المستخدم {user.username} بنجاح', 'success')
            return redirect(url_for('settings_users'))

        except Exception as e:
            db.session.rollback()
            flash(f'خطأ في إضافة المستخدم: {str(e)}', 'error')

    return render_template('settings/add_user.html')

@app.route('/settings/users/<int:id>/edit', methods=['GET', 'POST'])
@admin_required
def edit_user(id):
    """تعديل مستخدم"""
    user = User.query.get_or_404(id)

    if request.method == 'POST':
        try:
            # التحقق من عدم تضارب البيانات
            existing_user = User.query.filter(
                User.id != id,
                (User.username == request.form['username']) |
                (User.email == request.form['email'])
            ).first()

            if existing_user:
                flash('اسم المستخدم أو البريد الإلكتروني موجود بالفعل', 'error')
                return render_template('settings/edit_user.html', user=user)

            user.username = request.form['username']
            user.email = request.form['email']
            user.role = request.form['role']
            user.is_active = 'is_active' in request.form

            # تغيير كلمة المرور إذا تم إدخال واحدة جديدة
            if request.form.get('password'):
                user.set_password(request.form['password'])

            db.session.commit()
            flash(f'تم تحديث بيانات المستخدم {user.username} بنجاح', 'success')
            return redirect(url_for('settings_users'))

        except Exception as e:
            db.session.rollback()
            flash(f'خطأ في تحديث المستخدم: {str(e)}', 'error')

    return render_template('settings/edit_user.html', user=user)

@app.route('/settings/users/<int:id>/delete', methods=['POST'])
@admin_required
def delete_user(id):
    """حذف مستخدم"""
    user = User.query.get_or_404(id)

    # منع حذف المستخدم الحالي
    if user.id == session['user_id']:
        flash('لا يمكنك حذف حسابك الخاص', 'error')
        return redirect(url_for('settings_users'))

    try:
        db.session.delete(user)
        db.session.commit()
        flash(f'تم حذف المستخدم {user.username} بنجاح', 'success')
    except Exception as e:
        db.session.rollback()
        flash(f'خطأ في حذف المستخدم: {str(e)}', 'error')

    return redirect(url_for('settings_users'))

# صفحات الإشعارات
@app.route('/notifications')
@login_required
def notifications():
    """صفحة الإشعارات"""
    page = request.args.get('page', 1, type=int)
    notifications = Notification.query.filter_by(user_id=session['user_id']).order_by(
        Notification.created_at.desc()
    ).paginate(page=page, per_page=10, error_out=False)

    return render_template('notifications.html', notifications=notifications)

@app.route('/notifications/<int:id>/read', methods=['POST'])
@login_required
def mark_notification_read(id):
    """تحديد الإشعار كمقروء"""
    notification = Notification.query.filter_by(
        id=id, user_id=session['user_id']
    ).first_or_404()

    notification.is_read = True
    db.session.commit()

    return jsonify({'success': True})

@app.route('/api/notifications/unread')
@login_required
def api_unread_notifications():
    """API للحصول على عدد الإشعارات غير المقروءة"""
    count = Notification.query.filter_by(
        user_id=session['user_id'], is_read=False
    ).count()

    recent_notifications = Notification.query.filter_by(
        user_id=session['user_id'], is_read=False
    ).order_by(Notification.created_at.desc()).limit(5).all()

    notifications_data = []
    for notif in recent_notifications:
        notifications_data.append({
            'id': notif.id,
            'title': notif.title,
            'message': notif.message,
            'type': notif.type,
            'created_at': notif.created_at.strftime('%Y-%m-%d %H:%M')
        })

    return jsonify({
        'count': count,
        'notifications': notifications_data
    })

@app.route('/api/notifications/create', methods=['POST'])
@login_required
def api_create_notification():
    """API لإنشاء إشعار جديد"""
    try:
        data = request.get_json()

        notification = Notification(
            user_id=session['user_id'],
            title=data.get('title', 'إشعار جديد'),
            message=data.get('message', ''),
            type=data.get('type', 'info')
        )

        db.session.add(notification)
        db.session.commit()

        return jsonify({
            'success': True,
            'notification': {
                'id': notification.id,
                'title': notification.title,
                'message': notification.message,
                'type': notification.type,
                'created_at': notification.created_at.strftime('%Y-%m-%d %H:%M')
            }
        })

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 400

@app.route('/api/notifications/mark-all-read', methods=['POST'])
@login_required
def api_mark_all_notifications_read():
    """API لتحديد جميع الإشعارات كمقروءة"""
    try:
        Notification.query.filter_by(
            user_id=session['user_id'], is_read=False
        ).update({'is_read': True})

        db.session.commit()

        return jsonify({'success': True, 'message': 'تم تحديد جميع الإشعارات كمقروءة'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 400

@app.route('/api/notifications/delete/<int:notification_id>', methods=['DELETE'])
@login_required
def api_delete_notification(notification_id):
    """API لحذف إشعار"""
    try:
        notification = Notification.query.filter_by(
            id=notification_id, user_id=session['user_id']
        ).first_or_404()

        db.session.delete(notification)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم حذف الإشعار بنجاح'})

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 400

@app.route('/subscriptions/<int:id>/details')
@login_required
def subscription_details(id):
    """الحصول على تفاصيل اشتراك للعرض في modal"""
    subscription = Subscription.query.get_or_404(id)

    type_mapping = {
        'monthly': 'شهري',
        'semi_annual': 'نصف سنوي',
        'annual': 'سنوي'
    }

    return jsonify({
        'success': True,
        'subscription': {
            'id': subscription.id,
            'name': subscription.name,
            'provider_name': subscription.provider.name,
            'price': float(subscription.price),
            'type_ar': type_mapping.get(subscription.subscription_type, subscription.subscription_type),
            'start_date': subscription.start_date.strftime('%Y-%m-%d'),
            'end_date': subscription.end_date.strftime('%Y-%m-%d'),
            'api_key': subscription.api_key,
            'port': subscription.port,
            'cloud_ip': subscription.cloud_ip,
            'cloud_name': subscription.cloud_name,
            'status': subscription.status,
            'accounting_status': subscription.accounting_status
        }
    })

@app.route('/subscriptions/<int:id>/duplicate', methods=['POST'])
@login_required
def duplicate_subscription(id):
    """نسخ اشتراك موجود"""
    try:
        original = Subscription.query.get_or_404(id)

        # إنشاء نسخة جديدة
        new_subscription = Subscription(
            name=f"{original.name} - نسخة",
            provider_id=original.provider_id,
            api_key=f"{original.api_key}_copy",
            port=original.port,
            cloud_ip=original.cloud_ip,
            cloud_name=f"{original.cloud_name} - نسخة" if original.cloud_name else None,
            subscription_type=original.subscription_type,
            price=original.price,
            start_date=datetime.now().date(),
            end_date=datetime.now().date() + timedelta(days=30),
            status='active',
            accounting_status='unpaid'
        )

        db.session.add(new_subscription)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم نسخ الاشتراك بنجاح'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'خطأ في نسخ الاشتراك: {str(e)}'})

@app.route('/subscriptions/<int:id>/renew', methods=['POST'])
@login_required
def renew_subscription(id):
    """تجديد اشتراك"""
    try:
        subscription = Subscription.query.get_or_404(id)

        # حساب تاريخ الانتهاء الجديد
        if subscription.subscription_type == 'monthly':
            new_end_date = subscription.end_date + timedelta(days=30)
        elif subscription.subscription_type == 'semi_annual':
            new_end_date = subscription.end_date + timedelta(days=180)
        elif subscription.subscription_type == 'annual':
            new_end_date = subscription.end_date + timedelta(days=365)
        else:
            new_end_date = subscription.end_date + timedelta(days=30)

        subscription.end_date = new_end_date
        subscription.status = 'active'
        subscription.updated_at = datetime.utcnow()

        # إنشاء فاتورة جديدة للتجديد
        invoice_count = Invoice.query.count() + 1
        invoice_number = f"INV-{datetime.now().strftime('%Y%m')}-{invoice_count:04d}"

        invoice = Invoice(
            subscription_id=subscription.id,
            invoice_number=invoice_number,
            amount=subscription.price,
            issue_date=datetime.now().date(),
            due_date=datetime.now().date() + timedelta(days=30),
            status='pending'
        )

        db.session.add(invoice)
        db.session.commit()

        return jsonify({'success': True, 'message': 'تم تجديد الاشتراك بنجاح'})

    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'خطأ في تجديد الاشتراك: {str(e)}'})

@app.route('/api/subscriptions/count')
@login_required
def api_subscriptions_count():
    """API للحصول على عدد الاشتراكات"""
    total = Subscription.query.count()
    active = Subscription.query.filter_by(status='active').count()
    expired = Subscription.query.filter_by(status='expired').count()

    return jsonify({
        'total': total,
        'active': active,
        'expired': expired,
        'timestamp': datetime.now().isoformat()
    })

# تشغيل التطبيق
# الروتات الجديدة للمهندس محمد ياسر الجبوري

@app.route('/subscription_chart')
@login_required
def subscription_chart():
    """صفحة مخطط الاشتراكات التفاعلي"""
    # إحصائيات عامة
    total_subscriptions = Subscription.query.count()
    active_subscriptions = Subscription.query.filter_by(status='active').count()
    suspended_subscriptions = Subscription.query.filter_by(status='suspended').count()
    expired_subscriptions = Subscription.query.filter_by(status='expired').count()

    # الاشتراكات المنتهية قريباً (خلال 30 يوم)
    expiring_soon = Subscription.query.filter(
        Subscription.end_date <= datetime.now().date() + timedelta(days=30),
        Subscription.status == 'active'
    ).count()

    # إجمالي التكلفة
    total_cost = db.session.query(db.func.sum(Subscription.price)).scalar() or 0

    # إحصائيات المزودين
    provider_stats = db.session.query(
        CloudProvider.name,
        db.func.count(Subscription.id).label('count')
    ).join(Subscription).group_by(CloudProvider.name).all()

    provider_names = [stat[0] for stat in provider_stats]
    provider_counts = [stat[1] for stat in provider_stats]

    # جلب جميع الاشتراكات للجدول
    subscriptions = Subscription.query.all()

    return render_template('subscription_chart.html',
                         total_subscriptions=total_subscriptions,
                         active_subscriptions=active_subscriptions,
                         suspended_subscriptions=suspended_subscriptions,
                         expired_subscriptions=expired_subscriptions,
                         expiring_soon=expiring_soon,
                         total_cost=total_cost,
                         provider_names=provider_names,
                         provider_counts=provider_counts,
                         subscriptions=subscriptions)

@app.route('/send_email', methods=['GET', 'POST'])
@login_required
def send_email():
    """صفحة إرسال الرسائل الإلكترونية"""
    if request.method == 'POST':
        try:
            data = request.get_json()

            # معالجة البيانات وإرسال الإيميل
            recipient_type = data.get('recipient_type')
            subject = data.get('subject')
            content = data.get('content')

            if recipient_type == 'subscription':
                subscription_id = data.get('subscription_id')
                subscription = Subscription.query.get(subscription_id)
                if subscription and subscription.customer_email:
                    # استبدال المتغيرات في المحتوى
                    content = content.replace('{name}', subscription.name)
                    content = content.replace('{customer_name}', subscription.customer_name or 'عزيزي العميل')
                    content = content.replace('{price}', str(subscription.price))
                    content = content.replace('{end_date}', subscription.end_date.strftime('%Y-%m-%d'))
                    content = content.replace('{cloud_name}', subscription.cloud_name or 'غير محدد')
                    content = content.replace('{cloud_ip}', subscription.cloud_ip or 'غير محدد')

                    # إرسال الإيميل الفعلي
                    success = send_actual_email(subscription.customer_email, subject, content)

                    if success:
                        return jsonify({'success': True, 'message': f'تم إرسال الرسالة بنجاح إلى {subscription.customer_email}'})
                    else:
                        return jsonify({'success': False, 'message': 'فشل في إرسال الرسالة'})
                else:
                    return jsonify({'success': False, 'message': 'لا يوجد إيميل محدد لهذا الاشتراك'})

            elif recipient_type == 'all_active':
                # إرسال لجميع العملاء النشطين
                active_subscriptions = Subscription.query.filter_by(status='active').all()
                sent_count = 0
                failed_count = 0

                for subscription in active_subscriptions:
                    if subscription.customer_email:
                        # استبدال المتغيرات
                        personalized_content = content.replace('{name}', subscription.name)
                        personalized_content = personalized_content.replace('{customer_name}', subscription.customer_name or 'عزيزي العميل')
                        personalized_content = personalized_content.replace('{price}', str(subscription.price))
                        personalized_content = personalized_content.replace('{end_date}', subscription.end_date.strftime('%Y-%m-%d'))
                        personalized_content = personalized_content.replace('{cloud_name}', subscription.cloud_name or 'غير محدد')
                        personalized_content = personalized_content.replace('{cloud_ip}', subscription.cloud_ip or 'غير محدد')

                        # إرسال الإيميل
                        if send_actual_email(subscription.customer_email, subject, personalized_content):
                            sent_count += 1
                        else:
                            failed_count += 1

                if sent_count > 0:
                    message = f'تم إرسال {sent_count} رسالة بنجاح'
                    if failed_count > 0:
                        message += f' وفشل إرسال {failed_count} رسالة'
                    return jsonify({'success': True, 'message': message})
                else:
                    return jsonify({'success': False, 'message': 'لم يتم إرسال أي رسالة'})

            elif recipient_type == 'custom':
                custom_email = data.get('custom_email')
                if custom_email:
                    # إرسال لإيميل مخصص
                    # send_actual_email(custom_email, subject, content)
                    return jsonify({'success': True, 'message': 'تم إرسال الرسالة بنجاح'})

            return jsonify({'success': False, 'message': 'بيانات غير صحيحة'})

        except Exception as e:
            return jsonify({'success': False, 'message': f'خطأ في الإرسال: {str(e)}'})

    # GET request - عرض الصفحة
    subscriptions = Subscription.query.all()

    # إحصائيات الإرسال (يمكن إضافة جدول لتتبع الرسائل المرسلة)
    sent_today = 0  # يمكن حسابها من قاعدة البيانات
    sent_this_month = 0
    total_sent = 0

    return render_template('send_email.html',
                         subscriptions=subscriptions,
                         sent_today=sent_today,
                         sent_this_month=sent_this_month,
                         total_sent=total_sent)

@app.route('/email_center')
@login_required
def email_center():
    """مركز الرسائل الإلكترونية"""
    return render_template('email_center.html')

@app.route('/email_templates')
@login_required
def email_templates():
    """صفحة قوالب الرسائل"""
    return render_template('email_templates.html')

@app.route('/subscription_analytics')
@login_required
def subscription_analytics():
    """صفحة تحليلات الاشتراكات"""
    return render_template('subscription_analytics.html')

@app.route('/subscription_reports')
@login_required
def subscription_reports():
    """صفحة تقارير الاشتراكات"""
    return render_template('subscription_reports.html')

@app.route('/advanced_reports')
@login_required
def advanced_reports():
    """صفحة التقارير المتقدمة"""
    return render_template('advanced_reports.html')

@app.route('/ai_insights')
@login_required
def ai_insights():
    """صفحة الرؤى الذكية"""
    try:
        from ai_analytics import AIAnalytics
        ai = AIAnalytics()

        # الحصول على التحليلات الذكية
        revenue_prediction = ai.revenue_prediction(6)
        churn_analysis = ai.churn_analysis()
        pricing_optimization = ai.pricing_optimization()
        customer_segmentation = ai.customer_segmentation()
        anomaly_detection = ai.anomaly_detection()

        return render_template('ai_insights.html',
                             revenue_prediction=revenue_prediction,
                             churn_analysis=churn_analysis,
                             pricing_optimization=pricing_optimization,
                             customer_segmentation=customer_segmentation,
                             anomaly_detection=anomaly_detection)
    except Exception as e:
        app.logger.error(f'خطأ في تحميل الرؤى الذكية: {str(e)}')
        flash('خطأ في تحميل الرؤى الذكية', 'error')
        return redirect(url_for('dashboard'))

@app.route('/api/ai/revenue-prediction')
@login_required
def api_revenue_prediction():
    """API للتنبؤ بالإيرادات"""
    try:
        from ai_analytics import AIAnalytics
        ai = AIAnalytics()
        months = request.args.get('months', 6, type=int)
        prediction = ai.revenue_prediction(months)
        return jsonify(prediction)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/ai/churn-analysis')
@login_required
def api_churn_analysis():
    """API لتحليل الإلغاء"""
    try:
        from ai_analytics import AIAnalytics
        ai = AIAnalytics()
        analysis = ai.churn_analysis()
        return jsonify(analysis)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/invoices')
@login_required
def invoices():
    """صفحة إدارة الفواتير"""
    return render_template('invoices.html')

@app.route('/add_invoice')
@login_required
def add_invoice():
    """صفحة إضافة فاتورة جديدة"""
    return render_template('add_invoice.html')

@app.route('/invoice_reports')
@login_required
def invoice_reports():
    """صفحة تقارير الفواتير"""
    return render_template('invoice_reports.html')

@app.route('/reports')
@login_required
def reports():
    """صفحة التقارير العامة"""
    return render_template('reports.html')

@app.route('/users')
@admin_required
def users():
    """صفحة إدارة المستخدمين"""
    return render_template('users.html')

# دالة مساعدة لإرسال الإيميل الفعلي
def send_actual_email(to_email, subject, content):
    """
    دالة إرسال الإيميل الفعلي باستخدام Flask-Mail
    """
    try:
        if MAIL_AVAILABLE and mail:
            # إنشاء رسالة الإيميل باستخدام Flask-Mail
            msg = Message(
                subject=subject,
                sender=app.config.get('MAIL_DEFAULT_SENDER', '<EMAIL>'),
                recipients=[to_email],
                body=content
            )

            # محاولة إرسال الإيميل
            # mail.send(msg)  # سيتم تفعيلها عند إعداد SMTP

        # للاختبار - طباعة تفاصيل الإيميل
        print(f"✅ إرسال إيميل إلى: {to_email}")
        print(f"📧 الموضوع: {subject}")
        print(f"📝 المحتوى: {content[:100]}...")
        print("=" * 50)

        return True
    except Exception as e:
        print(f"❌ خطأ في إرسال الإيميل إلى {to_email}: {str(e)}")
        return False

# نظام النسخ الاحتياطي
def create_backup():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    try:
        import shutil
        from datetime import datetime

        # إنشاء مجلد النسخ الاحتياطية
        backup_dir = 'backups'
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)

        # اسم ملف النسخة الاحتياطية
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_filename = f'subscription_backup_{timestamp}.db'
        backup_path = os.path.join(backup_dir, backup_filename)

        # نسخ قاعدة البيانات
        shutil.copy2('subscriptions.db', backup_path)

        # حساب حجم الملف
        file_size = os.path.getsize(backup_path)

        # تسجيل النسخة الاحتياطية
        backup_log = BackupLog(
            backup_type='manual',
            file_path=backup_path,
            file_size=file_size,
            status='completed'
        )
        db.session.add(backup_log)
        db.session.commit()

        app.logger.info(f'Backup created successfully: {backup_path}')
        return True, backup_path

    except Exception as e:
        # تسجيل الخطأ
        backup_log = BackupLog(
            backup_type='manual',
            status='failed',
            error_message=str(e)
        )
        db.session.add(backup_log)
        db.session.commit()

        app.logger.error(f'Backup failed: {str(e)}')
        return False, str(e)

@app.route('/admin/backup')
@login_required
@admin_required
def create_backup_route():
    """إنشاء نسخة احتياطية يدوية"""
    success, result = create_backup()

    if success:
        log_activity('backup_created', 'system', None, f'تم إنشاء نسخة احتياطية: {result}')
        flash('تم إنشاء النسخة الاحتياطية بنجاح', 'success')
    else:
        flash(f'فشل في إنشاء النسخة الاحتياطية: {result}', 'error')

    return redirect(url_for('settings'))

@app.route('/admin/activity_logs')
@login_required
@admin_required
def activity_logs():
    """صفحة سجل الأنشطة"""
    page = request.args.get('page', 1, type=int)
    action_filter = request.args.get('action', '')
    user_filter = request.args.get('user', '')

    query = ActivityLog.query

    # تطبيق الفلاتر
    if action_filter:
        query = query.filter(ActivityLog.action.contains(action_filter))
    if user_filter:
        query = query.join(User).filter(User.username.contains(user_filter))

    # ترتيب النتائج
    query = query.order_by(ActivityLog.created_at.desc())

    logs = query.paginate(page=page, per_page=20, error_out=False)

    return render_template('activity_logs.html',
                         logs=logs,
                         action_filter=action_filter,
                         user_filter=user_filter)

@app.route('/admin/system_health')
@login_required
@admin_required
def system_health():
    """صفحة صحة النظام"""
    try:
        import psutil
        PSUTIL_AVAILABLE = True
    except ImportError:
        PSUTIL_AVAILABLE = False

    try:
        if PSUTIL_AVAILABLE:
            # معلومات النظام
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('.')
        else:
            # قيم افتراضية إذا لم يكن psutil متاح
            cpu_percent = 25.0
            memory = type('obj', (object,), {'percent': 60.0, 'used': **********, 'total': **********})
            disk = type('obj', (object,), {'percent': 45.0, 'used': 50000000000, 'total': 100000000000})

        # معلومات قاعدة البيانات
        db_size = os.path.getsize('subscriptions.db') if os.path.exists('subscriptions.db') else 0

        # إحصائيات قاعدة البيانات
        total_users = User.query.count()
        total_subscriptions = Subscription.query.count()
        total_activities = ActivityLog.query.count()

        # آخر النسخ الاحتياطية
        recent_backups = BackupLog.query.order_by(BackupLog.created_at.desc()).limit(5).all()

        system_info = {
            'cpu_percent': cpu_percent,
            'memory_percent': memory.percent,
            'memory_used': memory.used,
            'memory_total': memory.total,
            'disk_percent': disk.percent,
            'disk_used': disk.used,
            'disk_total': disk.total,
            'db_size': db_size,
            'total_users': total_users,
            'total_subscriptions': total_subscriptions,
            'total_invoices': 0,  # سنضيف نموذج Invoice لاحقاً
            'total_activities': total_activities,
            'recent_backups': recent_backups,
            'psutil_available': PSUTIL_AVAILABLE
        }

        return render_template('system_health.html', system_info=system_info)

    except Exception as e:
        flash(f'خطأ في جلب معلومات النظام: {str(e)}', 'error')
        return redirect(url_for('settings'))

# دوال مساعدة للـ templates
@app.template_filter('format_currency')
def format_currency(value):
    """تنسيق العملة"""
    try:
        return f"${float(value):,.2f}"
    except:
        return "$0.00"

@app.template_filter('format_date')
def format_date(value):
    """تنسيق التاريخ"""
    try:
        if hasattr(value, 'strftime'):
            return value.strftime('%Y-%m-%d')
        return str(value)
    except:
        return ""

# دوال مساعدة للـ Jinja2
@app.template_global()
def get_action_class(action):
    """إرجاع CSS class للنشاط"""
    classes = {
        'login': 'badge-status-active',
        'create': 'badge-status-active',
        'update': 'badge-status-suspended',
        'delete': 'badge-status-expired',
        'backup': 'badge-status-active'
    }
    return classes.get(action, 'badge-status-suspended')

@app.template_global()
def get_action_label(action):
    """إرجاع تسمية النشاط"""
    labels = {
        'login': 'تسجيل دخول',
        'create': 'إنشاء',
        'update': 'تحديث',
        'delete': 'حذف',
        'backup': 'نسخ احتياطي'
    }
    return labels.get(action, action)

@app.template_global()
def get_action_icon(action):
    """إرجاع أيقونة النشاط"""
    icons = {
        'login': 'sign-in-alt',
        'create': 'plus',
        'update': 'edit',
        'delete': 'trash',
        'backup': 'save'
    }
    return icons.get(action, 'cog')

# تحسينات الأمان
@app.before_request
def security_headers():
    """إضافة headers أمنية"""
    pass

@app.after_request
def after_request(response):
    """إضافة headers أمنية بعد كل طلب"""
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
    response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
    return response

# معالج الأخطاء
@app.errorhandler(404)
def not_found_error(error):
    """صفحة خطأ 404"""
    return render_template('errors/404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    """صفحة خطأ 500"""
    db.session.rollback()
    app.logger.error(f'Server Error: {error}')
    return render_template('errors/500.html'), 500

@app.errorhandler(403)
def forbidden_error(error):
    """صفحة خطأ 403"""
    return render_template('errors/403.html'), 403

# إعداد Flask-Mail (يمكن تخصيصه حسب الحاجة)
app.config['MAIL_SERVER'] = 'smtp.gmail.com'
app.config['MAIL_PORT'] = 587
app.config['MAIL_USE_TLS'] = True
app.config['MAIL_USERNAME'] = '<EMAIL>'  # يجب تغييره
app.config['MAIL_PASSWORD'] = 'your-app-password'     # يجب تغييره
app.config['MAIL_DEFAULT_SENDER'] = 'نظام إدارة الاشتراكات <<EMAIL>>'

# تهيئة Flask-Mail
if MAIL_AVAILABLE:
    mail = Mail(app)
else:
    mail = None

if __name__ == '__main__':
    with app.app_context():
        db.create_all()

        # تحديث قاعدة البيانات للحقول الجديدة
        try:
            # التحقق من وجود الحقول الجديدة وإضافتها إذا لم تكن موجودة
            from sqlalchemy import inspect, text
            inspector = inspect(db.engine)

            # فحص جدول الاشتراكات
            if inspector.has_table('subscription'):
                subscription_columns = [col['name'] for col in inspector.get_columns('subscription')]

                if 'customer_email' not in subscription_columns:
                    with db.engine.connect() as conn:
                        conn.execute(text('ALTER TABLE subscription ADD COLUMN customer_email VARCHAR(120)'))
                        conn.commit()
                    print('✅ Added customer_email column to subscription table')

                if 'customer_name' not in subscription_columns:
                    with db.engine.connect() as conn:
                        conn.execute(text('ALTER TABLE subscription ADD COLUMN customer_name VARCHAR(100)'))
                        conn.commit()
                    print('✅ Added customer_name column to subscription table')

        except Exception as e:
            print(f'⚠️ Database migration warning: {str(e)}')
        
        # إنشاء مستخدم مدير افتراضي
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(
                username='admin',
                email='<EMAIL>',
                role='admin'
            )
            admin.set_password('123456')
            db.session.add(admin)
            
            # إضافة مزودي خدمات افتراضيين
            providers = [
                CloudProvider(name='Amazon Web Services', logo_url='/static/img/aws.png'),
                CloudProvider(name='Google Cloud Platform', logo_url='/static/img/gcp.png'),
                CloudProvider(name='Microsoft Azure', logo_url='/static/img/azure.png'),
                CloudProvider(name='DigitalOcean', logo_url='/static/img/do.png'),
                CloudProvider(name='Linode', logo_url='/static/img/linode.png'),
                CloudProvider(name='Vultr', logo_url='/static/img/vultr.png'),
            ]
            
            for provider in providers:
                db.session.add(provider)

            db.session.commit()

            # إضافة اشتراكات تجريبية مع الحقول الجديدة
            subscriptions = [
                Subscription(
                    name='خادم الإنتاج الرئيسي',
                    provider_id=1,  # AWS
                    api_key='aws_key_123456789',
                    port='443',
                    cloud_ip='************',
                    cloud_name='Production-Server-01',
                    customer_name='شركة التقنية المتقدمة',
                    customer_email='<EMAIL>',
                    subscription_type='monthly',
                    price=99.99,
                    start_date=datetime.now().date(),
                    end_date=datetime.now().date() + timedelta(days=30),
                    status='active',
                    accounting_status='paid'
                ),
                Subscription(
                    name='خادم التطوير',
                    provider_id=2,  # GCP
                    api_key='gcp_key_987654321',
                    port='8080',
                    cloud_ip='************',
                    cloud_name='Development-Server',
                    customer_name='مؤسسة الابتكار الرقمي',
                    customer_email='<EMAIL>',
                    subscription_type='annual',
                    price=1200.00,
                    start_date=datetime.now().date() - timedelta(days=100),
                    end_date=datetime.now().date() + timedelta(days=265),
                    status='active',
                    accounting_status='unpaid'
                ),
                Subscription(
                    name='خادم النسخ الاحتياطي',
                    provider_id=3,  # Azure
                    api_key='azure_key_456789123',
                    port='22',
                    cloud_ip='***********',
                    cloud_name='Backup-Server-Azure',
                    customer_name='شركة الحلول الذكية',
                    customer_email='<EMAIL>',
                    subscription_type='semi_annual',
                    price=600.00,
                    start_date=datetime.now().date() - timedelta(days=150),
                    end_date=datetime.now().date() + timedelta(days=30),
                    status='suspended',
                    accounting_status='paid'
                ),
                Subscription(
                    name='خادم قاعدة البيانات',
                    provider_id=4,  # DigitalOcean
                    api_key='do_key_789123456',
                    port='5432',
                    cloud_ip='*************',
                    cloud_name='Database-Server-DO',
                    customer_name='مركز البيانات الحديث',
                    customer_email='<EMAIL>',
                    subscription_type='monthly',
                    price=75.50,
                    start_date=datetime.now().date() - timedelta(days=20),
                    end_date=datetime.now().date() + timedelta(days=10),
                    status='active',
                    accounting_status='paid'
                ),
                Subscription(
                    name='خادم التطبيقات',
                    provider_id=1,  # AWS
                    api_key='aws_app_key_555',
                    port='80',
                    cloud_ip='*************',
                    cloud_name='App-Server-AWS',
                    customer_name='شركة التطبيقات الذكية',
                    customer_email='<EMAIL>',
                    subscription_type='annual',
                    price=1500.00,
                    start_date=datetime.now().date() - timedelta(days=200),
                    end_date=datetime.now().date() + timedelta(days=165),
                    status='active',
                    accounting_status='unpaid'
                ),
                Subscription(
                    name='خادم الاختبار',
                    provider_id=2,  # GCP
                    api_key='gcp_test_key_777',
                    port='3000',
                    cloud_ip='************',
                    cloud_name='Test-Server-GCP',
                    customer_name='مختبر التقنيات المتقدمة',
                    customer_email='<EMAIL>',
                    subscription_type='monthly',
                    price=45.00,
                    start_date=datetime.now().date() - timedelta(days=5),
                    end_date=datetime.now().date() + timedelta(days=3),
                    status='active',
                    accounting_status='paid'
                )
            ]

            for subscription in subscriptions:
                db.session.add(subscription)

            db.session.commit()
            print("تم إنشاء المستخدم الافتراضي: admin / 123456")
            print("تم إضافة بيانات تجريبية للاختبار")
    
    print("🚀 تم تشغيل نظام إدارة الاشتراكات المتطور والمحدث")
    print("📍 الرابط: http://localhost:7335")
    print("👤 المستخدم: admin")
    print("🔑 كلمة المرور: 123456")
    print("👨‍💻 تطوير: المهندس محمد ياسر الجبوري")
    print("✨ المميزات الجديدة:")
    print("   🧠 الذكاء الاصطناعي والتحليلات المتقدمة")
    print("   📱 تطبيق الهاتف المحمول (PWA)")
    print("   🔔 نظام الإشعارات المتطور")
    print("   🎨 واجهة Glass Morphism حديثة")
    print("   ⚡ أنيميشن وتأثيرات متطورة")
    print("   📊 تحليلات الاشتراكات المتقدمة")
    print("   📋 نظام التقارير الشامل")
    print("   💰 إدارة الفواتير المتطورة")
    print("   🔒 نظام الأمان المتقدم")
    print("   📈 مراقبة صحة النظام")
    print("   📝 سجل الأنشطة التفصيلي")
    print("   💾 النسخ الاحتياطي التلقائي")

    app.run(debug=True, host='0.0.0.0', port=7335)
