{% extends "base.html" %}

{% block title %}إعدادات الألوان والثيمات - نظام إدارة الاشتراكات{% endblock %}
{% block page_title %}إعدادات الألوان والثيمات{% endblock %}
{% block page_description %}تخصيص شامل لألوان وثيمات النظام{% endblock %}

{% block extra_css %}
<style>
.theme-container {
    display: grid;
    grid-template-columns: 300px 1fr 300px;
    gap: 2rem;
    margin-bottom: 2rem;
}

.theme-sidebar {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    height: fit-content;
}

.theme-main {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 2rem;
}

.theme-preview {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    height: fit-content;
}

.section-title {
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.section-icon {
    width: 2rem;
    height: 2rem;
    border-radius: 6px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
}

.preset-themes {
    margin-bottom: 2rem;
}

.theme-preset {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    margin-bottom: 0.75rem;
    cursor: pointer;
    transition: all var(--transition-medium);
}

.theme-preset:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: var(--glass-border-light);
}

.theme-preset.active {
    background: rgba(59, 130, 246, 0.1);
    border-color: var(--primary-color);
}

.theme-colors {
    display: flex;
    gap: 0.25rem;
}

.theme-color {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.theme-info {
    flex: 1;
}

.theme-name {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.theme-description {
    color: var(--text-secondary);
    font-size: 0.75rem;
}

.color-section {
    margin-bottom: 2rem;
}

.color-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
}

.color-input-group {
    margin-bottom: 1rem;
}

.color-label {
    color: var(--text-primary);
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: block;
}

.color-input-wrapper {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    transition: all var(--transition-medium);
}

.color-input-wrapper:focus-within {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.color-picker {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    background: none;
}

.color-text {
    flex: 1;
    background: none;
    border: none;
    color: var(--text-primary);
    font-size: 0.875rem;
    font-family: 'Courier New', monospace;
}

.color-text:focus {
    outline: none;
}

.background-section {
    margin-bottom: 2rem;
}

.background-types {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 1rem;
}

.background-type {
    padding: 1rem;
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-medium);
    background: rgba(255, 255, 255, 0.05);
}

.background-type:hover {
    background: rgba(255, 255, 255, 0.1);
}

.background-type.active {
    background: rgba(59, 130, 246, 0.1);
    border-color: var(--primary-color);
}

.background-type-icon {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.background-type-name {
    color: var(--text-primary);
    font-size: 0.875rem;
    font-weight: 600;
}

.slider-group {
    margin-bottom: 1rem;
}

.slider-label {
    color: var(--text-primary);
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    display: flex;
    justify-content: between;
    align-items: center;
}

.slider-value {
    color: var(--text-secondary);
    font-size: 0.75rem;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

.slider {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: rgba(255, 255, 255, 0.1);
    outline: none;
    -webkit-appearance: none;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.preview-window {
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.preview-header {
    background: var(--surface-color);
    padding: 1rem;
    border-bottom: 1px solid var(--glass-border);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.preview-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
}

.preview-dot.red {
    background: #ef4444;
}

.preview-dot.yellow {
    background: #f59e0b;
}

.preview-dot.green {
    background: #10b981;
}

.preview-content {
    padding: 1.5rem;
    background: var(--background-color);
    min-height: 200px;
}

.preview-card {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.preview-title {
    color: var(--text-primary);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.preview-text {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin-bottom: 1rem;
}

.preview-button {
    background: var(--primary-color);
    color: white;
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 6px;
    font-size: 0.875rem;
    cursor: pointer;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
}

.action-btn {
    flex: 1;
    padding: 0.75rem 1.5rem;
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-medium);
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.1);
}

.action-btn.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-color: var(--primary-color);
}

.action-btn.success {
    background: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

@media (max-width: 1024px) {
    .theme-container {
        grid-template-columns: 1fr;
    }
    
    .color-grid {
        grid-template-columns: 1fr;
    }
    
    .background-types {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 768px) {
    .background-types {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .action-buttons {
        flex-direction: column;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="theme-container">
    <!-- الشريط الجانبي الأيسر -->
    <div class="theme-sidebar">
        <h3 class="section-title">
            <div class="section-icon">
                <i class="fas fa-palette"></i>
            </div>
            الثيمات المحفوظة
        </h3>
        
        <div class="preset-themes">
            <div class="theme-preset active" data-theme="default">
                <div class="theme-colors">
                    <div class="theme-color" style="background: #3b82f6;"></div>
                    <div class="theme-color" style="background: #8b5cf6;"></div>
                    <div class="theme-color" style="background: #10b981;"></div>
                    <div class="theme-color" style="background: #0f172a;"></div>
                </div>
                <div class="theme-info">
                    <div class="theme-name">الثيم الافتراضي</div>
                    <div class="theme-description">الألوان الأساسية للنظام</div>
                </div>
            </div>
            
            <div class="theme-preset" data-theme="dark">
                <div class="theme-colors">
                    <div class="theme-color" style="background: #1f2937;"></div>
                    <div class="theme-color" style="background: #374151;"></div>
                    <div class="theme-color" style="background: #6b7280;"></div>
                    <div class="theme-color" style="background: #000000;"></div>
                </div>
                <div class="theme-info">
                    <div class="theme-name">الثيم المظلم</div>
                    <div class="theme-description">ألوان داكنة مريحة للعين</div>
                </div>
            </div>
            
            <div class="theme-preset" data-theme="ocean">
                <div class="theme-colors">
                    <div class="theme-color" style="background: #0ea5e9;"></div>
                    <div class="theme-color" style="background: #06b6d4;"></div>
                    <div class="theme-color" style="background: #0891b2;"></div>
                    <div class="theme-color" style="background: #0c4a6e;"></div>
                </div>
                <div class="theme-info">
                    <div class="theme-name">ثيم المحيط</div>
                    <div class="theme-description">ألوان زرقاء هادئة</div>
                </div>
            </div>
            
            <div class="theme-preset" data-theme="forest">
                <div class="theme-colors">
                    <div class="theme-color" style="background: #059669;"></div>
                    <div class="theme-color" style="background: #047857;"></div>
                    <div class="theme-color" style="background: #065f46;"></div>
                    <div class="theme-color" style="background: #064e3b;"></div>
                </div>
                <div class="theme-info">
                    <div class="theme-name">ثيم الغابة</div>
                    <div class="theme-description">ألوان خضراء طبيعية</div>
                </div>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="theme-main">
        <h2 class="section-title">
            <div class="section-icon">
                <i class="fas fa-paint-brush"></i>
            </div>
            تخصيص الألوان
        </h2>

        <!-- الألوان الأساسية -->
        <div class="color-section">
            <h4 style="color: var(--text-primary); margin-bottom: 1rem;">الألوان الأساسية</h4>
            <div class="color-grid">
                <div class="color-input-group">
                    <label class="color-label">اللون الأساسي</label>
                    <div class="color-input-wrapper">
                        <input type="color" class="color-picker" id="primaryColor" value="#3b82f6">
                        <input type="text" class="color-text" value="#3b82f6">
                    </div>
                </div>
                
                <div class="color-input-group">
                    <label class="color-label">اللون الثانوي</label>
                    <div class="color-input-wrapper">
                        <input type="color" class="color-picker" id="secondaryColor" value="#8b5cf6">
                        <input type="text" class="color-text" value="#8b5cf6">
                    </div>
                </div>
                
                <div class="color-input-group">
                    <label class="color-label">لون التمييز</label>
                    <div class="color-input-wrapper">
                        <input type="color" class="color-picker" id="accentColor" value="#10b981">
                        <input type="text" class="color-text" value="#10b981">
                    </div>
                </div>
                
                <div class="color-input-group">
                    <label class="color-label">لون الخلفية</label>
                    <div class="color-input-wrapper">
                        <input type="color" class="color-picker" id="backgroundColor" value="#0f172a">
                        <input type="text" class="color-text" value="#0f172a">
                    </div>
                </div>
            </div>
        </div>

        <!-- ألوان النص -->
        <div class="color-section">
            <h4 style="color: var(--text-primary); margin-bottom: 1rem;">ألوان النص</h4>
            <div class="color-grid">
                <div class="color-input-group">
                    <label class="color-label">النص الأساسي</label>
                    <div class="color-input-wrapper">
                        <input type="color" class="color-picker" id="textPrimary" value="#f8fafc">
                        <input type="text" class="color-text" value="#f8fafc">
                    </div>
                </div>
                
                <div class="color-input-group">
                    <label class="color-label">النص الثانوي</label>
                    <div class="color-input-wrapper">
                        <input type="color" class="color-picker" id="textSecondary" value="#cbd5e1">
                        <input type="text" class="color-text" value="#cbd5e1">
                    </div>
                </div>
            </div>
        </div>

        <!-- إعدادات الخلفية -->
        <div class="background-section">
            <h4 style="color: var(--text-primary); margin-bottom: 1rem;">إعدادات الخلفية</h4>
            
            <div class="background-types">
                <div class="background-type active" data-type="gradient">
                    <div class="background-type-icon">
                        <i class="fas fa-fill-drip"></i>
                    </div>
                    <div class="background-type-name">تدرج</div>
                </div>
                
                <div class="background-type" data-type="solid">
                    <div class="background-type-icon">
                        <i class="fas fa-square"></i>
                    </div>
                    <div class="background-type-name">لون واحد</div>
                </div>
                
                <div class="background-type" data-type="image">
                    <div class="background-type-icon">
                        <i class="fas fa-image"></i>
                    </div>
                    <div class="background-type-name">صورة</div>
                </div>
                
                <div class="background-type" data-type="pattern">
                    <div class="background-type-icon">
                        <i class="fas fa-th"></i>
                    </div>
                    <div class="background-type-name">نمط</div>
                </div>
            </div>
            
            <div class="slider-group">
                <div class="slider-label">
                    <span>شفافية الخلفية</span>
                    <span class="slider-value" id="opacityValue">90%</span>
                </div>
                <input type="range" class="slider" id="backgroundOpacity" min="0" max="100" value="90">
            </div>
            
            <div class="slider-group">
                <div class="slider-label">
                    <span>مقدار الضبابية</span>
                    <span class="slider-value" id="blurValue">10px</span>
                </div>
                <input type="range" class="slider" id="blurAmount" min="0" max="30" value="10">
            </div>
            
            <div class="slider-group">
                <div class="slider-label">
                    <span>انحناء الحواف</span>
                    <span class="slider-value" id="radiusValue">12px</span>
                </div>
                <input type="range" class="slider" id="borderRadius" min="0" max="30" value="12">
            </div>
        </div>

        <!-- أزرار الإجراءات -->
        <div class="action-buttons">
            <button class="action-btn" onclick="resetTheme()">
                <i class="fas fa-undo"></i>
                إعادة تعيين
            </button>
            <button class="action-btn" onclick="saveTheme()">
                <i class="fas fa-save"></i>
                حفظ كثيم
            </button>
            <button class="action-btn primary" onclick="applyTheme()">
                <i class="fas fa-check"></i>
                تطبيق التغييرات
            </button>
        </div>
    </div>

    <!-- معاينة الثيم -->
    <div class="theme-preview">
        <h3 class="section-title">
            <div class="section-icon">
                <i class="fas fa-eye"></i>
            </div>
            معاينة مباشرة
        </h3>
        
        <div class="preview-window">
            <div class="preview-header">
                <div class="preview-dot red"></div>
                <div class="preview-dot yellow"></div>
                <div class="preview-dot green"></div>
            </div>
            <div class="preview-content">
                <div class="preview-card">
                    <div class="preview-title">عنوان تجريبي</div>
                    <div class="preview-text">
                        هذا نص تجريبي لمعاينة الألوان والثيم الجديد. 
                        يمكنك رؤية كيف ستبدو الألوان في النظام الفعلي.
                    </div>
                    <button class="preview-button">زر تجريبي</button>
                </div>
                
                <div class="preview-card">
                    <div class="preview-title">بطاقة أخرى</div>
                    <div class="preview-text">
                        معاينة إضافية للتأكد من تناسق الألوان 
                        وجودة التصميم العام.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// إدارة الثيمات والألوان
document.addEventListener('DOMContentLoaded', function() {
    initializeThemeSettings();
});

function initializeThemeSettings() {
    // ربط أحداث تغيير الألوان
    document.querySelectorAll('.color-picker').forEach(picker => {
        picker.addEventListener('change', function() {
            const textInput = this.nextElementSibling;
            textInput.value = this.value;
            updatePreview();
        });
    });
    
    // ربط أحداث النصوص
    document.querySelectorAll('.color-text').forEach(input => {
        input.addEventListener('input', function() {
            const colorPicker = this.previousElementSibling;
            if (this.value.match(/^#[0-9A-F]{6}$/i)) {
                colorPicker.value = this.value;
                updatePreview();
            }
        });
    });
    
    // ربط أحداث المنزلقات
    document.getElementById('backgroundOpacity').addEventListener('input', function() {
        document.getElementById('opacityValue').textContent = this.value + '%';
        updatePreview();
    });
    
    document.getElementById('blurAmount').addEventListener('input', function() {
        document.getElementById('blurValue').textContent = this.value + 'px';
        updatePreview();
    });
    
    document.getElementById('borderRadius').addEventListener('input', function() {
        document.getElementById('radiusValue').textContent = this.value + 'px';
        updatePreview();
    });
    
    // ربط أحداث أنواع الخلفية
    document.querySelectorAll('.background-type').forEach(type => {
        type.addEventListener('click', function() {
            document.querySelectorAll('.background-type').forEach(t => t.classList.remove('active'));
            this.classList.add('active');
            updatePreview();
        });
    });
    
    // ربط أحداث الثيمات المحفوظة
    document.querySelectorAll('.theme-preset').forEach(preset => {
        preset.addEventListener('click', function() {
            document.querySelectorAll('.theme-preset').forEach(p => p.classList.remove('active'));
            this.classList.add('active');
            loadPresetTheme(this.dataset.theme);
        });
    });
}

function updatePreview() {
    const primaryColor = document.getElementById('primaryColor').value;
    const secondaryColor = document.getElementById('secondaryColor').value;
    const accentColor = document.getElementById('accentColor').value;
    const backgroundColor = document.getElementById('backgroundColor').value;
    const textPrimary = document.getElementById('textPrimary').value;
    const textSecondary = document.getElementById('textSecondary').value;
    
    const opacity = document.getElementById('backgroundOpacity').value / 100;
    const blur = document.getElementById('blurAmount').value;
    const radius = document.getElementById('borderRadius').value;
    
    // تطبيق الألوان على المعاينة
    const previewContent = document.querySelector('.preview-content');
    const previewCards = document.querySelectorAll('.preview-card');
    const previewButton = document.querySelector('.preview-button');
    
    previewContent.style.backgroundColor = backgroundColor;
    
    previewCards.forEach(card => {
        card.style.backgroundColor = `rgba(255, 255, 255, ${opacity * 0.1})`;
        card.style.backdropFilter = `blur(${blur}px)`;
        card.style.borderRadius = `${radius}px`;
        card.style.border = `1px solid rgba(255, 255, 255, ${opacity * 0.2})`;
    });
    
    document.querySelectorAll('.preview-title').forEach(title => {
        title.style.color = textPrimary;
    });
    
    document.querySelectorAll('.preview-text').forEach(text => {
        text.style.color = textSecondary;
    });
    
    previewButton.style.backgroundColor = primaryColor;
    previewButton.style.borderRadius = `${radius * 0.5}px`;
}

function loadPresetTheme(themeName) {
    const themes = {
        default: {
            primary: '#3b82f6',
            secondary: '#8b5cf6',
            accent: '#10b981',
            background: '#0f172a',
            textPrimary: '#f8fafc',
            textSecondary: '#cbd5e1'
        },
        dark: {
            primary: '#1f2937',
            secondary: '#374151',
            accent: '#6b7280',
            background: '#000000',
            textPrimary: '#f9fafb',
            textSecondary: '#d1d5db'
        },
        ocean: {
            primary: '#0ea5e9',
            secondary: '#06b6d4',
            accent: '#0891b2',
            background: '#0c4a6e',
            textPrimary: '#f0f9ff',
            textSecondary: '#bae6fd'
        },
        forest: {
            primary: '#059669',
            secondary: '#047857',
            accent: '#065f46',
            background: '#064e3b',
            textPrimary: '#f0fdf4',
            textSecondary: '#bbf7d0'
        }
    };
    
    const theme = themes[themeName];
    if (theme) {
        document.getElementById('primaryColor').value = theme.primary;
        document.getElementById('secondaryColor').value = theme.secondary;
        document.getElementById('accentColor').value = theme.accent;
        document.getElementById('backgroundColor').value = theme.background;
        document.getElementById('textPrimary').value = theme.textPrimary;
        document.getElementById('textSecondary').value = theme.textSecondary;
        
        // تحديث النصوص
        document.querySelectorAll('.color-text').forEach((input, index) => {
            const values = [theme.primary, theme.secondary, theme.accent, theme.background, theme.textPrimary, theme.textSecondary];
            if (values[index]) {
                input.value = values[index];
            }
        });
        
        updatePreview();
    }
}

function resetTheme() {
    loadPresetTheme('default');
    document.getElementById('backgroundOpacity').value = 90;
    document.getElementById('blurAmount').value = 10;
    document.getElementById('borderRadius').value = 12;
    
    document.getElementById('opacityValue').textContent = '90%';
    document.getElementById('blurValue').textContent = '10px';
    document.getElementById('radiusValue').textContent = '12px';
    
    updatePreview();
}

function saveTheme() {
    const themeName = prompt('أدخل اسم الثيم:');
    if (themeName) {
        const themeData = {
            name: themeName,
            primary_color: document.getElementById('primaryColor').value,
            secondary_color: document.getElementById('secondaryColor').value,
            accent_color: document.getElementById('accentColor').value,
            background_color: document.getElementById('backgroundColor').value,
            text_primary: document.getElementById('textPrimary').value,
            text_secondary: document.getElementById('textSecondary').value,
            background_opacity: document.getElementById('backgroundOpacity').value / 100,
            blur_amount: document.getElementById('blurAmount').value,
            border_radius: document.getElementById('borderRadius').value
        };
        
        fetch('/api/themes/save', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(themeData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('تم حفظ الثيم بنجاح', 'success');
            } else {
                showNotification('فشل في حفظ الثيم: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showNotification('خطأ في الاتصال بالخادم', 'error');
        });
    }
}

function applyTheme() {
    const themeData = {
        primary_color: document.getElementById('primaryColor').value,
        secondary_color: document.getElementById('secondaryColor').value,
        accent_color: document.getElementById('accentColor').value,
        background_color: document.getElementById('backgroundColor').value,
        text_primary: document.getElementById('textPrimary').value,
        text_secondary: document.getElementById('textSecondary').value,
        background_opacity: document.getElementById('backgroundOpacity').value / 100,
        blur_amount: document.getElementById('blurAmount').value,
        border_radius: document.getElementById('borderRadius').value
    };
    
    fetch('/api/themes/apply', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(themeData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('تم تطبيق الثيم بنجاح', 'success');
            setTimeout(() => location.reload(), 1000);
        } else {
            showNotification('فشل في تطبيق الثيم: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showNotification('خطأ في الاتصال بالخادم', 'error');
    });
}

// تحديث المعاينة عند التحميل
updatePreview();
</script>
{% endblock %}
