{% extends "base.html" %}

{% block title %}إدارة المستخدمين - نظام إدارة الاشتراكات{% endblock %}
{% block page_title %}إدارة المستخدمين{% endblock %}
{% block page_description %}إدارة شاملة للمستخدمين والأدوار والأذونات{% endblock %}

{% block extra_css %}
<style>
.users-container {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 2rem;
    height: calc(100vh - 200px);
}

.users-sidebar {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 1.5rem;
    overflow-y: auto;
}

.users-main {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 1.5rem;
    overflow-y: auto;
}

.sidebar-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-section:last-child {
    border-bottom: none;
}

.section-title {
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-group {
    margin-bottom: 1rem;
}

.filter-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
    display: block;
}

.filter-input {
    width: 100%;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 0.5rem;
    color: white;
    font-size: 0.875rem;
}

.filter-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.role-filter {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.role-option {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.role-option:hover {
    background: rgba(255, 255, 255, 0.1);
}

.role-option input[type="checkbox"] {
    accent-color: #667eea;
}

.role-option label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.875rem;
    cursor: pointer;
}

.users-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.users-title {
    color: white;
    font-size: 1.5rem;
    font-weight: 600;
}

.users-actions {
    display: flex;
    gap: 1rem;
}

.btn-add-user {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-add-user:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
    color: white;
    text-decoration: none;
}

.users-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.user-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
}

.user-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.user-avatar {
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea, #764ba2);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    font-weight: bold;
    margin-bottom: 1rem;
}

.user-info {
    margin-bottom: 1rem;
}

.user-name {
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.user-email {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
}

.user-role {
    display: inline-block;
    background: rgba(102, 126, 234, 0.2);
    color: #667eea;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    border: 1px solid rgba(102, 126, 234, 0.3);
}

.user-status {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid white;
}

.user-status.active {
    background: #10b981;
}

.user-status.inactive {
    background: #ef4444;
}

.user-status.locked {
    background: #f59e0b;
}

.user-meta {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
    margin-bottom: 1rem;
    font-size: 0.75rem;
}

.meta-item {
    color: rgba(255, 255, 255, 0.6);
}

.meta-value {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.user-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.action-btn {
    width: 2rem;
    height: 2rem;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
}

.action-btn:hover {
    transform: translateY(-2px);
}

.btn-edit {
    background: rgba(59, 130, 246, 0.2);
    color: #3b82f6;
    border: 1px solid rgba(59, 130, 246, 0.3);
}

.btn-delete {
    background: rgba(239, 68, 68, 0.2);
    color: #ef4444;
    border: 1px solid rgba(239, 68, 68, 0.3);
}

.btn-lock {
    background: rgba(245, 158, 11, 0.2);
    color: #f59e0b;
    border: 1px solid rgba(245, 158, 11, 0.3);
}

.btn-unlock {
    background: rgba(16, 185, 129, 0.2);
    color: #10b981;
    border: 1px solid rgba(16, 185, 129, 0.3);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1rem;
    text-align: center;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: white;
    margin-bottom: 0.25rem;
}

.stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.75rem;
}

.empty-state {
    text-align: center;
    padding: 3rem;
    color: rgba(255, 255, 255, 0.6);
}

.empty-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

@media (max-width: 768px) {
    .users-container {
        grid-template-columns: 1fr;
        height: auto;
    }
    
    .users-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>
{% endblock %}

{% block content %}
<div class="users-container">
    <!-- الشريط الجانبي للفلاتر -->
    <div class="users-sidebar">
        <!-- إحصائيات سريعة -->
        <div class="sidebar-section">
            <h3 class="section-title">
                <i class="fas fa-chart-bar"></i>
                إحصائيات سريعة
            </h3>
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-value">{{ users_stats.total or 0 }}</div>
                    <div class="stat-label">إجمالي المستخدمين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{{ users_stats.active or 0 }}</div>
                    <div class="stat-label">نشط</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{{ users_stats.admins or 0 }}</div>
                    <div class="stat-label">مديرين</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value">{{ users_stats.locked or 0 }}</div>
                    <div class="stat-label">مقفل</div>
                </div>
            </div>
        </div>

        <!-- فلاتر البحث -->
        <div class="sidebar-section">
            <h3 class="section-title">
                <i class="fas fa-search"></i>
                البحث والفلترة
            </h3>
            <div class="filter-group">
                <label class="filter-label">البحث</label>
                <input type="text" class="filter-input" id="search-users" placeholder="ابحث بالاسم أو البريد...">
            </div>
            <div class="filter-group">
                <label class="filter-label">الحالة</label>
                <select class="filter-input" id="status-filter">
                    <option value="">جميع الحالات</option>
                    <option value="active">نشط</option>
                    <option value="inactive">غير نشط</option>
                    <option value="locked">مقفل</option>
                </select>
            </div>
        </div>

        <!-- فلتر الأدوار -->
        <div class="sidebar-section">
            <h3 class="section-title">
                <i class="fas fa-user-tag"></i>
                الأدوار
            </h3>
            <div class="role-filter">
                {% for role in roles %}
                <div class="role-option">
                    <input type="checkbox" id="role-{{ role.id }}" value="{{ role.id }}" class="role-checkbox">
                    <label for="role-{{ role.id }}">{{ role.display_name }}</label>
                </div>
                {% endfor %}
            </div>
        </div>

        <!-- إجراءات سريعة -->
        <div class="sidebar-section">
            <h3 class="section-title">
                <i class="fas fa-bolt"></i>
                إجراءات سريعة
            </h3>
            <div style="display: flex; flex-direction: column; gap: 0.5rem;">
                <button class="btn-add-user" onclick="exportUsers()">
                    <i class="fas fa-download"></i>
                    تصدير المستخدمين
                </button>
                <button class="btn-add-user" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);" onclick="manageRoles()">
                    <i class="fas fa-users-cog"></i>
                    إدارة الأدوار
                </button>
            </div>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="users-main">
        <!-- رأس الصفحة -->
        <div class="users-header">
            <div>
                <h2 class="users-title">إدارة المستخدمين</h2>
                <p style="color: rgba(255, 255, 255, 0.7); margin: 0;">إدارة شاملة للمستخدمين والأدوار</p>
            </div>
            <div class="users-actions">
                <a href="#" class="btn-add-user" onclick="addUser()">
                    <i class="fas fa-plus"></i>
                    إضافة مستخدم
                </a>
            </div>
        </div>

        <!-- شبكة المستخدمين -->
        <div class="users-grid" id="users-grid">
            {% for user in users %}
            <div class="user-card" data-user-id="{{ user.id }}">
                <div class="user-status {{ 'locked' if user.is_locked else 'active' if user.is_active else 'inactive' }}"></div>
                
                <div class="user-avatar">
                    {{ user.username[0].upper() }}
                </div>
                
                <div class="user-info">
                    <div class="user-name">{{ user.full_name }}</div>
                    <div class="user-email">{{ user.email }}</div>
                    <div class="user-role">{{ user.role.display_name if user.role else 'بدون دور' }}</div>
                </div>
                
                <div class="user-meta">
                    <div class="meta-item">
                        <div>آخر دخول:</div>
                        <div class="meta-value">{{ user.last_login.strftime('%Y-%m-%d') if user.last_login else 'لم يدخل بعد' }}</div>
                    </div>
                    <div class="meta-item">
                        <div>تاريخ الإنشاء:</div>
                        <div class="meta-value">{{ user.created_at.strftime('%Y-%m-%d') }}</div>
                    </div>
                    <div class="meta-item">
                        <div>القسم:</div>
                        <div class="meta-value">{{ user.department or 'غير محدد' }}</div>
                    </div>
                    <div class="meta-item">
                        <div>المنصب:</div>
                        <div class="meta-value">{{ user.position or 'غير محدد' }}</div>
                    </div>
                </div>
                
                <div class="user-actions">
                    <button class="action-btn btn-edit" onclick="editUser({{ user.id }})" title="تعديل">
                        <i class="fas fa-edit"></i>
                    </button>
                    {% if user.is_locked %}
                    <button class="action-btn btn-unlock" onclick="unlockUser({{ user.id }})" title="إلغاء القفل">
                        <i class="fas fa-unlock"></i>
                    </button>
                    {% else %}
                    <button class="action-btn btn-lock" onclick="lockUser({{ user.id }})" title="قفل الحساب">
                        <i class="fas fa-lock"></i>
                    </button>
                    {% endif %}
                    {% if not user.is_admin %}
                    <button class="action-btn btn-delete" onclick="deleteUser({{ user.id }})" title="حذف">
                        <i class="fas fa-trash"></i>
                    </button>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>

        {% if not users %}
        <div class="empty-state">
            <div class="empty-icon">
                <i class="fas fa-users"></i>
            </div>
            <h3>لا توجد مستخدمين</h3>
            <p>ابدأ بإضافة مستخدمين جدد للنظام</p>
            <button class="btn-add-user" onclick="addUser()">
                <i class="fas fa-plus"></i>
                إضافة أول مستخدم
            </button>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
class UserManager {
    constructor() {
        this.users = [];
        this.filteredUsers = [];
        this.init();
    }
    
    init() {
        this.loadUsers();
        this.initFilters();
        this.initSearch();
    }
    
    async loadUsers() {
        try {
            const response = await fetch('/api/users');
            this.users = await response.json();
            this.filteredUsers = [...this.users];
            this.renderUsers();
        } catch (error) {
            console.error('خطأ في تحميل المستخدمين:', error);
        }
    }
    
    initFilters() {
        // فلتر الحالة
        document.getElementById('status-filter').addEventListener('change', (e) => {
            this.filterUsers();
        });
        
        // فلتر الأدوار
        document.querySelectorAll('.role-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', () => {
                this.filterUsers();
            });
        });
    }
    
    initSearch() {
        const searchInput = document.getElementById('search-users');
        let searchTimeout;
        
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.filterUsers();
            }, 300);
        });
    }
    
    filterUsers() {
        const searchTerm = document.getElementById('search-users').value.toLowerCase();
        const statusFilter = document.getElementById('status-filter').value;
        const selectedRoles = Array.from(document.querySelectorAll('.role-checkbox:checked')).map(cb => cb.value);
        
        this.filteredUsers = this.users.filter(user => {
            // فلتر البحث
            const matchesSearch = !searchTerm || 
                user.full_name.toLowerCase().includes(searchTerm) ||
                user.email.toLowerCase().includes(searchTerm) ||
                user.username.toLowerCase().includes(searchTerm);
            
            // فلتر الحالة
            const matchesStatus = !statusFilter || user.status === statusFilter;
            
            // فلتر الأدوار
            const matchesRole = selectedRoles.length === 0 || selectedRoles.includes(user.role_id?.toString());
            
            return matchesSearch && matchesStatus && matchesRole;
        });
        
        this.renderUsers();
    }
    
    renderUsers() {
        const grid = document.getElementById('users-grid');
        
        if (this.filteredUsers.length === 0) {
            grid.innerHTML = `
                <div class="empty-state" style="grid-column: 1 / -1;">
                    <div class="empty-icon">
                        <i class="fas fa-search"></i>
                    </div>
                    <h3>لا توجد نتائج</h3>
                    <p>جرب تغيير معايير البحث</p>
                </div>
            `;
            return;
        }
        
        // هنا يمكن إضافة منطق الرندر الديناميكي
        // حالياً نعتمد على الرندر من الخادم
    }
}

// دوال الإجراءات
function addUser() {
    window.location.href = '/users/add';
}

function editUser(userId) {
    window.location.href = `/users/${userId}/edit`;
}

async function lockUser(userId) {
    if (confirm('هل تريد قفل هذا المستخدم؟')) {
        try {
            const response = await fetch(`/api/users/${userId}/lock`, { method: 'POST' });
            if (response.ok) {
                location.reload();
            }
        } catch (error) {
            alert('حدث خطأ في قفل المستخدم');
        }
    }
}

async function unlockUser(userId) {
    try {
        const response = await fetch(`/api/users/${userId}/unlock`, { method: 'POST' });
        if (response.ok) {
            location.reload();
        }
    } catch (error) {
        alert('حدث خطأ في إلغاء قفل المستخدم');
    }
}

async function deleteUser(userId) {
    if (confirm('هل تريد حذف هذا المستخدم نهائياً؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        try {
            const response = await fetch(`/api/users/${userId}`, { method: 'DELETE' });
            if (response.ok) {
                location.reload();
            }
        } catch (error) {
            alert('حدث خطأ في حذف المستخدم');
        }
    }
}

function exportUsers() {
    window.location.href = '/api/users/export';
}

function manageRoles() {
    window.location.href = '/roles';
}

// تهيئة مدير المستخدمين
document.addEventListener('DOMContentLoaded', () => {
    new UserManager();
});
</script>
{% endblock %}
