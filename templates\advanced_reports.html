{% extends "base.html" %}

{% block title %}التقارير المتقدمة - نظام إدارة الاشتراكات{% endblock %}
{% block page_title %}التقارير المتقدمة والذكية{% endblock %}
{% block page_description %}تقارير شاملة مع تحليلات ذكية وتنبؤات مستقبلية{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-4 space-x-reverse">
    <!-- منشئ التقارير المخصص -->
    <button class="btn-secondary" onclick="openReportBuilder()">
        <i class="fas fa-magic ml-2"></i>
        منشئ التقارير
    </button>
    
    <!-- جدولة التقارير -->
    <button class="btn-secondary" onclick="scheduleReport()">
        <i class="fas fa-clock ml-2"></i>
        جدولة التقارير
    </button>
    
    <!-- تصدير شامل -->
    <div class="relative">
        <button class="btn-primary" onclick="toggleExportMenu()">
            <i class="fas fa-download ml-2"></i>
            تصدير شامل
            <i class="fas fa-chevron-down mr-2"></i>
        </button>
        <div id="exportMenu" class="hidden absolute left-0 mt-2 w-48 glass-card rounded-lg shadow-lg z-50">
            <div class="p-2">
                <button class="w-full text-right p-2 text-white hover:bg-white hover:bg-opacity-10 rounded" onclick="exportReport('pdf')">
                    <i class="fas fa-file-pdf ml-2 text-red-400"></i>
                    تصدير PDF
                </button>
                <button class="w-full text-right p-2 text-white hover:bg-white hover:bg-opacity-10 rounded" onclick="exportReport('excel')">
                    <i class="fas fa-file-excel ml-2 text-green-400"></i>
                    تصدير Excel
                </button>
                <button class="w-full text-right p-2 text-white hover:bg-white hover:bg-opacity-10 rounded" onclick="exportReport('csv')">
                    <i class="fas fa-file-csv ml-2 text-blue-400"></i>
                    تصدير CSV
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block content %}
<!-- فلاتر التقارير المتقدمة -->
<div class="glass-card p-6 mb-8">
    <h3 class="text-xl font-bold text-white mb-4">فلاتر التقارير المتقدمة</h3>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <!-- فلتر التاريخ -->
        <div>
            <label class="block text-white text-opacity-80 text-sm mb-2">نطاق التاريخ</label>
            <select class="input-field w-full" id="dateRange">
                <option value="7">آخر 7 أيام</option>
                <option value="30" selected>آخر 30 يوم</option>
                <option value="90">آخر 3 أشهر</option>
                <option value="365">آخر سنة</option>
                <option value="custom">نطاق مخصص</option>
            </select>
        </div>
        
        <!-- فلتر المزود -->
        <div>
            <label class="block text-white text-opacity-80 text-sm mb-2">مزود الخدمة</label>
            <select class="input-field w-full" id="providerFilter">
                <option value="">جميع المزودين</option>
                <option value="aws">Amazon AWS</option>
                <option value="azure">Microsoft Azure</option>
                <option value="gcp">Google Cloud</option>
                <option value="digitalocean">DigitalOcean</option>
            </select>
        </div>
        
        <!-- فلتر نوع الاشتراك -->
        <div>
            <label class="block text-white text-opacity-80 text-sm mb-2">نوع الاشتراك</label>
            <select class="input-field w-full" id="subscriptionType">
                <option value="">جميع الأنواع</option>
                <option value="monthly">شهري</option>
                <option value="semi_annual">نصف سنوي</option>
                <option value="annual">سنوي</option>
            </select>
        </div>
        
        <!-- فلتر الحالة -->
        <div>
            <label class="block text-white text-opacity-80 text-sm mb-2">حالة الاشتراك</label>
            <select class="input-field w-full" id="statusFilter">
                <option value="">جميع الحالات</option>
                <option value="active">نشط</option>
                <option value="suspended">معلق</option>
                <option value="expired">منتهي</option>
            </select>
        </div>
    </div>
    
    <div class="flex justify-between items-center mt-6">
        <button class="btn-secondary" onclick="resetFilters()">
            <i class="fas fa-undo ml-2"></i>
            إعادة تعيين
        </button>
        <button class="btn-primary" onclick="applyFilters()">
            <i class="fas fa-filter ml-2"></i>
            تطبيق الفلاتر
        </button>
    </div>
</div>

<!-- تقارير سريعة -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
    <!-- تقرير الإيرادات -->
    <div class="glass-card p-6 hover-lift cursor-pointer" onclick="generateQuickReport('revenue')">
        <div class="flex items-center justify-between mb-4">
            <h4 class="text-lg font-bold text-white">تقرير الإيرادات</h4>
            <div class="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl flex items-center justify-center">
                <i class="fas fa-dollar-sign text-white"></i>
            </div>
        </div>
        <p class="text-white text-opacity-70 text-sm mb-4">تحليل شامل للإيرادات والنمو المالي</p>
        <div class="flex items-center justify-between">
            <span class="text-green-400 text-sm">جاهز للتصدير</span>
            <i class="fas fa-arrow-left text-white text-opacity-50"></i>
        </div>
    </div>
    
    <!-- تقرير الاشتراكات -->
    <div class="glass-card p-6 hover-lift cursor-pointer" onclick="generateQuickReport('subscriptions')">
        <div class="flex items-center justify-between mb-4">
            <h4 class="text-lg font-bold text-white">تقرير الاشتراكات</h4>
            <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-xl flex items-center justify-center">
                <i class="fas fa-server text-white"></i>
            </div>
        </div>
        <p class="text-white text-opacity-70 text-sm mb-4">إحصائيات مفصلة عن جميع الاشتراكات</p>
        <div class="flex items-center justify-between">
            <span class="text-blue-400 text-sm">جاهز للتصدير</span>
            <i class="fas fa-arrow-left text-white text-opacity-50"></i>
        </div>
    </div>
    
    <!-- تقرير العملاء -->
    <div class="glass-card p-6 hover-lift cursor-pointer" onclick="generateQuickReport('customers')">
        <div class="flex items-center justify-between mb-4">
            <h4 class="text-lg font-bold text-white">تقرير العملاء</h4>
            <div class="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl flex items-center justify-center">
                <i class="fas fa-users text-white"></i>
            </div>
        </div>
        <p class="text-white text-opacity-70 text-sm mb-4">تحليل سلوك العملاء ومعدل الاحتفاظ</p>
        <div class="flex items-center justify-between">
            <span class="text-purple-400 text-sm">جاهز للتصدير</span>
            <i class="fas fa-arrow-left text-white text-opacity-50"></i>
        </div>
    </div>
</div>

<!-- التقارير المجدولة -->
<div class="glass-card p-6 mb-8">
    <div class="flex items-center justify-between mb-6">
        <h3 class="text-xl font-bold text-white">التقارير المجدولة</h3>
        <button class="btn-primary" onclick="addScheduledReport()">
            <i class="fas fa-plus ml-2"></i>
            إضافة تقرير مجدول
        </button>
    </div>
    
    <div class="overflow-x-auto">
        <table class="w-full">
            <thead>
                <tr class="border-b border-white border-opacity-20">
                    <th class="text-right py-3 px-4 text-white text-opacity-80">اسم التقرير</th>
                    <th class="text-right py-3 px-4 text-white text-opacity-80">التكرار</th>
                    <th class="text-right py-3 px-4 text-white text-opacity-80">آخر تشغيل</th>
                    <th class="text-right py-3 px-4 text-white text-opacity-80">التشغيل التالي</th>
                    <th class="text-right py-3 px-4 text-white text-opacity-80">الحالة</th>
                    <th class="text-right py-3 px-4 text-white text-opacity-80">الإجراءات</th>
                </tr>
            </thead>
            <tbody id="scheduledReportsTable">
                <tr class="border-b border-white border-opacity-10 hover:bg-white hover:bg-opacity-5">
                    <td class="py-3 px-4 text-white">تقرير الإيرادات الشهري</td>
                    <td class="py-3 px-4 text-white text-opacity-70">شهرياً</td>
                    <td class="py-3 px-4 text-white text-opacity-70">2024-01-01</td>
                    <td class="py-3 px-4 text-white text-opacity-70">2024-02-01</td>
                    <td class="py-3 px-4">
                        <span class="badge-success">نشط</span>
                    </td>
                    <td class="py-3 px-4">
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <button class="w-8 h-8 bg-blue-500 bg-opacity-20 rounded-lg flex items-center justify-center text-blue-400 hover:bg-opacity-30" onclick="editScheduledReport(1)">
                                <i class="fas fa-edit text-xs"></i>
                            </button>
                            <button class="w-8 h-8 bg-red-500 bg-opacity-20 rounded-lg flex items-center justify-center text-red-400 hover:bg-opacity-30" onclick="deleteScheduledReport(1)">
                                <i class="fas fa-trash text-xs"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- منشئ التقارير المخصص -->
<div id="reportBuilderModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
    <div class="glass-card p-8 max-w-4xl w-full mx-4 max-h-screen overflow-y-auto">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-2xl font-bold text-white">منشئ التقارير المخصص</h3>
            <button class="w-8 h-8 bg-red-500 bg-opacity-20 rounded-lg flex items-center justify-center text-red-400 hover:bg-opacity-30" onclick="closeReportBuilder()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- إعدادات التقرير -->
            <div>
                <h4 class="text-lg font-bold text-white mb-4">إعدادات التقرير</h4>
                <div class="space-y-4">
                    <div>
                        <label class="block text-white text-opacity-80 text-sm mb-2">اسم التقرير</label>
                        <input type="text" class="input-field w-full" placeholder="أدخل اسم التقرير">
                    </div>
                    <div>
                        <label class="block text-white text-opacity-80 text-sm mb-2">نوع التقرير</label>
                        <select class="input-field w-full">
                            <option>تقرير مالي</option>
                            <option>تقرير الاشتراكات</option>
                            <option>تقرير العملاء</option>
                            <option>تقرير الأداء</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-white text-opacity-80 text-sm mb-2">تنسيق التصدير</label>
                        <div class="flex space-x-4 space-x-reverse">
                            <label class="flex items-center">
                                <input type="checkbox" class="ml-2" checked>
                                <span class="text-white text-opacity-80">PDF</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="ml-2">
                                <span class="text-white text-opacity-80">Excel</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="ml-2">
                                <span class="text-white text-opacity-80">CSV</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- معاينة التقرير -->
            <div>
                <h4 class="text-lg font-bold text-white mb-4">معاينة التقرير</h4>
                <div class="bg-white bg-opacity-10 rounded-lg p-4 h-64 overflow-y-auto">
                    <p class="text-white text-opacity-70 text-sm">ستظهر معاينة التقرير هنا...</p>
                </div>
            </div>
        </div>
        
        <div class="flex justify-end space-x-4 space-x-reverse mt-6">
            <button class="btn-secondary" onclick="closeReportBuilder()">إلغاء</button>
            <button class="btn-primary" onclick="generateCustomReport()">إنشاء التقرير</button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// وظائف التقارير المتقدمة
function openReportBuilder() {
    document.getElementById('reportBuilderModal').classList.remove('hidden');
}

function closeReportBuilder() {
    document.getElementById('reportBuilderModal').classList.add('hidden');
}

function toggleExportMenu() {
    const menu = document.getElementById('exportMenu');
    menu.classList.toggle('hidden');
}

function generateQuickReport(type) {
    // إظهار مؤشر التحميل
    showLoading();
    
    // محاكاة إنشاء التقرير
    setTimeout(() => {
        hideLoading();
        showNotification(`تم إنشاء تقرير ${type} بنجاح`, 'success');
    }, 2000);
}

function exportReport(format) {
    showNotification(`جاري تصدير التقرير بصيغة ${format.toUpperCase()}...`, 'info');
    
    // محاكاة التصدير
    setTimeout(() => {
        showNotification(`تم تصدير التقرير بصيغة ${format.toUpperCase()} بنجاح`, 'success');
    }, 1500);
}

function applyFilters() {
    showLoading();
    
    // محاكاة تطبيق الفلاتر
    setTimeout(() => {
        hideLoading();
        showNotification('تم تطبيق الفلاتر بنجاح', 'success');
    }, 1000);
}

function resetFilters() {
    document.getElementById('dateRange').value = '30';
    document.getElementById('providerFilter').value = '';
    document.getElementById('subscriptionType').value = '';
    document.getElementById('statusFilter').value = '';
    
    showNotification('تم إعادة تعيين الفلاتر', 'info');
}

function showLoading() {
    // إضافة مؤشر التحميل
    const loader = document.createElement('div');
    loader.id = 'pageLoader';
    loader.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
    loader.innerHTML = '<div class="spinner"></div>';
    document.body.appendChild(loader);
}

function hideLoading() {
    const loader = document.getElementById('pageLoader');
    if (loader) {
        loader.remove();
    }
}

function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} fixed top-4 right-4 z-50 max-w-md`;
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-info-circle ml-2"></i>
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="mr-auto">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 5000);
}

// إغلاق القائمة عند النقر خارجها
document.addEventListener('click', (e) => {
    if (!e.target.closest('#exportMenu') && !e.target.closest('button')) {
        document.getElementById('exportMenu').classList.add('hidden');
    }
});
</script>
{% endblock %}
