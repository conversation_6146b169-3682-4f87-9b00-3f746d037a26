# نظام إدارة الاشتراكات المتطور
# تطوير: المهندس محمد ياسر الجبوري

# المتطلبات الأساسية
Flask==2.3.3
Flask-SQLAlchemy==3.0.5
Werkzeug==2.3.7
Jinja2==3.1.2
MarkupSafe==2.1.3
itsdangerous==2.1.2
click==8.1.7
blinker==1.6.3

# المتطلبات المتقدمة للنظام المحدث
Flask-Migrate==4.0.5
Flask-Mail==0.9.1
reportlab==4.0.4
psutil==5.9.5
cryptography==41.0.4
python-dateutil==2.8.2
python-dotenv==1.0.0
gunicorn==21.2.0
redis==5.0.1
flask-limiter==3.5.0
flask-compress==1.14
flask-caching==2.1.0

# مكتبات الذكاء الاصطناعي والتحليلات
numpy==1.24.3
pandas==2.0.3
scikit-learn==1.3.0
matplotlib==3.7.2
seaborn==0.12.2

# مكتبات الأمان المتقدمة
bcrypt==4.0.1
PyJWT==2.8.0
flask-cors==4.0.0
