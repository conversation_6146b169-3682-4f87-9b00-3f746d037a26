{% extends "base.html" %}

{% block title %}الرؤى الذكية - نظام إدارة الاشتراكات{% endblock %}
{% block page_title %}الرؤى الذكية والتحليلات المدعومة بالذكاء الاصطناعي{% endblock %}
{% block page_description %}تحليلات متقدمة وتنبؤات ذكية لاتخاذ قرارات مدروسة{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-4 space-x-reverse">
    <!-- تحديث التحليلات -->
    <button class="btn-secondary" onclick="refreshAnalytics()">
        <i class="fas fa-sync-alt ml-2"></i>
        تحديث التحليلات
    </button>
    
    <!-- إعدادات الذكاء الاصطناعي -->
    <button class="btn-secondary" onclick="openAISettings()">
        <i class="fas fa-cog ml-2"></i>
        إعدادات الذكاء الاصطناعي
    </button>
    
    <!-- تصدير التقرير الذكي -->
    <button class="btn-primary" onclick="exportAIReport()">
        <i class="fas fa-brain ml-2"></i>
        تصدير التقرير الذكي
    </button>
</div>
{% endblock %}

{% block content %}
<!-- مؤشرات الذكاء الاصطناعي -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- دقة التنبؤ -->
    <div class="glass-card p-6 hover-lift">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-white text-opacity-80">دقة التنبؤ</p>
                <p class="text-3xl font-bold text-white mt-2">{{ "%.1f"|format(revenue_prediction.model_accuracy) }}%</p>
                <div class="flex items-center mt-2">
                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse ml-2"></div>
                    <span class="text-green-400 text-sm">دقة عالية</span>
                </div>
            </div>
            <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center">
                <i class="fas fa-brain text-2xl text-white"></i>
            </div>
        </div>
        <div class="progress-bar mt-4">
            <div class="progress-fill" style="width: {{ revenue_prediction.model_accuracy }}%"></div>
        </div>
    </div>

    <!-- معدل الإلغاء -->
    <div class="glass-card p-6 hover-lift">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-white text-opacity-80">معدل الإلغاء</p>
                <p class="text-3xl font-bold text-white mt-2">{{ churn_analysis.churn_rate }}%</p>
                <div class="flex items-center mt-2">
                    <div class="w-2 h-2 bg-yellow-400 rounded-full animate-pulse ml-2"></div>
                    <span class="text-yellow-400 text-sm">يحتاج متابعة</span>
                </div>
            </div>
            <div class="w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-2xl flex items-center justify-center">
                <i class="fas fa-user-times text-2xl text-white"></i>
            </div>
        </div>
        <div class="progress-bar mt-4">
            <div class="progress-fill" style="width: {{ churn_analysis.churn_rate }}%"></div>
        </div>
    </div>

    <!-- العملاء المعرضون للخطر -->
    <div class="glass-card p-6 hover-lift">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-white text-opacity-80">عملاء معرضون للخطر</p>
                <p class="text-3xl font-bold text-white mt-2">{{ churn_analysis.at_risk_customers|length }}</p>
                <div class="flex items-center mt-2">
                    <div class="w-2 h-2 bg-red-400 rounded-full animate-pulse ml-2"></div>
                    <span class="text-red-400 text-sm">يحتاج تدخل فوري</span>
                </div>
            </div>
            <div class="w-16 h-16 bg-gradient-to-r from-red-500 to-pink-500 rounded-2xl flex items-center justify-center">
                <i class="fas fa-exclamation-triangle text-2xl text-white"></i>
            </div>
        </div>
        <div class="progress-bar mt-4">
            <div class="progress-fill" style="width: 35%"></div>
        </div>
    </div>

    <!-- الشذوذ المكتشف -->
    <div class="glass-card p-6 hover-lift">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-white text-opacity-80">شذوذ مكتشف</p>
                <p class="text-3xl font-bold text-white mt-2">{{ anomaly_detection.total_anomalies }}</p>
                <div class="flex items-center mt-2">
                    <div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse ml-2"></div>
                    <span class="text-blue-400 text-sm">تحت المراقبة</span>
                </div>
            </div>
            <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center">
                <i class="fas fa-search text-2xl text-white"></i>
            </div>
        </div>
        <div class="progress-bar mt-4">
            <div class="progress-fill" style="width: 20%"></div>
        </div>
    </div>
</div>

<!-- التنبؤ بالإيرادات -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <div class="glass-card p-6">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-xl font-bold text-white">التنبؤ بالإيرادات (الذكاء الاصطناعي)</h3>
            <div class="flex items-center space-x-2 space-x-reverse">
                <span class="badge-success">{{ revenue_prediction.trend }}</span>
                <button class="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center text-white hover:bg-opacity-30 transition-all duration-300" onclick="refreshRevenuePrediction()">
                    <i class="fas fa-sync-alt text-sm"></i>
                </button>
            </div>
        </div>
        <div class="h-64">
            <canvas id="revenuePredictionChart"></canvas>
        </div>
        <div class="mt-4 p-4 bg-white bg-opacity-10 rounded-lg">
            <p class="text-white text-sm">
                <i class="fas fa-lightbulb text-yellow-400 ml-2"></i>
                <strong>رؤية ذكية:</strong> بناءً على التحليل، من المتوقع {{ revenue_prediction.trend }} في الإيرادات بنسبة {{ "%.1f"|format(revenue_prediction.model_accuracy) }}% دقة.
            </p>
        </div>
    </div>

    <!-- تحليل الإلغاء -->
    <div class="glass-card p-6">
        <div class="flex items-center justify-between mb-6">
            <h3 class="text-xl font-bold text-white">تحليل معدل الإلغاء</h3>
            <span class="badge-warning">{{ churn_analysis.churn_rate }}%</span>
        </div>
        <div class="h-64">
            <canvas id="churnAnalysisChart"></canvas>
        </div>
        <div class="mt-4">
            <h4 class="text-white font-semibold mb-2">توصيات الذكاء الاصطناعي:</h4>
            <ul class="space-y-2">
                {% for recommendation in churn_analysis.recommendations %}
                <li class="flex items-center text-white text-sm">
                    <i class="fas fa-check-circle text-green-400 ml-2"></i>
                    {{ recommendation }}
                </li>
                {% endfor %}
            </ul>
        </div>
    </div>
</div>

<!-- تحسين الأسعار وتقسيم العملاء -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- تحسين الأسعار -->
    <div class="glass-card p-6">
        <h3 class="text-xl font-bold text-white mb-6">تحسين الأسعار الذكي</h3>
        <div class="space-y-4">
            {% for rec in pricing_optimization.recommendations %}
            <div class="p-4 bg-white bg-opacity-10 rounded-lg">
                <div class="flex items-center justify-between mb-2">
                    <h4 class="text-white font-semibold">{{ rec.subscription_type }}</h4>
                    <span class="badge-primary">{{ rec.demand_score }}% طلب</span>
                </div>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div>
                        <p class="text-white text-opacity-70">السعر الحالي</p>
                        <p class="text-white font-bold">${{ rec.current_avg_price }}</p>
                    </div>
                    <div>
                        <p class="text-white text-opacity-70">السعر المقترح</p>
                        <p class="text-green-400 font-bold">${{ rec.recommended_price }}</p>
                    </div>
                </div>
                <p class="text-white text-opacity-80 text-xs mt-2">{{ rec.reason }}</p>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- تقسيم العملاء -->
    <div class="glass-card p-6">
        <h3 class="text-xl font-bold text-white mb-6">تقسيم العملاء الذكي</h3>
        <div class="h-48 mb-4">
            <canvas id="customerSegmentationChart"></canvas>
        </div>
        <div class="space-y-3">
            {% for segment_name, segment_data in customer_segmentation.segments.items() %}
            <div class="flex items-center justify-between p-3 bg-white bg-opacity-10 rounded-lg">
                <div>
                    <h4 class="text-white font-semibold">{{ segment_name.replace('_', ' ').title() }}</h4>
                    <p class="text-white text-opacity-70 text-sm">{{ segment_data.size }} عميل</p>
                </div>
                <div class="text-right">
                    {% if segment_data.avg_price %}
                    <p class="text-white text-sm">${{ segment_data.avg_price }}</p>
                    <p class="text-white text-opacity-70 text-xs">متوسط السعر</p>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<!-- كشف الشذوذ -->
{% if anomaly_detection.anomalies %}
<div class="glass-card p-6 mb-8">
    <h3 class="text-xl font-bold text-white mb-6">كشف الشذوذ بالذكاء الاصطناعي</h3>
    <div class="overflow-x-auto">
        <table class="w-full">
            <thead>
                <tr class="border-b border-white border-opacity-20">
                    <th class="text-right py-3 px-4 text-white text-opacity-80">نوع الشذوذ</th>
                    <th class="text-right py-3 px-4 text-white text-opacity-80">معرف الاشتراك</th>
                    <th class="text-right py-3 px-4 text-white text-opacity-80">القيمة</th>
                    <th class="text-right py-3 px-4 text-white text-opacity-80">المستوى</th>
                    <th class="text-right py-3 px-4 text-white text-opacity-80">الإجراء</th>
                </tr>
            </thead>
            <tbody>
                {% for anomaly in anomaly_detection.anomalies[:10] %}
                <tr class="border-b border-white border-opacity-10 hover:bg-white hover:bg-opacity-5">
                    <td class="py-3 px-4 text-white">{{ anomaly.type.replace('_', ' ').title() }}</td>
                    <td class="py-3 px-4 text-white text-opacity-70">{{ anomaly.subscription_id }}</td>
                    <td class="py-3 px-4 text-white text-opacity-70">{{ anomaly.value }}</td>
                    <td class="py-3 px-4">
                        {% if anomaly.severity == 'high' %}
                        <span class="badge-danger">عالي</span>
                        {% elif anomaly.severity == 'medium' %}
                        <span class="badge-warning">متوسط</span>
                        {% else %}
                        <span class="badge-primary">منخفض</span>
                        {% endif %}
                    </td>
                    <td class="py-3 px-4">
                        <button class="btn-secondary text-xs" onclick="investigateAnomaly({{ anomaly.subscription_id }})">
                            تحقق
                        </button>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endif %}

<!-- رؤى إضافية -->
<div class="glass-card p-6">
    <h3 class="text-xl font-bold text-white mb-6">رؤى إضافية من الذكاء الاصطناعي</h3>
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <div class="p-4 bg-white bg-opacity-10 rounded-lg">
            <h4 class="text-white font-semibold mb-2">
                <i class="fas fa-chart-line text-green-400 ml-2"></i>
                اتجاه النمو
            </h4>
            <p class="text-white text-opacity-80 text-sm">
                النظام يتوقع نمواً {{ revenue_prediction.trend }} في الإيرادات خلال الأشهر القادمة.
            </p>
        </div>
        
        <div class="p-4 bg-white bg-opacity-10 rounded-lg">
            <h4 class="text-white font-semibold mb-2">
                <i class="fas fa-users text-blue-400 ml-2"></i>
                سلوك العملاء
            </h4>
            <p class="text-white text-opacity-80 text-sm">
                تم تحديد {{ customer_segmentation.segments|length }} شرائح مختلفة من العملاء لتحسين الاستهداف.
            </p>
        </div>
        
        <div class="p-4 bg-white bg-opacity-10 rounded-lg">
            <h4 class="text-white font-semibold mb-2">
                <i class="fas fa-shield-alt text-purple-400 ml-2"></i>
                مراقبة الجودة
            </h4>
            <p class="text-white text-opacity-80 text-sm">
                تم اكتشاف {{ anomaly_detection.total_anomalies }} حالة شذوذ تحتاج للمراجعة.
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// رسم بياني للتنبؤ بالإيرادات
const revenuePredictionData = {{ revenue_prediction.predictions | tojson }};

// رسم بياني لتحليل الإلغاء
const churnData = {{ churn_analysis.churn_by_provider | tojson }};

// رسم بياني لتقسيم العملاء
const segmentationData = {{ customer_segmentation.segments | tojson }};

// تهيئة الرسوم البيانية
document.addEventListener('DOMContentLoaded', function() {
    initRevenuePredictionChart();
    initChurnAnalysisChart();
    initCustomerSegmentationChart();
});

function initRevenuePredictionChart() {
    const ctx = document.getElementById('revenuePredictionChart').getContext('2d');
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: revenuePredictionData.map(d => d.month),
            datasets: [{
                label: 'الإيرادات المتوقعة',
                data: revenuePredictionData.map(d => d.predicted_revenue),
                borderColor: 'rgba(102, 126, 234, 1)',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: { color: 'white' }
                }
            },
            scales: {
                x: { ticks: { color: 'white' } },
                y: { ticks: { color: 'white' } }
            }
        }
    });
}

function initChurnAnalysisChart() {
    const ctx = document.getElementById('churnAnalysisChart').getContext('2d');
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: Object.keys(churnData),
            datasets: [{
                data: Object.values(churnData),
                backgroundColor: [
                    'rgba(239, 68, 68, 0.8)',
                    'rgba(245, 158, 11, 0.8)',
                    'rgba(59, 130, 246, 0.8)',
                    'rgba(16, 185, 129, 0.8)'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: { color: 'white' }
                }
            }
        }
    });
}

function initCustomerSegmentationChart() {
    const ctx = document.getElementById('customerSegmentationChart').getContext('2d');
    const labels = Object.keys(segmentationData);
    const data = Object.values(segmentationData).map(s => s.size);
    
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'عدد العملاء',
                data: data,
                backgroundColor: 'rgba(102, 126, 234, 0.8)',
                borderColor: 'rgba(102, 126, 234, 1)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    labels: { color: 'white' }
                }
            },
            scales: {
                x: { ticks: { color: 'white' } },
                y: { ticks: { color: 'white' } }
            }
        }
    });
}

// وظائف التفاعل
function refreshAnalytics() {
    showNotification('جاري تحديث التحليلات...', 'info');
    setTimeout(() => {
        location.reload();
    }, 2000);
}

function refreshRevenuePrediction() {
    showNotification('جاري تحديث التنبؤ بالإيرادات...', 'info');
    // يمكن إضافة استدعاء AJAX هنا
}

function investigateAnomaly(subscriptionId) {
    showNotification(`جاري التحقق من الاشتراك ${subscriptionId}...`, 'info');
    // يمكن إضافة منطق التحقق هنا
}

function exportAIReport() {
    showNotification('جاري تصدير التقرير الذكي...', 'info');
    // يمكن إضافة منطق التصدير هنا
}

function openAISettings() {
    showNotification('فتح إعدادات الذكاء الاصطناعي...', 'info');
    // يمكن إضافة modal للإعدادات
}

function showNotification(message, type) {
    // استخدام نظام الإشعارات المتقدم
    if (window.notificationSystem) {
        window.notificationSystem.createNotification({
            title: 'الذكاء الاصطناعي',
            message: message,
            type: type
        });
    }
}
</script>
{% endblock %}
