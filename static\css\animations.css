/* 
 * ملف الأنيميشن المتقدم
 * تطوير: المهندس محمد ياسر الجبوري
 * نظام إدارة الاشتراكات المتطور
 */

/* Page Transitions */
.page-transition {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.page-transition.loaded {
    opacity: 1;
    transform: translateY(0);
}

/* Loading Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-100%);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes rotateIn {
    from {
        opacity: 0;
        transform: rotate(-200deg);
    }
    to {
        opacity: 1;
        transform: rotate(0);
    }
}

@keyframes flipInX {
    from {
        opacity: 0;
        transform: perspective(400px) rotateX(90deg);
    }
    40% {
        transform: perspective(400px) rotateX(-20deg);
    }
    60% {
        transform: perspective(400px) rotateX(10deg);
    }
    80% {
        transform: perspective(400px) rotateX(-5deg);
    }
    to {
        opacity: 1;
        transform: perspective(400px) rotateX(0deg);
    }
}

/* Animation Classes */
.animate-fadeInUp {
    animation: fadeInUp 0.8s ease-out forwards;
}

.animate-fadeInLeft {
    animation: fadeInLeft 0.8s ease-out forwards;
}

.animate-fadeInRight {
    animation: fadeInRight 0.8s ease-out forwards;
}

.animate-scaleIn {
    animation: scaleIn 0.6s ease-out forwards;
}

.animate-slideInDown {
    animation: slideInDown 0.8s ease-out forwards;
}

.animate-bounceIn {
    animation: bounceIn 1s ease-out forwards;
}

.animate-rotateIn {
    animation: rotateIn 0.8s ease-out forwards;
}

.animate-flipInX {
    animation: flipInX 0.8s ease-out forwards;
}

/* Delay Classes */
.delay-100 { animation-delay: 0.1s; }
.delay-200 { animation-delay: 0.2s; }
.delay-300 { animation-delay: 0.3s; }
.delay-400 { animation-delay: 0.4s; }
.delay-500 { animation-delay: 0.5s; }
.delay-600 { animation-delay: 0.6s; }
.delay-700 { animation-delay: 0.7s; }
.delay-800 { animation-delay: 0.8s; }

/* Hover Effects */
.hover-lift {
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.hover-lift:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.hover-glow {
    transition: all 0.3s ease;
}

.hover-glow:hover {
    box-shadow: 0 0 30px rgba(102, 126, 234, 0.5);
}

.hover-rotate {
    transition: transform 0.3s ease;
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

.hover-scale {
    transition: transform 0.3s ease;
}

.hover-scale:hover {
    transform: scale(1.05);
}

/* Loading Spinner */
.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #fff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Pulse Effect */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.7;
        transform: scale(1.05);
    }
}

/* Floating Effect */
.float {
    animation: float 3s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Shake Effect */
.shake {
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

/* Gradient Animation */
.gradient-animation {
    background: linear-gradient(-45deg, #667eea, #764ba2, #f093fb, #f5576c);
    background-size: 400% 400%;
    animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Text Animations */
.typewriter {
    overflow: hidden;
    border-right: 2px solid;
    white-space: nowrap;
    animation: typing 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from { width: 0; }
    to { width: 100%; }
}

@keyframes blink-caret {
    from, to { border-color: transparent; }
    50% { border-color: currentColor; }
}

/* Morphing Shapes */
.morph {
    transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.morph:hover {
    border-radius: 50%;
    transform: rotate(180deg);
}

/* Parallax Effect */
.parallax {
    transform: translateZ(0);
    transition: transform 0.1s ease-out;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}
