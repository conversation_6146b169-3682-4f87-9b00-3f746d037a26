{% extends "base.html" %}

{% block title %}كشف حساب العميل - {{ subscription.customer_name }}{% endblock %}
{% block page_title %}كشف حساب العميل{% endblock %}
{% block page_description %}{{ subscription.customer_name }} - {{ subscription.network_name }}{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/subscriptions-advanced.css') }}">
<style>
.statement-header {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
}

.statement-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    transition: all 0.3s ease;
}

.statement-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.payment-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid;
    transition: all 0.3s ease;
}

.payment-item.completed {
    border-left-color: #10b981;
}

.payment-item.pending {
    border-left-color: #f59e0b;
}

.payment-item.failed {
    border-left-color: #ef4444;
}

.payment-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.stat-box {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.stat-box:hover {
    transform: translateY(-3px);
    background: rgba(255, 255, 255, 0.15);
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: white;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
}

.timeline {
    position: relative;
    padding-left: 2rem;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 0.75rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #667eea, #764ba2);
}

.timeline-item {
    position: relative;
    margin-bottom: 2rem;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: -1.75rem;
    top: 0.5rem;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: #667eea;
    border: 3px solid white;
}

.timeline-content {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1rem;
    margin-left: 1rem;
}

@media (max-width: 768px) {
    .statement-header {
        padding: 1.5rem;
        margin: 0 1rem 1.5rem;
    }
    
    .statement-card {
        margin: 0 1rem 1.5rem;
        padding: 1rem;
    }
}
</style>
{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3 space-x-reverse">
    <button onclick="printStatement()" class="btn-secondary">
        <i class="fas fa-print ml-2"></i>
        طباعة
    </button>
    <button onclick="exportStatement()" class="btn-secondary">
        <i class="fas fa-download ml-2"></i>
        تصدير PDF
    </button>
    <a href="{{ url_for('subscriptions') }}" class="btn-secondary">
        <i class="fas fa-arrow-right ml-2"></i>
        العودة للاشتراكات
    </a>
</div>
{% endblock %}

{% block content %}
<!-- معلومات العميل والاشتراك -->
<div class="statement-header">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
            <h2 class="text-2xl font-bold mb-4">معلومات العميل</h2>
            <div class="space-y-2">
                <p><strong>الاسم:</strong> {{ subscription.customer_name }}</p>
                <p><strong>البريد الإلكتروني:</strong> {{ subscription.customer_email or 'غير محدد' }}</p>
                <p><strong>الهاتف:</strong> {{ subscription.customer_phone or 'غير محدد' }}</p>
            </div>
        </div>
        <div>
            <h2 class="text-2xl font-bold mb-4">معلومات الاشتراك</h2>
            <div class="space-y-2">
                <p><strong>اسم الشبكة:</strong> {{ subscription.network_name }}</p>
                <p><strong>نوع الاشتراك:</strong> 
                    {% if subscription.subscription_type == 'monthly' %}شهري
                    {% elif subscription.subscription_type == 'semi_annual' %}نصف سنوي
                    {% elif subscription.subscription_type == 'annual' %}سنوي
                    {% else %}{{ subscription.subscription_type }}{% endif %}
                </p>
                <p><strong>السعر:</strong> ${{ "%.2f"|format(subscription.price) }}</p>
                <p><strong>تاريخ الانتهاء:</strong> {{ subscription.end_date }}</p>
            </div>
        </div>
    </div>
</div>

<!-- الإحصائيات المالية -->
<div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
    <div class="stat-box">
        <div class="stat-value">${{ "%.2f"|format(subscription.total_paid) }}</div>
        <div class="stat-label">إجمالي المدفوع</div>
    </div>
    <div class="stat-box">
        <div class="stat-value">${{ "%.2f"|format(subscription.remaining_amount) }}</div>
        <div class="stat-label">المبلغ المتبقي</div>
    </div>
    <div class="stat-box">
        <div class="stat-value">{{ subscription.payments|length }}</div>
        <div class="stat-label">عدد الدفعات</div>
    </div>
    <div class="stat-box">
        <div class="stat-value">{{ subscription.days_remaining }}</div>
        <div class="stat-label">الأيام المتبقية</div>
    </div>
</div>

<!-- حالة الدفع الحالية -->
<div class="statement-card">
    <h3 class="text-xl font-bold text-white mb-4">حالة الدفع الحالية</h3>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="text-center">
            <div class="text-lg font-semibold text-white">
                {% if subscription.last_payment %}
                    {{ subscription.last_payment.payment_date }}
                {% else %}
                    لا توجد دفعات
                {% endif %}
            </div>
            <div class="text-white text-opacity-70">آخر دفعة</div>
        </div>
        <div class="text-center">
            <div class="text-lg font-semibold text-white">
                {% if subscription.remaining_amount > 0 %}
                    <span class="text-red-400">غير مكتمل</span>
                {% else %}
                    <span class="text-green-400">مكتمل</span>
                {% endif %}
            </div>
            <div class="text-white text-opacity-70">حالة الدفع</div>
        </div>
        <div class="text-center">
            <div class="text-lg font-semibold text-white">
                {% set current_month = now.strftime('%Y-%m') %}
                {% set paid_this_month = subscription.payments|selectattr('payment_date')|map(attribute='payment_date')|map('string')|select('match', current_month + '.*')|list|length > 0 %}
                {% if paid_this_month %}
                    <span class="text-green-400">نعم</span>
                {% else %}
                    <span class="text-red-400">لا</span>
                {% endif %}
            </div>
            <div class="text-white text-opacity-70">دفع هذا الشهر</div>
        </div>
    </div>
</div>

<!-- سجل الدفعات -->
<div class="statement-card">
    <div class="flex justify-between items-center mb-4">
        <h3 class="text-xl font-bold text-white">سجل الدفعات</h3>
        <button onclick="addPayment()" class="btn-primary text-sm">
            <i class="fas fa-plus ml-1"></i>
            إضافة دفعة
        </button>
    </div>
    
    {% if subscription.payments %}
        <div class="timeline">
            {% for payment in subscription.payments %}
            <div class="timeline-item">
                <div class="timeline-content">
                    <div class="flex justify-between items-start">
                        <div>
                            <div class="text-white font-semibold">${{ "%.2f"|format(payment.amount) }}</div>
                            <div class="text-white text-opacity-70 text-sm">{{ payment.payment_date }}</div>
                            <div class="text-white text-opacity-60 text-xs">
                                طريقة الدفع: 
                                {% if payment.payment_method == 'cash' %}نقداً
                                {% elif payment.payment_method == 'bank_transfer' %}تحويل بنكي
                                {% elif payment.payment_method == 'card' %}بطاقة
                                {% else %}{{ payment.payment_method }}{% endif %}
                            </div>
                            {% if payment.notes %}
                            <div class="text-white text-opacity-60 text-xs mt-1">{{ payment.notes }}</div>
                            {% endif %}
                        </div>
                        <div class="flex items-center gap-2">
                            <span class="badge-{{ 'success' if payment.status == 'completed' else 'warning' if payment.status == 'pending' else 'danger' }} text-xs">
                                {% if payment.status == 'completed' %}مكتمل
                                {% elif payment.status == 'pending' %}معلق
                                {% elif payment.status == 'failed' %}فشل
                                {% else %}{{ payment.status }}{% endif %}
                            </span>
                            <button onclick="editPayment({{ payment.id }})" class="text-blue-400 hover:text-blue-300">
                                <i class="fas fa-edit text-xs"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    {% else %}
        <div class="text-center py-8">
            <i class="fas fa-receipt text-4xl text-white text-opacity-30 mb-4"></i>
            <p class="text-white text-opacity-70">لا توجد دفعات مسجلة</p>
            <button onclick="addPayment()" class="btn-primary mt-4">
                <i class="fas fa-plus ml-2"></i>
                إضافة أول دفعة
            </button>
        </div>
    {% endif %}
</div>

<!-- تذكيرات الدفع -->
{% if subscription.is_expiring_soon or subscription.remaining_amount > 0 %}
<div class="statement-card">
    <h3 class="text-xl font-bold text-white mb-4">تذكيرات</h3>
    <div class="space-y-3">
        {% if subscription.is_expiring_soon %}
        <div class="bg-yellow-500 bg-opacity-20 border border-yellow-500 border-opacity-30 rounded-lg p-3">
            <div class="flex items-center">
                <i class="fas fa-exclamation-triangle text-yellow-400 ml-3"></i>
                <div>
                    <div class="text-white font-semibold">الاشتراك ينتهي قريباً</div>
                    <div class="text-white text-opacity-70 text-sm">باقي {{ subscription.days_remaining }} أيام</div>
                </div>
            </div>
        </div>
        {% endif %}
        
        {% if subscription.remaining_amount > 0 %}
        <div class="bg-red-500 bg-opacity-20 border border-red-500 border-opacity-30 rounded-lg p-3">
            <div class="flex items-center">
                <i class="fas fa-dollar-sign text-red-400 ml-3"></i>
                <div>
                    <div class="text-white font-semibold">يوجد مبلغ متبقي</div>
                    <div class="text-white text-opacity-70 text-sm">${{ "%.2f"|format(subscription.remaining_amount) }} غير مدفوع</div>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
function addPayment() {
    // فتح نافذة إضافة دفعة
    window.location.href = `/subscription/{{ subscription.id }}/add-payment`;
}

function editPayment(paymentId) {
    // فتح نافذة تعديل دفعة
    window.location.href = `/payment/${paymentId}/edit`;
}

function printStatement() {
    window.print();
}

function exportStatement() {
    window.location.href = `/subscription/{{ subscription.id }}/statement/export`;
}
</script>
{% endblock %}
