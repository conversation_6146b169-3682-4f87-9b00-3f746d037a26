{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام إدارة الاشتراكات{% endblock %}

{% block page_title %}لوحة التحكم{% endblock %}
{% block page_description %}مرحباً المهندس محمد ياسر الجبيري! إليك نظرة عامة على نشاط النظام{% endblock %}

{% block header_actions %}
<!-- Search -->
<div class="relative">
    <input type="text" placeholder="البحث..." class="input-field w-64 text-sm">
    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
</div>

<!-- Notifications -->
<div class="relative">
    <button id="notificationBtn" class="relative p-2 text-gray-400 hover:text-gray-600 transition-colors">
        <i class="fas fa-bell text-xl"></i>
        <span id="notificationBadge" class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center {{ 'hidden' if unread_notifications == 0 }}">
            {{ unread_notifications }}
        </span>
    </button>

    <!-- Notifications Dropdown -->
    <div id="notificationDropdown" class="hidden absolute left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
        <div class="p-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">الإشعارات</h3>
        </div>
        <div id="notificationsList" class="max-h-64 overflow-y-auto">
            <!-- سيتم تحميل الإشعارات هنا -->
        </div>
        <div class="p-3 border-t border-gray-200">
            <a href="{{ url_for('notifications') }}" class="text-sm text-blue-600 hover:text-blue-800">عرض جميع الإشعارات</a>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<a href="{{ url_for('add_subscription') }}" class="btn-primary">
    <i class="fas fa-plus ml-2"></i>
    إضافة اشتراك
</a>
{% endblock %}

{% block content %}
<!-- إحصائيات تفاعلية محدثة -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- إجمالي الاشتراكات -->
    <div class="glass-card p-6 hover:scale-105 transition-all duration-500 group cursor-pointer" onclick="animateCard(this)">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-white text-opacity-80">إجمالي الاشتراكات</p>
                <p class="text-4xl font-bold text-white mt-2 counter" data-target="{{ total_subscriptions }}">0</p>
                <div class="flex items-center mt-3">
                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse ml-2"></div>
                    <span class="text-green-400 text-sm font-medium">+12% من الشهر الماضي</span>
                </div>
            </div>
            <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-cyan-500 rounded-2xl flex items-center justify-center group-hover:rotate-12 transition-transform duration-500 shadow-lg">
                <i class="fas fa-server text-2xl text-white"></i>
            </div>
        </div>
        <div class="progress-bar mt-4">
            <div class="progress-fill" style="width: 0%" data-width="75"></div>
        </div>
    </div>

    <!-- الاشتراكات النشطة -->
    <div class="glass-card p-6 hover:scale-105 transition-all duration-500 group cursor-pointer" onclick="animateCard(this)">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-white text-opacity-80">الاشتراكات النشطة</p>
                <p class="text-4xl font-bold text-white mt-2 counter" data-target="{{ active_subscriptions }}">0</p>
                <div class="flex items-center mt-3">
                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse ml-2"></div>
                    <span class="text-green-400 text-sm font-medium">+8% من الشهر الماضي</span>
                </div>
            </div>
            <div class="w-16 h-16 bg-gradient-to-r from-green-500 to-emerald-500 rounded-2xl flex items-center justify-center group-hover:rotate-12 transition-transform duration-500 shadow-lg">
                <i class="fas fa-check-circle text-2xl text-white"></i>
            </div>
        </div>
        <div class="progress-bar mt-4">
            <div class="progress-fill" style="width: 0%" data-width="85"></div>
        </div>
    </div>

    <!-- إجمالي الإيرادات -->
    <div class="glass-card p-6 hover:scale-105 transition-all duration-500 group cursor-pointer" onclick="animateCard(this)">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-white text-opacity-80">إجمالي الإيرادات</p>
                <p class="text-4xl font-bold text-white mt-2">$<span class="counter" data-target="{{ total_revenue|int }}">0</span></p>
                <div class="flex items-center mt-3">
                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse ml-2"></div>
                    <span class="text-green-400 text-sm font-medium">+25% هذا الشهر</span>
                </div>
            </div>
            <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-pink-500 rounded-2xl flex items-center justify-center group-hover:rotate-12 transition-transform duration-500 shadow-lg">
                <i class="fas fa-dollar-sign text-2xl text-white"></i>
            </div>
        </div>
        <div class="progress-bar mt-4">
            <div class="progress-fill" style="width: 0%" data-width="90"></div>
        </div>
    </div>

    <!-- الفواتير المعلقة -->
    <div class="glass-card p-6 hover:scale-105 transition-all duration-500 group cursor-pointer" onclick="animateCard(this)">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-white text-opacity-80">الفواتير المعلقة</p>
                <p class="text-4xl font-bold text-white mt-2 counter" data-target="{{ pending_invoices }}">0</p>
                <div class="flex items-center mt-3">
                    <div class="w-2 h-2 bg-yellow-400 rounded-full animate-pulse ml-2"></div>
                    <span class="text-yellow-400 text-sm font-medium">تحتاج متابعة</span>
                </div>
            </div>
            <div class="w-16 h-16 bg-gradient-to-r from-yellow-500 to-orange-500 rounded-2xl flex items-center justify-center group-hover:rotate-12 transition-transform duration-500 shadow-lg">
                <i class="fas fa-clock text-2xl text-white"></i>
            </div>
        </div>
        <div class="progress-bar mt-4">
            <div class="progress-fill" style="width: 0%" data-width="45"></div>
        </div>
    </div>
</div>

    <div class="card p-6 border-r-4 border-yellow-500">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">الفواتير المعلقة</p>
                <p class="text-3xl font-bold text-gray-900 mt-2">{{ pending_invoices }}</p>
                <div class="flex items-center mt-2">
                    <i class="fas fa-arrow-down text-red-500 text-sm ml-1"></i>
                    <span class="text-red-500 text-sm font-medium">-3%</span>
                    <span class="text-gray-500 text-sm mr-2">من الشهر الماضي</span>
                </div>
            </div>
            <div class="p-3 bg-yellow-100 rounded-full">
                <i class="fas fa-file-invoice text-2xl text-yellow-600"></i>
            </div>
        </div>
    </div>

    <div class="card p-6 border-r-4 border-indigo-500">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">المستخدمين</p>
                <p class="text-3xl font-bold text-gray-900 mt-2">{{ total_users }}</p>
                <div class="flex items-center mt-2">
                    <i class="fas fa-arrow-up text-green-500 text-sm ml-1"></i>
                    <span class="text-green-500 text-sm font-medium">+2</span>
                    <span class="text-gray-500 text-sm mr-2">هذا الأسبوع</span>
                </div>
            </div>
            <div class="p-3 bg-indigo-100 rounded-full">
                <i class="fas fa-users text-2xl text-indigo-600"></i>
            </div>
        </div>
    </div>
</div>

<!-- Provider Statistics -->
<div class="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-8">
    <div class="lg:col-span-3 card p-6">
        <div class="flex items-center justify-between mb-6">
            <div>
                <h3 class="text-lg font-semibold text-gray-900">نمو الاشتراكات والإيرادات</h3>
                <p class="text-sm text-gray-600">إحصائيات الـ 6 أشهر الماضية</p>
            </div>
            <div class="flex space-x-2 space-x-reverse">
                <button class="btn-secondary text-sm">شهري</button>
                <button class="btn-primary text-sm">سنوي</button>
            </div>
        </div>
        <div class="h-80">
            <canvas id="subscriptionChart"></canvas>
        </div>
    </div>

    <!-- Provider Distribution -->
    <div class="card p-6">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">توزيع مزودي الخدمات</h3>
        <div class="space-y-3">
            {% for provider_name, count in provider_stats %}
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-blue-500 rounded-full ml-2"></div>
                    <span class="text-sm text-gray-700">{{ provider_name }}</span>
                </div>
                <span class="text-sm font-medium text-gray-900">{{ count }}</span>
            </div>
            {% endfor %}
        </div>
        <div class="mt-4 pt-4 border-t border-gray-200">
            <div class="text-center">
                <span class="text-2xl font-bold text-gray-900">{{ total_subscriptions }}</span>
                <p class="text-sm text-gray-600">إجمالي الاشتراكات</p>
            </div>
        </div>
    </div>
</div>

<!-- Charts and Quick Info -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
    <!-- Chart -->
    <div class="lg:col-span-2 card p-6">
        <div class="flex items-center justify-between mb-6">
            <div>
                <h3 class="text-lg font-semibold text-gray-900">نمو الاشتراكات</h3>
                <p class="text-sm text-gray-600">إحصائيات الـ 6 أشهر الماضية</p>
            </div>
            <div class="flex space-x-2 space-x-reverse">
                <button class="btn-secondary text-sm">شهري</button>
                <button class="btn-primary text-sm">سنوي</button>
            </div>
        </div>
        <div class="h-80">
            <canvas id="subscriptionChart"></canvas>
        </div>
    </div>

    <!-- Expiring Soon Alert -->
    <div class="space-y-6">
        <div class="card p-6 border-r-4 border-yellow-400">
            <div class="flex items-center mb-4">
                <i class="fas fa-exclamation-triangle text-yellow-500 text-xl ml-3"></i>
                <h4 class="font-semibold text-gray-900">تنبيه مهم</h4>
            </div>
            <p class="text-sm text-gray-600 mb-4">
                لديك {{ upcoming_expiry|length }} اشتراكات ستنتهي خلال الشهر القادم
            </p>
            <div class="space-y-2 mb-4">
                {% for subscription in upcoming_expiry[:3] %}
                <div class="flex justify-between text-xs">
                    <span>{{ subscription.name }}</span>
                    <span class="text-red-500">{{ (subscription.end_date - today).days }} يوم</span>
                </div>
                {% endfor %}
            </div>
            <a href="{{ url_for('subscriptions') }}?status=active" class="btn-primary w-full text-sm">
                عرض التفاصيل
            </a>
        </div>

        <!-- Quick Actions -->
        <div class="card p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">إجراءات سريعة</h3>
            <div class="space-y-3">
                <a href="{{ url_for('add_subscription') }}" class="btn-primary w-full text-sm">
                    <i class="fas fa-plus ml-2"></i>
                    إضافة اشتراك جديد
                </a>
                <button class="btn-secondary w-full text-sm">
                    <i class="fas fa-file-export ml-2"></i>
                    تصدير التقارير
                </button>
                <button class="btn-secondary w-full text-sm">
                    <i class="fas fa-cog ml-2"></i>
                    إعدادات النظام
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Recent Subscriptions Table -->
<div class="card p-6">
    <div class="flex items-center justify-between mb-6">
        <div>
            <h3 class="text-lg font-semibold text-gray-900">الاشتراكات الحديثة</h3>
            <p class="text-sm text-gray-600">آخر الاشتراكات المضافة والمحدثة</p>
        </div>
        <a href="{{ url_for('subscriptions') }}" class="btn-primary">عرض الكل</a>
    </div>
    
    <div class="overflow-x-auto">
        <table class="min-w-full">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الاشتراك</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">مزود الخدمة</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">النوع</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">السعر</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">الإجراءات</th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                {% for subscription in recent_subscriptions %}
                <tr class="table-row">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10">
                                <div class="h-10 w-10 rounded-lg bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center">
                                    <i class="fas fa-server text-white"></i>
                                </div>
                            </div>
                            <div class="mr-4">
                                <div class="text-sm font-medium text-gray-900">{{ subscription.name }}</div>
                                <div class="text-sm text-gray-500">{{ subscription.api_key[:10] }}...</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="text-sm text-gray-900">{{ subscription.provider.name }}</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="badge-primary">{{ subscription.subscription_type }}</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        ${{ "%.2f"|format(subscription.price) }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="badge-{{ 'success' if subscription.status == 'active' else 'warning' if subscription.status == 'suspended' else 'danger' }}">
                            {{ subscription.status }}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex space-x-2 space-x-reverse">
                            <a href="{{ url_for('edit_subscription', id=subscription.id) }}" class="text-blue-600 hover:text-blue-900 p-1 rounded">
                                <i class="fas fa-eye"></i>
                            </a>
                            <a href="{{ url_for('edit_subscription', id=subscription.id) }}" class="text-yellow-600 hover:text-yellow-900 p-1 rounded">
                                <i class="fas fa-edit"></i>
                            </a>
                            <form method="POST" action="{{ url_for('delete_subscription', id=subscription.id) }}" class="inline" onsubmit="return confirm('هل أنت متأكد من حذف هذا الاشتراك؟')">
                                <button type="submit" class="text-red-600 hover:text-red-900 p-1 rounded">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </form>
                        </div>
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Chart.js Configuration
    const ctx = document.getElementById('subscriptionChart').getContext('2d');
    const chartData = {{ chart_data|safe }};
    
    const subscriptionChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: chartData.map(item => item.month),
            datasets: [{
                label: 'الاشتراكات الجديدة',
                data: chartData.map(item => item.count),
                borderColor: 'rgb(102, 126, 234)',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: 'rgb(102, 126, 234)',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20,
                        font: {
                            family: 'Cairo',
                            size: 12
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    },
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                },
                x: {
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    },
                    ticks: {
                        font: {
                            family: 'Cairo'
                        }
                    }
                }
            }
        }
    });

    // Notifications functionality
    const notificationBtn = document.getElementById('notificationBtn');
    const notificationDropdown = document.getElementById('notificationDropdown');
    const notificationsList = document.getElementById('notificationsList');
    const notificationBadge = document.getElementById('notificationBadge');

    // Toggle notifications dropdown
    notificationBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        notificationDropdown.classList.toggle('hidden');

        if (!notificationDropdown.classList.contains('hidden')) {
            loadNotifications();
        }
    });

    // Close dropdown when clicking outside
    document.addEventListener('click', function() {
        notificationDropdown.classList.add('hidden');
    });

    // Load notifications
    function loadNotifications() {
        fetch('/api/notifications/unread')
            .then(response => response.json())
            .then(data => {
                updateNotificationBadge(data.count);
                displayNotifications(data.notifications);
            })
            .catch(error => console.error('Error loading notifications:', error));
    }

    // Update notification badge
    function updateNotificationBadge(count) {
        if (count > 0) {
            notificationBadge.textContent = count;
            notificationBadge.classList.remove('hidden');
        } else {
            notificationBadge.classList.add('hidden');
        }
    }

    // Display notifications in dropdown
    function displayNotifications(notifications) {
        if (notifications.length === 0) {
            notificationsList.innerHTML = `
                <div class="p-4 text-center text-gray-500">
                    <i class="fas fa-bell-slash text-2xl mb-2"></i>
                    <p class="text-sm">لا توجد إشعارات جديدة</p>
                </div>
            `;
            return;
        }

        notificationsList.innerHTML = notifications.map(notification => `
            <div class="p-3 hover:bg-gray-50 border-b border-gray-100 cursor-pointer"
                 onclick="markNotificationRead(${notification.id})">
                <div class="flex items-start space-x-3 space-x-reverse">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 rounded-full flex items-center justify-center
                                    ${notification.type === 'success' ? 'bg-green-100' :
                                      notification.type === 'warning' ? 'bg-yellow-100' :
                                      notification.type === 'error' ? 'bg-red-100' : 'bg-blue-100'}">
                            <i class="fas ${notification.type === 'success' ? 'fa-check-circle text-green-600' :
                                           notification.type === 'warning' ? 'fa-exclamation-triangle text-yellow-600' :
                                           notification.type === 'error' ? 'fa-exclamation-circle text-red-600' :
                                           'fa-info-circle text-blue-600'} text-sm"></i>
                        </div>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900">${notification.title}</p>
                        <p class="text-xs text-gray-500 mt-1">${notification.message}</p>
                        <p class="text-xs text-gray-400 mt-1">${notification.created_at}</p>
                    </div>
                </div>
            </div>
        `).join('');
    }

    // Mark notification as read
    function markNotificationRead(notificationId) {
        fetch(`/notifications/${notificationId}/read`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                loadNotifications(); // Reload notifications
            }
        })
        .catch(error => console.error('Error marking notification as read:', error));
    }

    // Auto-refresh notifications every 30 seconds
    setInterval(loadNotifications, 30000);

    // Load notifications on page load
    loadNotifications();

    // تأثيرات الأرقام المتحركة
    function animateCounters() {
        const counters = document.querySelectorAll('.counter');
        counters.forEach(counter => {
            const target = parseInt(counter.getAttribute('data-target'));
            const increment = target / 100;
            let current = 0;

            const updateCounter = () => {
                if (current < target) {
                    current += increment;
                    counter.textContent = Math.ceil(current);
                    requestAnimationFrame(updateCounter);
                } else {
                    counter.textContent = target;
                }
            };

            updateCounter();
        });
    }

    // تأثيرات شريط التقدم
    function animateProgressBars() {
        const progressBars = document.querySelectorAll('.progress-fill');
        progressBars.forEach((bar, index) => {
            setTimeout(() => {
                const width = bar.getAttribute('data-width');
                bar.style.width = width + '%';
            }, index * 200);
        });
    }

    // تأثير النقر على الكارت
    function animateCard(card) {
        card.style.transform = 'scale(0.95)';
        setTimeout(() => {
            card.style.transform = 'scale(1.05)';
            setTimeout(() => {
                card.style.transform = 'scale(1)';
            }, 150);
        }, 150);
    }

    // تأثيرات التحميل
    document.addEventListener('DOMContentLoaded', function() {
        // تأثير التحميل التدريجي
        const cards = document.querySelectorAll('.glass-card');
        cards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(50px)';

            setTimeout(() => {
                card.style.transition = 'all 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, index * 200);
        });

        // بدء تشغيل الأنيميشن بعد التحميل
        setTimeout(() => {
            animateCounters();
            animateProgressBars();
        }, 1000);

        // تأثير الماوس ثلاثي الأبعاد للكروت
        cards.forEach(card => {
            card.addEventListener('mousemove', function(e) {
                const rect = card.getBoundingClientRect();
                const x = e.clientX - rect.left;
                const y = e.clientY - rect.top;

                const centerX = rect.width / 2;
                const centerY = rect.height / 2;

                const rotateX = (y - centerY) / 10;
                const rotateY = (centerX - x) / 10;

                card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(20px)`;
            });

            card.addEventListener('mouseleave', function() {
                card.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateZ(0)';
            });
        });
    });

    // Auto-refresh stats every 30 seconds with animation
    setInterval(() => {
        fetch('/api/stats')
            .then(response => response.json())
            .then(data => {
                // Update stats with animation
                const counters = document.querySelectorAll('.counter');
                counters.forEach(counter => {
                    const statType = counter.closest('.glass-card').querySelector('p').textContent;
                    let newValue = 0;

                    if (statType.includes('إجمالي الاشتراكات')) {
                        newValue = data.total_subscriptions;
                    } else if (statType.includes('النشطة')) {
                        newValue = data.active_subscriptions;
                    } else if (statType.includes('الإيرادات')) {
                        newValue = data.total_revenue;
                    } else if (statType.includes('المعلقة')) {
                        newValue = data.pending_invoices;
                    }

                    if (newValue !== parseInt(counter.textContent)) {
                        counter.setAttribute('data-target', newValue);
                        animateCounters();
                    }
                });

                console.log('Stats updated with animation:', data);
            })
            .catch(error => console.error('Error fetching stats:', error));
    }, 30000);
</script>
{% endblock %}
