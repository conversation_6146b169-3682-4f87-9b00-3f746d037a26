<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار تسجيل الدخول</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #555;
            font-weight: 600;
        }
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        input[type="text"]:focus, input[type="password"]:focus {
            outline: none;
            border-color: #667eea;
        }
        .btn {
            width: 100%;
            padding: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s;
        }
        .btn:hover {
            transform: translateY(-2px);
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            display: none;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 اختبار تسجيل الدخول</h1>
        <form id="loginForm">
            <div class="form-group">
                <label for="username">اسم المستخدم:</label>
                <input type="text" id="username" name="username" value="admin" required>
            </div>
            <div class="form-group">
                <label for="password">كلمة المرور:</label>
                <input type="password" id="password" name="password" value="123456" required>
            </div>
            <button type="submit" class="btn">تسجيل الدخول</button>
        </form>
        <div id="result" class="result"></div>
        
        <div style="margin-top: 30px; text-align: center; color: #666;">
            <p><strong>بيانات الاختبار:</strong></p>
            <p>المستخدم: admin</p>
            <p>كلمة المرور: 123456</p>
        </div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            try {
                const response = await fetch('http://localhost:7722/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}`,
                    credentials: 'include'
                });
                
                if (response.ok) {
                    const text = await response.text();
                    if (text.includes('dashboard') || response.url.includes('dashboard')) {
                        resultDiv.className = 'result success';
                        resultDiv.textContent = '✅ تم تسجيل الدخول بنجاح! سيتم توجيهك للوحة التحكم...';
                        resultDiv.style.display = 'block';
                        
                        setTimeout(() => {
                            window.location.href = 'http://localhost:7722/dashboard';
                        }, 2000);
                    } else {
                        resultDiv.className = 'result error';
                        resultDiv.textContent = '❌ فشل في تسجيل الدخول. تحقق من البيانات.';
                        resultDiv.style.display = 'block';
                    }
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ خطأ في الاتصال بالخادم.';
                    resultDiv.style.display = 'block';
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ خطأ في الشبكة: ' + error.message;
                resultDiv.style.display = 'block';
            }
        });
    </script>
</body>
</html>
