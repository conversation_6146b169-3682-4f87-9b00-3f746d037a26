{% extends "base.html" %}

{% block title %}إضافة اشتراك جديد - نظام إدارة الاشتراكات{% endblock %}
{% block page_title %}إضافة اشتراك جديد{% endblock %}
{% block page_description %}إضافة اشتراك جديد مع جميع البيانات المطلوبة{% endblock %}

{% block extra_css %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/subscriptions-advanced.css') }}">
<style>
.form-section {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    transition: all 0.3s ease;
}

.form-section:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.form-section h3 {
    color: white;
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.form-section h3 i {
    width: 2rem;
    height: 2rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    color: white;
    font-weight: 500;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 0.75rem 1rem;
    color: white;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: rgba(255, 255, 255, 0.15);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: rgba(255, 255, 255, 0.5);
}

.btn-submit {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
    padding: 1rem 2rem;
    border-radius: 12px;
    border: none;
    font-weight: 600;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    justify-content: center;
    min-width: 200px;
}

.btn-submit:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
}

.btn-submit:active {
    transform: translateY(0);
}

@media (max-width: 768px) {
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .form-section {
        padding: 1.5rem;
        margin: 0 1rem 1.5rem;
    }
}
</style>
{% endblock %}

{% block header_actions %}
<div class="flex items-center space-x-3 space-x-reverse">
    <a href="{{ url_for('subscriptions') }}" class="btn-secondary">
        <i class="fas fa-arrow-right ml-2"></i>
        العودة للاشتراكات
    </a>
</div>
{% endblock %}

{% block content %}
<form method="POST" class="max-w-6xl mx-auto">
    <!-- بيانات العميل -->
    <div class="form-section">
        <h3>
            <i class="fas fa-user"></i>
            بيانات العميل
        </h3>
        <div class="form-grid">
            <div class="form-group">
                <label for="customer_name">اسم العميل *</label>
                <input type="text" id="customer_name" name="customer_name" required 
                       placeholder="أدخل اسم العميل الكامل">
            </div>
            <div class="form-group">
                <label for="customer_email">البريد الإلكتروني</label>
                <input type="email" id="customer_email" name="customer_email" 
                       placeholder="<EMAIL>">
            </div>
            <div class="form-group">
                <label for="customer_phone">رقم الهاتف</label>
                <input type="tel" id="customer_phone" name="customer_phone" 
                       placeholder="+966 50 123 4567">
            </div>
        </div>
    </div>

    <!-- بيانات الشبكة والخدمة -->
    <div class="form-section">
        <h3>
            <i class="fas fa-network-wired"></i>
            بيانات الشبكة والخدمة
        </h3>
        <div class="form-grid">
            <div class="form-group">
                <label for="network_name">اسم الشبكة *</label>
                <input type="text" id="network_name" name="name" required 
                       placeholder="أدخل اسم الشبكة">
            </div>
            <div class="form-group">
                <label for="provider_id">مزود الخدمة</label>
                <select id="provider_id" name="provider_id">
                    <option value="">شركة محمد الجبوري (افتراضي)</option>
                    {% for provider in providers %}
                    <option value="{{ provider.id }}">{{ provider.name }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="form-group">
                <label for="cloud_name">اسم الكلاود *</label>
                <input type="text" id="cloud_name" name="cloud_name" required 
                       placeholder="مثال: AWS EC2, DigitalOcean">
            </div>
            <div class="form-group">
                <label for="cloud_ip">IP الكلاود *</label>
                <input type="text" id="cloud_ip" name="cloud_ip" required 
                       placeholder="***********">
            </div>
            <div class="form-group">
                <label for="port">البورت *</label>
                <input type="text" id="port" name="port" required 
                       placeholder="8080">
            </div>
            <div class="form-group">
                <label for="api_key">API Key</label>
                <input type="text" id="api_key" name="api_key" 
                       placeholder="أدخل API Key إذا كان متوفراً">
            </div>
        </div>
    </div>

    <!-- بيانات الاشتراك -->
    <div class="form-section">
        <h3>
            <i class="fas fa-calendar-alt"></i>
            بيانات الاشتراك
        </h3>
        <div class="form-grid">
            <div class="form-group">
                <label for="subscription_type">نوع الاشتراك *</label>
                <select id="subscription_type" name="subscription_type" required onchange="updatePrice()">
                    <option value="">اختر نوع الاشتراك</option>
                    <option value="monthly">شهري</option>
                    <option value="semi_annual">نصف سنوي</option>
                    <option value="annual">سنوي</option>
                </select>
            </div>
            <div class="form-group">
                <label for="price">سعر الاشتراك ($) *</label>
                <input type="number" id="price" name="price" step="0.01" required 
                       placeholder="0.00">
            </div>
            <div class="form-group">
                <label for="start_date">تاريخ بداية الاشتراك *</label>
                <input type="date" id="start_date" name="start_date" required 
                       value="{{ today }}">
            </div>
            <div class="form-group">
                <label for="end_date">تاريخ انتهاء الاشتراك *</label>
                <input type="date" id="end_date" name="end_date" required>
            </div>
            <div class="form-group">
                <label for="status">حالة الاشتراك</label>
                <select id="status" name="status">
                    <option value="active">نشط</option>
                    <option value="suspended">معلق</option>
                    <option value="expired">منتهي</option>
                </select>
            </div>
            <div class="form-group">
                <label for="accounting_status">حالة الدفع</label>
                <select id="accounting_status" name="accounting_status">
                    <option value="paid">مدفوع</option>
                    <option value="unpaid">غير مدفوع</option>
                </select>
            </div>
        </div>
    </div>

    <!-- ملاحظات إضافية -->
    <div class="form-section">
        <h3>
            <i class="fas fa-sticky-note"></i>
            ملاحظات إضافية
        </h3>
        <div class="form-group">
            <label for="notes">ملاحظات</label>
            <textarea id="notes" name="notes" rows="4" 
                      placeholder="أدخل أي ملاحظات إضافية حول الاشتراك"></textarea>
        </div>
    </div>

    <!-- أزرار التحكم -->
    <div class="flex justify-center gap-4 mt-8">
        <button type="submit" class="btn-submit">
            <i class="fas fa-save"></i>
            حفظ الاشتراك
        </button>
        <a href="{{ url_for('subscriptions') }}" class="btn-secondary">
            <i class="fas fa-times"></i>
            إلغاء
        </a>
    </div>
</form>
{% endblock %}

{% block extra_js %}
<script>
// تحديث السعر تلقائياً حسب نوع الاشتراك
function updatePrice() {
    const subscriptionType = document.getElementById('subscription_type').value;
    const priceInput = document.getElementById('price');
    
    const prices = {
        'monthly': 29.99,
        'semi_annual': 149.99,
        'annual': 299.99
    };
    
    if (prices[subscriptionType]) {
        priceInput.value = prices[subscriptionType];
    }
    
    updateEndDate();
}

// تحديث تاريخ الانتهاء تلقائياً
function updateEndDate() {
    const startDate = document.getElementById('start_date').value;
    const subscriptionType = document.getElementById('subscription_type').value;
    const endDateInput = document.getElementById('end_date');
    
    if (startDate && subscriptionType) {
        const start = new Date(startDate);
        let end = new Date(start);
        
        switch(subscriptionType) {
            case 'monthly':
                end.setMonth(end.getMonth() + 1);
                break;
            case 'semi_annual':
                end.setMonth(end.getMonth() + 6);
                break;
            case 'annual':
                end.setFullYear(end.getFullYear() + 1);
                break;
        }
        
        endDateInput.value = end.toISOString().split('T')[0];
    }
}

// تحديث تاريخ الانتهاء عند تغيير تاريخ البداية
document.getElementById('start_date').addEventListener('change', updateEndDate);

// تعيين تاريخ اليوم كافتراضي
document.addEventListener('DOMContentLoaded', function() {
    const today = new Date().toISOString().split('T')[0];
    document.getElementById('start_date').value = today;
});
</script>
{% endblock %}
