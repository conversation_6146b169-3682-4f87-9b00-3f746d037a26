{% extends "base.html" %}

{% block title %}إدارة الموزعين - نظام إدارة الاشتراكات{% endblock %}
{% block page_title %}إدارة الموزعين{% endblock %}
{% block page_description %}إدارة شاملة للموزعين والباقات والشحن{% endblock %}

{% block extra_css %}
<style>
.distributors-container {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 2rem;
    margin-bottom: 2rem;
}

.distributors-main {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 2rem;
}

.distributors-sidebar {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    height: fit-content;
}

.section-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 2rem;
}

.section-title {
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.section-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 8px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.125rem;
}

.action-buttons {
    display: flex;
    gap: 1rem;
}

.action-btn {
    padding: 0.75rem 1.5rem;
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 600;
    transition: all var(--transition-medium);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.action-btn.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-color: var(--primary-color);
}

.action-btn.success {
    background: linear-gradient(135deg, var(--success-color), #059669);
    color: white;
    border-color: var(--success-color);
}

.distributors-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.distributor-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    padding: 1.5rem;
    transition: all var(--transition-medium);
    position: relative;
    overflow: hidden;
}

.distributor-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
    border-color: var(--glass-border-light);
}

.distributor-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, var(--success-color), #059669);
}

.distributor-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.distributor-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--success-color), #059669);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    font-weight: bold;
}

.distributor-info {
    flex: 1;
}

.distributor-name {
    color: var(--text-primary);
    font-size: 1.125rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.distributor-code {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    display: inline-block;
}

.distributor-stats {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.stat-item {
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border: 1px solid var(--glass-border);
}

.stat-value {
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.75rem;
    font-weight: 500;
}

.distributor-actions {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
}

.dist-btn {
    padding: 0.5rem;
    border: 1px solid var(--glass-border);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-secondary);
    font-size: 0.75rem;
    cursor: pointer;
    transition: all var(--transition-medium);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
}

.dist-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.dist-btn.primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.dist-btn.success {
    background: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

.dist-btn.warning {
    background: var(--warning-color);
    color: white;
    border-color: var(--warning-color);
}

.packages-section {
    margin-bottom: 2rem;
}

.packages-grid {
    display: grid;
    gap: 1rem;
}

.package-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    padding: 1rem;
    transition: all var(--transition-medium);
}

.package-card:hover {
    background: rgba(255, 255, 255, 0.1);
}

.package-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.package-name {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.875rem;
}

.package-price {
    color: var(--success-color);
    font-weight: 700;
    font-size: 0.875rem;
}

.package-details {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
    margin-bottom: 0.75rem;
}

.package-detail {
    text-align: center;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 4px;
}

.package-detail-value {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.75rem;
    margin-bottom: 0.25rem;
}

.package-detail-label {
    color: var(--text-secondary);
    font-size: 0.625rem;
}

.package-actions {
    display: flex;
    gap: 0.5rem;
}

.package-btn {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid var(--glass-border);
    border-radius: 4px;
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-secondary);
    font-size: 0.75rem;
    cursor: pointer;
    transition: all var(--transition-medium);
}

.package-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.package-btn.primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.quick-stats {
    display: grid;
    grid-template-columns: 1fr;
    gap: 1rem;
    margin-bottom: 2rem;
}

.quick-stat {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
}

.quick-stat-value {
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.quick-stat-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
}

.quick-stat-change {
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

.quick-stat-change.positive {
    color: var(--success-color);
}

.quick-stat-change.negative {
    color: var(--error-color);
}

@media (max-width: 768px) {
    .distributors-container {
        grid-template-columns: 1fr;
    }
    
    .distributors-grid {
        grid-template-columns: 1fr;
    }
    
    .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .action-buttons {
        justify-content: center;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="distributors-container">
    <!-- المحتوى الرئيسي -->
    <div class="distributors-main">
        <div class="section-header">
            <h2 class="section-title">
                <div class="section-icon">
                    <i class="fas fa-store"></i>
                </div>
                إدارة الموزعين
            </h2>
            <div class="action-buttons">
                <button class="action-btn" onclick="exportDistributors()">
                    <i class="fas fa-download"></i>
                    تصدير
                </button>
                <button class="action-btn success" onclick="addDistributor()">
                    <i class="fas fa-plus"></i>
                    إضافة موزع
                </button>
            </div>
        </div>

        <!-- شبكة الموزعين -->
        <div class="distributors-grid">
            {% for distributor in distributors %}
            <div class="distributor-card">
                <div class="distributor-header">
                    <div class="distributor-avatar">
                        {{ distributor.first_name[0] if distributor.first_name else distributor.username[0] }}
                    </div>
                    <div class="distributor-info">
                        <div class="distributor-name">{{ distributor.first_name }} {{ distributor.last_name or '' }}</div>
                        <div class="distributor-code">{{ distributor.distributor_code or 'غير محدد' }}</div>
                    </div>
                </div>
                
                <div class="distributor-stats">
                    <div class="stat-item">
                        <div class="stat-value">{{ '{:,.0f}'.format(distributor.get_main_balance() if hasattr(distributor, 'get_main_balance') else 0) }}</div>
                        <div class="stat-label">الرصيد الرئيسي</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ '{:,.0f}'.format(distributor.get_commission_balance() if hasattr(distributor, 'get_commission_balance') else 0) }}</div>
                        <div class="stat-label">رصيد العمولة</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ distributor.commission_rate or 0 }}%</div>
                        <div class="stat-label">معدل العمولة</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">{{ distributor.territory or 'غير محدد' }}</div>
                        <div class="stat-label">المنطقة</div>
                    </div>
                </div>
                
                <div class="distributor-actions">
                    <button class="dist-btn primary" onclick="viewDistributor({{ distributor.id }})">
                        <i class="fas fa-eye"></i>
                        عرض
                    </button>
                    <button class="dist-btn success" onclick="chargeDistributor({{ distributor.id }})">
                        <i class="fas fa-plus-circle"></i>
                        شحن
                    </button>
                    <button class="dist-btn warning" onclick="managePackages({{ distributor.id }})">
                        <i class="fas fa-box"></i>
                        الباقات
                    </button>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- قسم الباقات -->
        <div class="packages-section">
            <div class="section-header">
                <h3 class="section-title">
                    <div class="section-icon">
                        <i class="fas fa-box"></i>
                    </div>
                    باقات الموزعين
                </h3>
                <div class="action-buttons">
                    <button class="action-btn primary" onclick="addPackage()">
                        <i class="fas fa-plus"></i>
                        إضافة باقة
                    </button>
                </div>
            </div>
            
            <div class="packages-grid">
                {% for package in packages %}
                <div class="package-card">
                    <div class="package-header">
                        <div class="package-name">{{ package.name_ar }}</div>
                        <div class="package-price">{{ '{:,.0f}'.format(package.price) }} ر.ي</div>
                    </div>
                    
                    <div class="package-details">
                        <div class="package-detail">
                            <div class="package-detail-value">{{ package.credit_amount or 0 }}</div>
                            <div class="package-detail-label">رصيد</div>
                        </div>
                        <div class="package-detail">
                            <div class="package-detail-value">{{ package.subscription_count or 0 }}</div>
                            <div class="package-detail-label">اشتراكات</div>
                        </div>
                        <div class="package-detail">
                            <div class="package-detail-value">{{ package.validity_days }}</div>
                            <div class="package-detail-label">أيام</div>
                        </div>
                    </div>
                    
                    <div class="package-actions">
                        <button class="package-btn" onclick="editPackage({{ package.id }})">
                            <i class="fas fa-edit"></i>
                            تعديل
                        </button>
                        <button class="package-btn primary" onclick="assignPackage({{ package.id }})">
                            <i class="fas fa-gift"></i>
                            تخصيص
                        </button>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- الشريط الجانبي -->
    <div class="distributors-sidebar">
        <!-- إحصائيات سريعة -->
        <div class="quick-stats">
            <div class="quick-stat">
                <div class="quick-stat-value">{{ total_distributors or 0 }}</div>
                <div class="quick-stat-label">إجمالي الموزعين</div>
                <div class="quick-stat-change positive">+5% هذا الشهر</div>
            </div>
            
            <div class="quick-stat">
                <div class="quick-stat-value">{{ '{:,.0f}'.format(total_sales or 0) }}</div>
                <div class="quick-stat-label">إجمالي المبيعات</div>
                <div class="quick-stat-change positive">+12% هذا الشهر</div>
            </div>
            
            <div class="quick-stat">
                <div class="quick-stat-value">{{ '{:,.0f}'.format(total_commissions or 0) }}</div>
                <div class="quick-stat-label">إجمالي العمولات</div>
                <div class="quick-stat-change positive">+8% هذا الشهر</div>
            </div>
            
            <div class="quick-stat">
                <div class="quick-stat-value">{{ active_packages or 0 }}</div>
                <div class="quick-stat-label">الباقات النشطة</div>
                <div class="quick-stat-change">{{ total_packages or 0 }} إجمالي</div>
            </div>
        </div>
        
        <!-- إجراءات سريعة -->
        <div class="section-header">
            <h3 class="section-title">
                <div class="section-icon">
                    <i class="fas fa-bolt"></i>
                </div>
                إجراءات سريعة
            </h3>
        </div>
        
        <div class="action-buttons" style="flex-direction: column;">
            <button class="action-btn" onclick="bulkCharge()">
                <i class="fas fa-coins"></i>
                شحن جماعي
            </button>
            <button class="action-btn" onclick="generateReports()">
                <i class="fas fa-chart-bar"></i>
                تقارير الموزعين
            </button>
            <button class="action-btn" onclick="manageCommissions()">
                <i class="fas fa-percentage"></i>
                إدارة العمولات
            </button>
            <button class="action-btn" onclick="distributorSettings()">
                <i class="fas fa-cog"></i>
                إعدادات الموزعين
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// إدارة الموزعين
function addDistributor() {
    window.location.href = '/distributors/add';
}

function viewDistributor(distributorId) {
    window.location.href = `/distributors/${distributorId}/view`;
}

function chargeDistributor(distributorId) {
    const amount = prompt('أدخل مبلغ الشحن:');
    if (amount && !isNaN(amount) && parseFloat(amount) > 0) {
        fetch(`/api/distributors/${distributorId}/charge`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                amount: parseFloat(amount),
                charge_type: 'manual',
                description: 'شحن يدوي من لوحة التحكم'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('تم شحن الموزع بنجاح', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showNotification('فشل في شحن الموزع: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showNotification('خطأ في الاتصال بالخادم', 'error');
        });
    }
}

function managePackages(distributorId) {
    window.location.href = `/distributors/${distributorId}/packages`;
}

function addPackage() {
    window.location.href = '/packages/add';
}

function editPackage(packageId) {
    window.location.href = `/packages/${packageId}/edit`;
}

function assignPackage(packageId) {
    const distributorId = prompt('أدخل ID الموزع:');
    if (distributorId && !isNaN(distributorId)) {
        fetch(`/api/packages/${packageId}/assign`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                distributor_id: parseInt(distributorId)
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('تم تخصيص الباقة بنجاح', 'success');
            } else {
                showNotification('فشل في تخصيص الباقة: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showNotification('خطأ في الاتصال بالخادم', 'error');
        });
    }
}

function exportDistributors() {
    window.open('/distributors/export', '_blank');
}

function bulkCharge() {
    window.location.href = '/distributors/bulk-charge';
}

function generateReports() {
    window.location.href = '/reports/distributors';
}

function manageCommissions() {
    window.location.href = '/distributors/commissions';
}

function distributorSettings() {
    window.location.href = '/settings/distributors';
}
</script>
{% endblock %}
