{% extends "base.html" %}

{% block title %}لوحة التحكم الذكية - نظام إدارة الاشتراكات{% endblock %}
{% block page_title %}لوحة التحكم الذكية{% endblock %}
{% block page_description %}تحليلات ذكية ومعلومات شاملة عن النظام{% endblock %}

{% block extra_css %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
<style>
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.dashboard-card {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: between;
    margin-bottom: 1rem;
}

.card-title {
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.card-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
}

.stat-value {
    font-size: 2.5rem;
    font-weight: bold;
    color: white;
    margin-bottom: 0.5rem;
}

.stat-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
}

.stat-change {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.stat-change.positive {
    color: #10b981;
}

.stat-change.negative {
    color: #ef4444;
}

.chart-container {
    position: relative;
    height: 300px;
    margin-top: 1rem;
}

.mini-chart {
    height: 100px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
    overflow: hidden;
    margin-top: 0.5rem;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #10b981, #059669);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.alert-item {
    background: rgba(255, 255, 255, 0.05);
    border-left: 4px solid;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 0.75rem;
    transition: all 0.3s ease;
}

.alert-item:hover {
    background: rgba(255, 255, 255, 0.1);
}

.alert-item.warning {
    border-left-color: #f59e0b;
}

.alert-item.danger {
    border-left-color: #ef4444;
}

.alert-item.info {
    border-left-color: #3b82f6;
}

.alert-item.success {
    border-left-color: #10b981;
}

.alert-title {
    color: white;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.alert-message {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.875rem;
}

.alert-time {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.quick-action {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    color: white;
}

.quick-action:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

.action-icon {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    display: block;
}

.action-label {
    font-size: 0.875rem;
    font-weight: 500;
}

.ai-insights {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.2), rgba(118, 75, 162, 0.2));
    border: 1px solid rgba(102, 126, 234, 0.3);
}

.insight-item {
    display: flex;
    align-items: start;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.insight-item:last-child {
    border-bottom: none;
}

.insight-icon {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.insight-content {
    flex: 1;
}

.insight-title {
    color: white;
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.insight-description {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.875rem;
    line-height: 1.4;
}

.insight-confidence {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

.real-time-indicator {
    position: absolute;
    top: 1rem;
    right: 1rem;
    width: 8px;
    height: 8px;
    background: #10b981;
    border-radius: 50%;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.metric-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.metric-item {
    text-align: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
}

.metric-value {
    font-size: 1.5rem;
    font-weight: bold;
    color: white;
    margin-bottom: 0.25rem;
}

.metric-label {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.75rem;
}

.trend-indicator {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.75rem;
    margin-top: 0.25rem;
}

.loading-skeleton {
    background: linear-gradient(90deg, rgba(255,255,255,0.1) 25%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0.1) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 4px;
    height: 1rem;
    margin: 0.25rem 0;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

@media (max-width: 768px) {
    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .metric-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>
{% endblock %}

{% block content %}
<!-- الإحصائيات الرئيسية -->
<div class="dashboard-grid">
    <!-- إجمالي الاشتراكات -->
    <div class="dashboard-card">
        <div class="real-time-indicator"></div>
        <div class="card-header">
            <div class="card-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                <i class="fas fa-users"></i>
            </div>
        </div>
        <div class="stat-value" id="total-subscriptions">{{ stats.total_subscriptions or 0 }}</div>
        <div class="stat-label">إجمالي الاشتراكات</div>
        <div class="stat-change positive">
            <i class="fas fa-arrow-up"></i>
            <span>+12% من الشهر الماضي</span>
        </div>
        <div class="progress-bar">
            <div class="progress-fill" style="width: 75%;"></div>
        </div>
    </div>

    <!-- الإيرادات الشهرية -->
    <div class="dashboard-card">
        <div class="card-header">
            <div class="card-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                <i class="fas fa-dollar-sign"></i>
            </div>
        </div>
        <div class="stat-value" id="monthly-revenue">${{ "%.2f"|format(stats.monthly_revenue or 0) }}</div>
        <div class="stat-label">الإيرادات الشهرية</div>
        <div class="stat-change positive">
            <i class="fas fa-arrow-up"></i>
            <span>+8.5% من الشهر الماضي</span>
        </div>
        <div class="chart-container mini-chart">
            <canvas id="revenue-trend-chart"></canvas>
        </div>
    </div>

    <!-- الاشتراكات النشطة -->
    <div class="dashboard-card">
        <div class="card-header">
            <div class="card-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                <i class="fas fa-chart-line"></i>
            </div>
        </div>
        <div class="stat-value" id="active-subscriptions">{{ stats.active_subscriptions or 0 }}</div>
        <div class="stat-label">الاشتراكات النشطة</div>
        <div class="stat-change positive">
            <i class="fas fa-arrow-up"></i>
            <span>+5.2% من الأسبوع الماضي</span>
        </div>
        <div class="metric-grid">
            <div class="metric-item">
                <div class="metric-value">{{ stats.expiring_soon or 0 }}</div>
                <div class="metric-label">تنتهي قريباً</div>
            </div>
            <div class="metric-item">
                <div class="metric-value">{{ stats.overdue or 0 }}</div>
                <div class="metric-label">متأخرة</div>
            </div>
        </div>
    </div>

    <!-- معدل التجديد -->
    <div class="dashboard-card">
        <div class="card-header">
            <div class="card-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                <i class="fas fa-sync-alt"></i>
            </div>
        </div>
        <div class="stat-value" id="renewal-rate">{{ "%.1f"|format(stats.renewal_rate or 0) }}%</div>
        <div class="stat-label">معدل التجديد</div>
        <div class="stat-change positive">
            <i class="fas fa-arrow-up"></i>
            <span>+2.1% من الشهر الماضي</span>
        </div>
        <div class="progress-bar">
            <div class="progress-fill" style="width: {{ stats.renewal_rate or 0 }}%;"></div>
        </div>
    </div>
</div>

<!-- الرسوم البيانية التفاعلية -->
<div class="dashboard-grid">
    <!-- رسم بياني للإيرادات -->
    <div class="dashboard-card" style="grid-column: span 2;">
        <div class="card-title">
            <i class="fas fa-chart-area"></i>
            تحليل الإيرادات الشهرية
        </div>
        <div class="chart-container">
            <canvas id="revenue-chart"></canvas>
        </div>
    </div>

    <!-- توزيع الاشتراكات -->
    <div class="dashboard-card">
        <div class="card-title">
            <i class="fas fa-pie-chart"></i>
            توزيع أنواع الاشتراكات
        </div>
        <div class="chart-container">
            <canvas id="subscription-types-chart"></canvas>
        </div>
    </div>
</div>

<!-- التحليلات الذكية والتنبيهات -->
<div class="dashboard-grid">
    <!-- التحليلات الذكية بالذكاء الاصطناعي -->
    <div class="dashboard-card ai-insights">
        <div class="card-title">
            <i class="fas fa-brain"></i>
            التحليلات الذكية
        </div>
        <div id="ai-insights-container">
            <div class="insight-item">
                <div class="insight-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                    <i class="fas fa-lightbulb"></i>
                </div>
                <div class="insight-content">
                    <div class="insight-title">توقع زيادة في التجديدات</div>
                    <div class="insight-description">
                        بناءً على تحليل البيانات، من المتوقع زيادة معدل التجديد بنسبة 15% في الشهر القادم
                    </div>
                    <div class="insight-confidence">مستوى الثقة: 87%</div>
                </div>
            </div>
            <div class="insight-item">
                <div class="insight-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="insight-content">
                    <div class="insight-title">عملاء معرضون لخطر الإلغاء</div>
                    <div class="insight-description">
                        تم تحديد 3 عملاء لديهم احتمالية عالية لعدم التجديد. يُنصح بالتواصل معهم
                    </div>
                    <div class="insight-confidence">مستوى الثقة: 92%</div>
                </div>
            </div>
        </div>
    </div>

    <!-- التنبيهات والإشعارات -->
    <div class="dashboard-card">
        <div class="card-title">
            <i class="fas fa-bell"></i>
            التنبيهات الحديثة
        </div>
        <div id="alerts-container">
            <div class="alert-item warning">
                <div class="alert-title">اشتراكات تنتهي قريباً</div>
                <div class="alert-message">5 اشتراكات تنتهي خلال الأسبوع القادم</div>
                <div class="alert-time">منذ ساعتين</div>
            </div>
            <div class="alert-item info">
                <div class="alert-title">دفعة جديدة</div>
                <div class="alert-message">تم استلام دفعة بقيمة $299.99 من أحمد محمد</div>
                <div class="alert-time">منذ 30 دقيقة</div>
            </div>
            <div class="alert-item success">
                <div class="alert-title">تجديد ناجح</div>
                <div class="alert-message">تم تجديد اشتراك فاطمة أحمد بنجاح</div>
                <div class="alert-time">منذ ساعة</div>
            </div>
        </div>
    </div>

    <!-- الإجراءات السريعة -->
    <div class="dashboard-card">
        <div class="card-title">
            <i class="fas fa-bolt"></i>
            الإجراءات السريعة
        </div>
        <div class="quick-actions">
            <a href="{{ url_for('add_subscription') }}" class="quick-action">
                <i class="fas fa-plus action-icon"></i>
                <span class="action-label">اشتراك جديد</span>
            </a>
            <a href="{{ url_for('subscriptions') }}" class="quick-action">
                <i class="fas fa-list action-icon"></i>
                <span class="action-label">عرض الكل</span>
            </a>
            <a href="#" class="quick-action" onclick="generateReport()">
                <i class="fas fa-file-alt action-icon"></i>
                <span class="action-label">تقرير سريع</span>
            </a>
            <a href="{{ url_for('ui_customizer') }}" class="quick-action">
                <i class="fas fa-palette action-icon"></i>
                <span class="action-label">تخصيص الواجهة</span>
            </a>
        </div>
    </div>
</div>

<!-- مؤشرات الأداء في الوقت الفعلي -->
<div class="dashboard-card">
    <div class="card-title">
        <i class="fas fa-tachometer-alt"></i>
        مؤشرات الأداء في الوقت الفعلي
    </div>
    <div class="metric-grid">
        <div class="metric-item">
            <div class="metric-value" id="online-users">{{ stats.online_users or 1 }}</div>
            <div class="metric-label">المستخدمون المتصلون</div>
            <div class="trend-indicator positive">
                <i class="fas fa-arrow-up"></i>
                <span>نشط</span>
            </div>
        </div>
        <div class="metric-item">
            <div class="metric-value" id="system-load">{{ "%.1f"|format(stats.system_load or 2.3) }}%</div>
            <div class="metric-label">حمولة النظام</div>
            <div class="trend-indicator positive">
                <i class="fas fa-check"></i>
                <span>مستقر</span>
            </div>
        </div>
        <div class="metric-item">
            <div class="metric-value" id="response-time">{{ stats.response_time or 120 }}ms</div>
            <div class="metric-label">زمن الاستجابة</div>
            <div class="trend-indicator positive">
                <i class="fas fa-bolt"></i>
                <span>سريع</span>
            </div>
        </div>
        <div class="metric-item">
            <div class="metric-value" id="uptime">99.9%</div>
            <div class="metric-label">وقت التشغيل</div>
            <div class="trend-indicator positive">
                <i class="fas fa-shield-alt"></i>
                <span>ممتاز</span>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
class SmartDashboard {
    constructor() {
        this.charts = {};
        this.updateInterval = 30000; // 30 ثانية
        this.init();
    }
    
    init() {
        this.initCharts();
        this.startRealTimeUpdates();
        this.loadAIInsights();
    }
    
    initCharts() {
        this.initRevenueChart();
        this.initRevenueTrendChart();
        this.initSubscriptionTypesChart();
    }
    
    initRevenueChart() {
        const ctx = document.getElementById('revenue-chart').getContext('2d');
        this.charts.revenue = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'الإيرادات الشهرية',
                    data: [1200, 1900, 3000, 5000, 2000, 3000],
                    borderColor: 'rgba(102, 126, 234, 1)',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        labels: { color: 'white' }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: { color: 'white' },
                        grid: { color: 'rgba(255, 255, 255, 0.1)' }
                    },
                    x: {
                        ticks: { color: 'white' },
                        grid: { color: 'rgba(255, 255, 255, 0.1)' }
                    }
                }
            }
        });
    }
    
    initRevenueTrendChart() {
        const ctx = document.getElementById('revenue-trend-chart').getContext('2d');
        this.charts.revenueTrend = new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['1', '2', '3', '4', '5', '6', '7'],
                datasets: [{
                    data: [100, 120, 110, 140, 160, 150, 180],
                    borderColor: '#10b981',
                    backgroundColor: 'rgba(16, 185, 129, 0.1)',
                    tension: 0.4,
                    fill: true,
                    pointRadius: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: { legend: { display: false } },
                scales: {
                    y: { display: false },
                    x: { display: false }
                }
            }
        });
    }
    
    initSubscriptionTypesChart() {
        const ctx = document.getElementById('subscription-types-chart').getContext('2d');
        this.charts.subscriptionTypes = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['شهري', 'نصف سنوي', 'سنوي'],
                datasets: [{
                    data: [60, 25, 15],
                    backgroundColor: [
                        'rgba(102, 126, 234, 0.8)',
                        'rgba(16, 185, 129, 0.8)',
                        'rgba(245, 158, 11, 0.8)'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: { color: 'white', padding: 20 }
                    }
                }
            }
        });
    }
    
    async startRealTimeUpdates() {
        setInterval(async () => {
            await this.updateStats();
            await this.updateCharts();
            await this.updateAlerts();
        }, this.updateInterval);
    }
    
    async updateStats() {
        try {
            const response = await fetch('/api/dashboard/stats');
            const stats = await response.json();
            
            // تحديث الإحصائيات
            this.animateValue('total-subscriptions', stats.total_subscriptions);
            this.animateValue('monthly-revenue', stats.monthly_revenue, '$');
            this.animateValue('active-subscriptions', stats.active_subscriptions);
            this.animateValue('renewal-rate', stats.renewal_rate, '%');
            
        } catch (error) {
            console.error('خطأ في تحديث الإحصائيات:', error);
        }
    }
    
    animateValue(elementId, newValue, prefix = '') {
        const element = document.getElementById(elementId);
        if (!element) return;
        
        const currentValue = parseFloat(element.textContent.replace(/[^0-9.]/g, '')) || 0;
        const increment = (newValue - currentValue) / 20;
        let current = currentValue;
        
        const timer = setInterval(() => {
            current += increment;
            if ((increment > 0 && current >= newValue) || (increment < 0 && current <= newValue)) {
                current = newValue;
                clearInterval(timer);
            }
            element.textContent = prefix + current.toFixed(prefix === '%' ? 1 : 0);
        }, 50);
    }
    
    async loadAIInsights() {
        try {
            const response = await fetch('/api/ai/insights');
            const insights = await response.json();
            
            const container = document.getElementById('ai-insights-container');
            container.innerHTML = insights.map(insight => `
                <div class="insight-item">
                    <div class="insight-icon" style="background: ${this.getInsightColor(insight.type)};">
                        <i class="fas ${this.getInsightIcon(insight.type)}"></i>
                    </div>
                    <div class="insight-content">
                        <div class="insight-title">${insight.title}</div>
                        <div class="insight-description">${insight.description}</div>
                        <div class="insight-confidence">مستوى الثقة: ${insight.confidence}%</div>
                    </div>
                </div>
            `).join('');
            
        } catch (error) {
            console.error('خطأ في تحميل التحليلات الذكية:', error);
        }
    }
    
    getInsightColor(type) {
        const colors = {
            'success': 'linear-gradient(135deg, #10b981, #059669)',
            'warning': 'linear-gradient(135deg, #f59e0b, #d97706)',
            'info': 'linear-gradient(135deg, #3b82f6, #1d4ed8)',
            'danger': 'linear-gradient(135deg, #ef4444, #dc2626)'
        };
        return colors[type] || colors.info;
    }
    
    getInsightIcon(type) {
        const icons = {
            'success': 'fa-lightbulb',
            'warning': 'fa-exclamation-triangle',
            'info': 'fa-info-circle',
            'danger': 'fa-times-circle'
        };
        return icons[type] || icons.info;
    }
    
    async updateAlerts() {
        try {
            const response = await fetch('/api/dashboard/alerts');
            const alerts = await response.json();
            
            const container = document.getElementById('alerts-container');
            container.innerHTML = alerts.map(alert => `
                <div class="alert-item ${alert.type}">
                    <div class="alert-title">${alert.title}</div>
                    <div class="alert-message">${alert.message}</div>
                    <div class="alert-time">${this.formatTime(alert.created_at)}</div>
                </div>
            `).join('');
            
        } catch (error) {
            console.error('خطأ في تحديث التنبيهات:', error);
        }
    }
    
    formatTime(timestamp) {
        const now = new Date();
        const time = new Date(timestamp);
        const diff = Math.floor((now - time) / 1000);
        
        if (diff < 60) return 'منذ لحظات';
        if (diff < 3600) return `منذ ${Math.floor(diff / 60)} دقيقة`;
        if (diff < 86400) return `منذ ${Math.floor(diff / 3600)} ساعة`;
        return `منذ ${Math.floor(diff / 86400)} يوم`;
    }
}

// دالة لتوليد تقرير سريع
async function generateReport() {
    try {
        const response = await fetch('/api/reports/quick', { method: 'POST' });
        const blob = await response.blob();
        
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `تقرير_سريع_${new Date().toISOString().split('T')[0]}.pdf`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
        
    } catch (error) {
        console.error('خطأ في توليد التقرير:', error);
        alert('حدث خطأ في توليد التقرير');
    }
}

// تهيئة لوحة التحكم الذكية
document.addEventListener('DOMContentLoaded', () => {
    new SmartDashboard();
});
</script>
{% endblock %}
