{% extends "base.html" %}

{% block title %}أنظمة الدفع اليمنية - نظام إدارة الاشتراكات{% endblock %}
{% block page_title %}أنظمة الدفع اليمنية{% endblock %}
{% block page_description %}إدارة بوابات الدفع المحلية والمعاملات المالية{% endblock %}

{% block extra_css %}
<style>
.payment-gateways-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.gateway-card {
    background: var(--glass-bg-light);
    backdrop-filter: blur(var(--blur-amount));
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    transition: all var(--transition-medium);
    position: relative;
    overflow: hidden;
}

.gateway-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-heavy);
    border-color: var(--glass-border-light);
}

.gateway-header {
    display: flex;
    align-items: center;
    justify-content: between;
    margin-bottom: 1.5rem;
}

.gateway-logo {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
    font-size: 1.5rem;
    color: white;
}

.gateway-info {
    flex: 1;
}

.gateway-name {
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.gateway-name-ar {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
}

.gateway-status {
    position: absolute;
    top: 1rem;
    left: 1rem;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--success-color);
    box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
}

.gateway-status.inactive {
    background: var(--error-color);
    box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
}

.gateway-details {
    margin-bottom: 1.5rem;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
}

.detail-value {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.875rem;
}

.gateway-actions {
    display: flex;
    gap: 0.75rem;
}

.gateway-btn {
    flex: 1;
    padding: 0.75rem;
    border: none;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-medium);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.btn-configure {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.btn-configure:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-colored);
}

.btn-toggle {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border: 1px solid var(--glass-border);
}

.btn-toggle:hover {
    background: rgba(255, 255, 255, 0.2);
}

.btn-toggle.active {
    background: var(--success-color);
    color: white;
}

.btn-toggle.inactive {
    background: var(--error-color);
    color: white;
}

.transactions-section {
    margin-top: 2rem;
}

.transactions-table {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table-header {
    background: rgba(255, 255, 255, 0.1);
    padding: 1rem;
    border-bottom: 1px solid var(--glass-border);
}

.table-title {
    color: var(--text-primary);
    font-size: 1.125rem;
    font-weight: 700;
    margin: 0;
}

.table-content {
    max-height: 400px;
    overflow-y: auto;
}

.transaction-row {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr auto;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
    transition: background var(--transition-fast);
}

.transaction-row:hover {
    background: rgba(255, 255, 255, 0.05);
}

.transaction-row:last-child {
    border-bottom: none;
}

.transaction-cell {
    display: flex;
    flex-direction: column;
}

.cell-label {
    color: var(--text-muted);
    font-size: 0.75rem;
    margin-bottom: 0.25rem;
}

.cell-value {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.875rem;
}

.status-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
    text-align: center;
}

.status-pending {
    background: rgba(245, 158, 11, 0.2);
    color: var(--warning-color);
}

.status-completed {
    background: rgba(16, 185, 129, 0.2);
    color: var(--success-color);
}

.status-failed {
    background: rgba(239, 68, 68, 0.2);
    color: var(--error-color);
}

.payment-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    transition: all var(--transition-medium);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.25rem;
    color: white;
}

.stat-value {
    font-size: 1.75rem;
    font-weight: bold;
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
}

.add-gateway-btn {
    position: fixed;
    bottom: 2rem;
    left: 2rem;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    box-shadow: var(--shadow-heavy);
    transition: all var(--transition-medium);
    z-index: 1000;
}

.add-gateway-btn:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-colored);
}

@media (max-width: 768px) {
    .payment-gateways-container {
        grid-template-columns: 1fr;
    }
    
    .payment-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .transaction-row {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }
    
    .gateway-actions {
        flex-direction: column;
    }
}
</style>
{% endblock %}

{% block content %}
<!-- إحصائيات الدفع -->
<div class="payment-stats">
    <div class="stat-card">
        <div class="stat-icon" style="background: linear-gradient(135deg, var(--success-color), #059669);">
            <i class="fas fa-credit-card"></i>
        </div>
        <div class="stat-value">{{ total_transactions or 0 }}</div>
        <div class="stat-label">إجمالي المعاملات</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));">
            <i class="fas fa-money-bill-wave"></i>
        </div>
        <div class="stat-value">{{ '{:,.0f}'.format(total_amount or 0) }} ر.ي</div>
        <div class="stat-label">إجمالي المبالغ</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon" style="background: linear-gradient(135deg, var(--warning-color), #d97706);">
            <i class="fas fa-percentage"></i>
        </div>
        <div class="stat-value">{{ '{:.1f}%'.format(success_rate or 0) }}</div>
        <div class="stat-label">معدل النجاح</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-icon" style="background: linear-gradient(135deg, var(--info-color), #1d4ed8);">
            <i class="fas fa-university"></i>
        </div>
        <div class="stat-value">{{ active_gateways or 0 }}</div>
        <div class="stat-label">البوابات النشطة</div>
    </div>
</div>

<!-- بوابات الدفع -->
<div class="payment-gateways-container">
    {% for gateway in gateways %}
    <div class="gateway-card">
        <div class="gateway-status {% if gateway.is_active %}active{% else %}inactive{% endif %}"></div>
        
        <div class="gateway-header">
            <div class="gateway-logo">
                {% if gateway.type == 'wallet' %}
                    <i class="fas fa-wallet"></i>
                {% elif gateway.type == 'bank' %}
                    <i class="fas fa-university"></i>
                {% else %}
                    <i class="fas fa-mobile-alt"></i>
                {% endif %}
            </div>
            <div class="gateway-info">
                <div class="gateway-name">{{ gateway.name }}</div>
                <div class="gateway-name-ar">{{ gateway.name_ar }}</div>
            </div>
        </div>
        
        <div class="gateway-details">
            <div class="detail-row">
                <span class="detail-label">الرسوم:</span>
                <span class="detail-value">
                    {% if gateway.fees_percentage > 0 %}{{ gateway.fees_percentage }}%{% endif %}
                    {% if gateway.fees_fixed > 0 %}
                        {% if gateway.fees_percentage > 0 %} + {% endif %}
                        {{ gateway.fees_fixed }} ر.ي
                    {% endif %}
                    {% if gateway.fees_percentage == 0 and gateway.fees_fixed == 0 %}مجاني{% endif %}
                </span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">الحد الأدنى:</span>
                <span class="detail-value">{{ '{:,.0f}'.format(gateway.min_amount) }} ر.ي</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">الحد الأقصى:</span>
                <span class="detail-value">{{ '{:,.0f}'.format(gateway.max_amount) }} ر.ي</span>
            </div>
            
            <div class="detail-row">
                <span class="detail-label">المعاملات اليوم:</span>
                <span class="detail-value">{{ gateway.transactions|length or 0 }}</span>
            </div>
        </div>
        
        <div class="gateway-actions">
            <button class="gateway-btn btn-configure" onclick="configureGateway({{ gateway.id }})">
                <i class="fas fa-cog"></i>
                إعدادات
            </button>
            <button class="gateway-btn btn-toggle {% if gateway.is_active %}active{% else %}inactive{% endif %}" 
                    onclick="toggleGateway({{ gateway.id }}, {{ gateway.is_active|lower }})">
                <i class="fas fa-{% if gateway.is_active %}pause{% else %}play{% endif %}"></i>
                {% if gateway.is_active %}إيقاف{% else %}تفعيل{% endif %}
            </button>
        </div>
    </div>
    {% endfor %}
</div>

<!-- المعاملات الحديثة -->
<div class="transactions-section">
    <div class="transactions-table">
        <div class="table-header">
            <h3 class="table-title">المعاملات الحديثة</h3>
        </div>
        <div class="table-content">
            {% for transaction in recent_transactions %}
            <div class="transaction-row">
                <div class="transaction-cell">
                    <span class="cell-label">رقم المعاملة</span>
                    <span class="cell-value">{{ transaction.transaction_id }}</span>
                </div>
                <div class="transaction-cell">
                    <span class="cell-label">البوابة</span>
                    <span class="cell-value">{{ transaction.gateway.name_ar }}</span>
                </div>
                <div class="transaction-cell">
                    <span class="cell-label">المبلغ</span>
                    <span class="cell-value">{{ '{:,.0f}'.format(transaction.amount) }} ر.ي</span>
                </div>
                <div class="transaction-cell">
                    <span class="cell-label">العميل</span>
                    <span class="cell-value">{{ transaction.customer_name or 'غير محدد' }}</span>
                </div>
                <div class="transaction-cell">
                    <span class="cell-label">التاريخ</span>
                    <span class="cell-value">{{ transaction.created_at.strftime('%Y-%m-%d %H:%M') }}</span>
                </div>
                <div class="transaction-cell">
                    <span class="cell-label">الحالة</span>
                    <span class="status-badge status-{{ transaction.status }}">
                        {% if transaction.status == 'pending' %}معلق
                        {% elif transaction.status == 'completed' %}مكتمل
                        {% elif transaction.status == 'failed' %}فاشل
                        {% else %}{{ transaction.status }}
                        {% endif %}
                    </span>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<!-- زر إضافة بوابة جديدة -->
<button class="add-gateway-btn" onclick="addNewGateway()" title="إضافة بوابة دفع جديدة">
    <i class="fas fa-plus"></i>
</button>
{% endblock %}

{% block extra_js %}
<script>
// إدارة بوابات الدفع
function configureGateway(gatewayId) {
    // فتح نافذة إعدادات البوابة
    window.location.href = `/payment-gateways/${gatewayId}/configure`;
}

function toggleGateway(gatewayId, isActive) {
    const action = isActive ? 'deactivate' : 'activate';
    const message = isActive ? 'إيقاف' : 'تفعيل';
    
    if (confirm(`هل تريد ${message} هذه البوابة؟`)) {
        fetch(`/api/payment-gateways/${gatewayId}/toggle`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ action: action })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`تم ${message} البوابة بنجاح`, 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showNotification(`فشل في ${message} البوابة`, 'error');
            }
        })
        .catch(error => {
            showNotification('خطأ في الاتصال بالخادم', 'error');
        });
    }
}

function addNewGateway() {
    window.location.href = '/payment-gateways/add';
}

// تحديث الإحصائيات كل 30 ثانية
setInterval(() => {
    fetch('/api/payment-stats')
        .then(response => response.json())
        .then(data => {
            // تحديث الإحصائيات
            updateStats(data);
        })
        .catch(error => console.error('خطأ في تحديث الإحصائيات:', error));
}, 30000);

function updateStats(data) {
    // تحديث قيم الإحصائيات
    document.querySelector('.stat-value').textContent = data.total_transactions || 0;
    // يمكن إضافة المزيد من التحديثات هنا
}
</script>
{% endblock %}
