{% extends "base.html" %}

{% block title %}محرر الواجهات التفاعلي - نظام إدارة الاشتراكات{% endblock %}
{% block page_title %}محرر الواجهات التفاعلي{% endblock %}
{% block page_description %}خصص واجهة النظام حسب تفضيلاتك{% endblock %}

{% block extra_css %}
<style>
.customizer-container {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 2rem;
    height: calc(100vh - 200px);
}

.customizer-panel {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 1.5rem;
    overflow-y: auto;
}

.preview-panel {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 1.5rem;
    overflow: hidden;
    position: relative;
}

.customizer-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.customizer-section:last-child {
    border-bottom: none;
}

.section-title {
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.control-group {
    margin-bottom: 1rem;
}

.control-label {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.875rem;
    margin-bottom: 0.5rem;
    display: block;
}

.control-input {
    width: 100%;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    padding: 0.5rem;
    color: white;
    font-size: 0.875rem;
}

.control-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.color-picker {
    width: 100%;
    height: 40px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
}

.slider-container {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.slider {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: rgba(255, 255, 255, 0.2);
    outline: none;
    cursor: pointer;
}

.slider-value {
    color: white;
    font-size: 0.875rem;
    min-width: 40px;
    text-align: center;
}

.theme-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
}

.theme-option {
    aspect-ratio: 2/1;
    border-radius: 8px;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.theme-option.active {
    border-color: #667eea;
    transform: scale(1.05);
}

.theme-option::after {
    content: '';
    position: absolute;
    inset: 0;
    background: inherit;
    opacity: 0.8;
}

.theme-default { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
.theme-dark { background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%); }
.theme-nature { background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%); }
.theme-sunset { background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%); }
.theme-ocean { background: linear-gradient(135deg, #2193b0 0%, #6dd5ed 100%); }
.theme-purple { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }

.layout-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 0.5rem;
}

.layout-option {
    aspect-ratio: 3/2;
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.layout-option.active {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.2);
}

.layout-preview {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding: 2px;
}

.layout-header {
    height: 20%;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;
    margin-bottom: 2px;
}

.layout-body {
    flex: 1;
    display: flex;
    gap: 2px;
}

.layout-sidebar {
    width: 30%;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 2px;
}

.layout-content {
    flex: 1;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn-apply {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn-apply:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(16, 185, 129, 0.3);
}

.btn-reset {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    padding: 0.75rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-reset:hover {
    background: rgba(255, 255, 255, 0.2);
}

.preview-frame {
    width: 100%;
    height: 100%;
    border: none;
    border-radius: 12px;
    background: white;
}

.component-showcase {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.component-demo {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 1rem;
    text-align: center;
}

.demo-card {
    background: var(--card-bg, rgba(255, 255, 255, 0.1));
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.demo-button {
    background: var(--primary-color, #667eea);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    margin: 0.25rem;
}

.demo-badge {
    background: var(--accent-color, #10b981);
    color: white;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
    margin: 0.25rem;
}

@media (max-width: 768px) {
    .customizer-container {
        grid-template-columns: 1fr;
        height: auto;
    }
    
    .theme-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .layout-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}
</style>
{% endblock %}

{% block content %}
<div class="customizer-container">
    <!-- لوحة التحكم -->
    <div class="customizer-panel">
        <!-- الثيمات -->
        <div class="customizer-section">
            <h3 class="section-title">
                <i class="fas fa-palette"></i>
                الثيمات
            </h3>
            <div class="theme-grid">
                <div class="theme-option theme-default active" data-theme="default" title="افتراضي"></div>
                <div class="theme-option theme-dark" data-theme="dark" title="داكن"></div>
                <div class="theme-option theme-nature" data-theme="nature" title="طبيعي"></div>
                <div class="theme-option theme-sunset" data-theme="sunset" title="غروب"></div>
                <div class="theme-option theme-ocean" data-theme="ocean" title="محيط"></div>
                <div class="theme-option theme-purple" data-theme="purple" title="بنفسجي"></div>
            </div>
        </div>

        <!-- الألوان المخصصة -->
        <div class="customizer-section">
            <h3 class="section-title">
                <i class="fas fa-paint-brush"></i>
                الألوان المخصصة
            </h3>
            <div class="control-group">
                <label class="control-label">اللون الأساسي</label>
                <input type="color" class="color-picker" id="primary-color" value="#667eea">
            </div>
            <div class="control-group">
                <label class="control-label">اللون الثانوي</label>
                <input type="color" class="color-picker" id="secondary-color" value="#764ba2">
            </div>
            <div class="control-group">
                <label class="control-label">لون التمييز</label>
                <input type="color" class="color-picker" id="accent-color" value="#10b981">
            </div>
        </div>

        <!-- التخطيط -->
        <div class="customizer-section">
            <h3 class="section-title">
                <i class="fas fa-th-large"></i>
                تخطيط الصفحة
            </h3>
            <div class="layout-grid">
                <div class="layout-option active" data-layout="default">
                    <div class="layout-preview">
                        <div class="layout-header"></div>
                        <div class="layout-body">
                            <div class="layout-sidebar"></div>
                            <div class="layout-content"></div>
                        </div>
                    </div>
                </div>
                <div class="layout-option" data-layout="full-width">
                    <div class="layout-preview">
                        <div class="layout-header"></div>
                        <div class="layout-body">
                            <div class="layout-content"></div>
                        </div>
                    </div>
                </div>
                <div class="layout-option" data-layout="centered">
                    <div class="layout-preview">
                        <div class="layout-header"></div>
                        <div class="layout-body" style="justify-content: center;">
                            <div class="layout-content" style="width: 60%;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- التأثيرات -->
        <div class="customizer-section">
            <h3 class="section-title">
                <i class="fas fa-magic"></i>
                التأثيرات
            </h3>
            <div class="control-group">
                <label class="control-label">شفافية الخلفية</label>
                <div class="slider-container">
                    <input type="range" class="slider" id="bg-opacity" min="0" max="100" value="10">
                    <span class="slider-value" id="bg-opacity-value">10%</span>
                </div>
            </div>
            <div class="control-group">
                <label class="control-label">قوة الضبابية</label>
                <div class="slider-container">
                    <input type="range" class="slider" id="blur-strength" min="0" max="50" value="20">
                    <span class="slider-value" id="blur-strength-value">20px</span>
                </div>
            </div>
            <div class="control-group">
                <label class="control-label">نصف قطر الحواف</label>
                <div class="slider-container">
                    <input type="range" class="slider" id="border-radius" min="0" max="30" value="20">
                    <span class="slider-value" id="border-radius-value">20px</span>
                </div>
            </div>
        </div>

        <!-- الخطوط -->
        <div class="customizer-section">
            <h3 class="section-title">
                <i class="fas fa-font"></i>
                الخطوط
            </h3>
            <div class="control-group">
                <label class="control-label">عائلة الخط</label>
                <select class="control-input" id="font-family">
                    <option value="'Segoe UI', Tahoma, Geneva, Verdana, sans-serif">Segoe UI</option>
                    <option value="'Arial', sans-serif">Arial</option>
                    <option value="'Helvetica', sans-serif">Helvetica</option>
                    <option value="'Times New Roman', serif">Times New Roman</option>
                    <option value="'Georgia', serif">Georgia</option>
                    <option value="'Courier New', monospace">Courier New</option>
                </select>
            </div>
            <div class="control-group">
                <label class="control-label">حجم الخط الأساسي</label>
                <div class="slider-container">
                    <input type="range" class="slider" id="font-size" min="12" max="20" value="14">
                    <span class="slider-value" id="font-size-value">14px</span>
                </div>
            </div>
        </div>

        <!-- أزرار التحكم -->
        <div class="action-buttons">
            <button class="btn-apply" onclick="applyCustomizations()">
                <i class="fas fa-check ml-2"></i>
                تطبيق التغييرات
            </button>
            <button class="btn-reset" onclick="resetToDefault()">
                <i class="fas fa-undo ml-2"></i>
                إعادة تعيين
            </button>
        </div>
    </div>

    <!-- لوحة المعاينة -->
    <div class="preview-panel">
        <h3 style="color: white; margin-bottom: 1rem;">
            <i class="fas fa-eye ml-2"></i>
            معاينة مباشرة
        </h3>
        
        <div class="component-showcase">
            <div class="component-demo">
                <h4 style="color: white; margin-bottom: 0.5rem;">البطاقات</h4>
                <div class="demo-card">
                    <p style="color: white; margin: 0;">بطاقة تجريبية</p>
                </div>
            </div>
            
            <div class="component-demo">
                <h4 style="color: white; margin-bottom: 0.5rem;">الأزرار</h4>
                <button class="demo-button">زر أساسي</button>
                <button class="demo-button" style="background: var(--secondary-color, #764ba2);">زر ثانوي</button>
            </div>
            
            <div class="component-demo">
                <h4 style="color: white; margin-bottom: 0.5rem;">الشارات</h4>
                <span class="demo-badge">نشط</span>
                <span class="demo-badge" style="background: var(--secondary-color, #764ba2);">معلق</span>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
class UICustomizer {
    constructor() {
        this.currentTheme = 'default';
        this.customizations = this.loadCustomizations();
        this.init();
    }
    
    init() {
        this.initThemeSelector();
        this.initColorPickers();
        this.initSliders();
        this.initLayoutSelector();
        this.initFontSelector();
        this.applyStoredCustomizations();
    }
    
    initThemeSelector() {
        document.querySelectorAll('.theme-option').forEach(option => {
            option.addEventListener('click', (e) => {
                document.querySelectorAll('.theme-option').forEach(opt => opt.classList.remove('active'));
                e.target.classList.add('active');
                this.currentTheme = e.target.dataset.theme;
                this.applyTheme(this.currentTheme);
            });
        });
    }
    
    initColorPickers() {
        ['primary-color', 'secondary-color', 'accent-color'].forEach(id => {
            const picker = document.getElementById(id);
            picker.addEventListener('change', (e) => {
                this.updateCustomProperty(`--${id.replace('-', '-')}`, e.target.value);
                this.updatePreview();
            });
        });
    }
    
    initSliders() {
        const sliders = [
            { id: 'bg-opacity', property: '--bg-opacity', suffix: '%' },
            { id: 'blur-strength', property: '--blur-strength', suffix: 'px' },
            { id: 'border-radius', property: '--border-radius', suffix: 'px' },
            { id: 'font-size', property: '--font-size', suffix: 'px' }
        ];
        
        sliders.forEach(slider => {
            const input = document.getElementById(slider.id);
            const valueDisplay = document.getElementById(`${slider.id}-value`);
            
            input.addEventListener('input', (e) => {
                const value = e.target.value;
                valueDisplay.textContent = value + slider.suffix;
                this.updateCustomProperty(slider.property, value + slider.suffix);
                this.updatePreview();
            });
        });
    }
    
    initLayoutSelector() {
        document.querySelectorAll('.layout-option').forEach(option => {
            option.addEventListener('click', (e) => {
                document.querySelectorAll('.layout-option').forEach(opt => opt.classList.remove('active'));
                e.currentTarget.classList.add('active');
                const layout = e.currentTarget.dataset.layout;
                this.applyLayout(layout);
            });
        });
    }
    
    initFontSelector() {
        const fontSelect = document.getElementById('font-family');
        fontSelect.addEventListener('change', (e) => {
            this.updateCustomProperty('--font-family', e.target.value);
            this.updatePreview();
        });
    }
    
    applyTheme(theme) {
        const themes = {
            default: { primary: '#667eea', secondary: '#764ba2' },
            dark: { primary: '#1a1a2e', secondary: '#16213e' },
            nature: { primary: '#11998e', secondary: '#38ef7d' },
            sunset: { primary: '#ff6b6b', secondary: '#feca57' },
            ocean: { primary: '#2193b0', secondary: '#6dd5ed' },
            purple: { primary: '#667eea', secondary: '#764ba2' }
        };
        
        if (themes[theme]) {
            document.getElementById('primary-color').value = themes[theme].primary;
            document.getElementById('secondary-color').value = themes[theme].secondary;
            this.updateCustomProperty('--primary-color', themes[theme].primary);
            this.updateCustomProperty('--secondary-color', themes[theme].secondary);
            this.updatePreview();
        }
    }
    
    applyLayout(layout) {
        document.body.className = document.body.className.replace(/layout-\w+/g, '');
        document.body.classList.add(`layout-${layout}`);
        this.customizations.layout = layout;
        this.saveCustomizations();
    }
    
    updateCustomProperty(property, value) {
        document.documentElement.style.setProperty(property, value);
        this.customizations[property] = value;
    }
    
    updatePreview() {
        // تحديث المعاينة المباشرة
        const demoCards = document.querySelectorAll('.demo-card');
        const demoButtons = document.querySelectorAll('.demo-button');
        const demoBadges = document.querySelectorAll('.demo-badge');
        
        demoCards.forEach(card => {
            card.style.background = `var(--card-bg, rgba(255, 255, 255, ${document.getElementById('bg-opacity').value / 100}))`;
            card.style.borderRadius = `var(--border-radius, ${document.getElementById('border-radius').value}px)`;
        });
        
        demoButtons.forEach(button => {
            button.style.borderRadius = `calc(var(--border-radius, ${document.getElementById('border-radius').value}px) / 2)`;
        });
    }
    
    applyCustomizations() {
        // حفظ التخصيصات
        this.saveCustomizations();
        
        // تطبيق على النظام
        Object.keys(this.customizations).forEach(property => {
            if (property.startsWith('--')) {
                document.documentElement.style.setProperty(property, this.customizations[property]);
            }
        });
        
        // إشعار بالنجاح
        this.showNotification('تم تطبيق التخصيصات بنجاح!', 'success');
        
        // إرسال للخادم
        this.saveToServer();
    }
    
    resetToDefault() {
        // إعادة تعيين القيم الافتراضية
        document.getElementById('primary-color').value = '#667eea';
        document.getElementById('secondary-color').value = '#764ba2';
        document.getElementById('accent-color').value = '#10b981';
        document.getElementById('bg-opacity').value = 10;
        document.getElementById('blur-strength').value = 20;
        document.getElementById('border-radius').value = 20;
        document.getElementById('font-size').value = 14;
        document.getElementById('font-family').value = "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
        
        // تحديث العرض
        document.getElementById('bg-opacity-value').textContent = '10%';
        document.getElementById('blur-strength-value').textContent = '20px';
        document.getElementById('border-radius-value').textContent = '20px';
        document.getElementById('font-size-value').textContent = '14px';
        
        // إعادة تعيين الثيم
        document.querySelectorAll('.theme-option').forEach(opt => opt.classList.remove('active'));
        document.querySelector('.theme-option[data-theme="default"]').classList.add('active');
        
        // تطبيق الإعدادات الافتراضية
        this.applyTheme('default');
        this.customizations = {};
        this.saveCustomizations();
        
        this.showNotification('تم إعادة تعيين التخصيصات!', 'info');
    }
    
    saveCustomizations() {
        localStorage.setItem('ui_customizations', JSON.stringify(this.customizations));
    }
    
    loadCustomizations() {
        const stored = localStorage.getItem('ui_customizations');
        return stored ? JSON.parse(stored) : {};
    }
    
    applyStoredCustomizations() {
        Object.keys(this.customizations).forEach(property => {
            if (property.startsWith('--')) {
                document.documentElement.style.setProperty(property, this.customizations[property]);
            }
        });
    }
    
    async saveToServer() {
        try {
            const response = await fetch('/api/user/preferences', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    ui_customizations: this.customizations
                })
            });
            
            if (response.ok) {
                console.log('تم حفظ التخصيصات على الخادم');
            }
        } catch (error) {
            console.error('خطأ في حفظ التخصيصات:', error);
        }
    }
    
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.innerHTML = `
            <div class="notification-content">
                <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle"></i>
                <span>${message}</span>
            </div>
        `;
        
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#10b981' : type === 'error' ? '#ef4444' : '#3b82f6'};
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            z-index: 10000;
            animation: slideIn 0.3s ease-out;
        `;
        
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }
}

// تهيئة محرر الواجهات
document.addEventListener('DOMContentLoaded', () => {
    new UICustomizer();
});

// إضافة أنيميشن CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    
    @keyframes slideOut {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
`;
document.head.appendChild(style);
</script>
{% endblock %}
