{% extends "base.html" %}

{% block title %}تسجيل الدخول - نظام إدارة الاشتراكات{% endblock %}

{% block full_content %}
<style>
    :root {
        --primary-color: #4f46e5;
        --secondary-color: #7c3aed;
        --accent-color: #06b6d4;
        --success-color: #10b981;
        --warning-color: #f59e0b;
        --error-color: #ef4444;
        --text-primary: #1f2937;
        --text-secondary: #6b7280;
        --text-muted: #9ca3af;
        --glass-bg: rgba(255, 255, 255, 0.12);
        --glass-border: rgba(255, 255, 255, 0.2);
        --shadow-light: 0 4px 16px rgba(0, 0, 0, 0.1);
        --shadow-medium: 0 8px 32px rgba(0, 0, 0, 0.15);
        --shadow-heavy: 0 16px 48px rgba(0, 0, 0, 0.2);
        --blur-amount: 24px;
        --border-radius: 20px;
        --transition-fast: 0.15s ease;
        --transition-medium: 0.25s ease;
        --transition-slow: 0.4s ease;
    }

    body {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 50%, var(--accent-color) 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    body::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background:
            radial-gradient(circle at 25% 25%, rgba(79, 70, 229, 0.2) 0%, transparent 50%),
            radial-gradient(circle at 75% 75%, rgba(124, 58, 237, 0.2) 0%, transparent 50%),
            radial-gradient(circle at 50% 50%, rgba(6, 182, 212, 0.15) 0%, transparent 50%);
        animation: gradientShift 12s ease-in-out infinite;
    }

    @keyframes gradientShift {
        0%, 100% {
            transform: scale(1) rotate(0deg);
            opacity: 0.8;
        }
        50% {
            transform: scale(1.1) rotate(180deg);
            opacity: 0.6;
        }
    }

    .login-container {
        background: var(--glass-bg);
        backdrop-filter: blur(var(--blur-amount));
        border-radius: var(--border-radius);
        box-shadow: var(--shadow-medium);
        border: 1px solid var(--glass-border);
        transition: all var(--transition-medium);
        position: relative;
        z-index: 10;
    }

    .login-container:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-heavy);
    }

    .floating-shapes {
        position: absolute;
        width: 100%;
        height: 100%;
        overflow: hidden;
        z-index: 0;
    }

    .shape {
        position: absolute;
        background: rgba(255, 255, 255, 0.08);
        border-radius: 50%;
        animation: float 8s ease-in-out infinite;
        backdrop-filter: blur(4px);
    }

    .shape:nth-child(1) {
        width: 60px;
        height: 60px;
        top: 15%;
        left: 8%;
        animation-delay: 0s;
    }
    .shape:nth-child(2) {
        width: 90px;
        height: 90px;
        top: 65%;
        right: 12%;
        animation-delay: 2s;
    }
    .shape:nth-child(3) {
        width: 45px;
        height: 45px;
        bottom: 25%;
        left: 25%;
        animation-delay: 4s;
    }
    .shape:nth-child(4) {
        width: 30px;
        height: 30px;
        top: 8%;
        right: 35%;
        animation-delay: 1s;
    }
    .shape:nth-child(5) {
        width: 75px;
        height: 75px;
        bottom: 8%;
        right: 25%;
        animation-delay: 3s;
    }

    @keyframes float {
        0%, 100% {
            transform: translateY(0px) rotate(0deg) scale(1);
            opacity: 0.6;
        }
        50% {
            transform: translateY(-15px) rotate(180deg) scale(1.1);
            opacity: 0.8;
        }
    }

    .input-group {
        position: relative;
        margin-bottom: 1.5rem;
    }

    .input-group label {
        display: block;
        color: var(--text-primary);
        margin-bottom: 0.5rem;
        font-weight: 600;
        font-size: 0.875rem;
    }

    .input-field {
        width: 100%;
        padding: 0.875rem 1rem 0.875rem 3rem;
        background: rgba(255, 255, 255, 0.9);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 12px;
        color: var(--text-primary);
        font-size: 0.95rem;
        transition: all var(--transition-fast);
        font-weight: 500;
    }

    .input-field:focus {
        outline: none;
        border-color: var(--primary-color);
        background: rgba(255, 255, 255, 0.95);
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
        transform: translateY(-1px);
    }

    .input-field::placeholder {
        color: var(--text-muted);
        font-weight: 400;
    }

    .input-icon {
        position: absolute;
        left: 1rem;
        top: 2.75rem;
        color: var(--primary-color);
        z-index: 10;
        transition: color var(--transition-fast);
    }

    .input-field:focus + .input-icon {
        color: var(--secondary-color);
    }

    .logo-container {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        transition: all var(--transition-medium);
    }

    .logo-container:hover {
        transform: scale(1.1) rotate(5deg);
    }

    .btn-primary {
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border: none;
        border-radius: 12px;
        font-weight: 600;
        transition: all var(--transition-medium);
        position: relative;
        overflow: hidden;
        letter-spacing: 0.025em;
    }

    .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 24px rgba(79, 70, 229, 0.3);
    }

    .btn-primary::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left var(--transition-slow);
    }

    .btn-primary:hover::before {
        left: 100%;
    }

    .alert {
        padding: 0.875rem;
        border-radius: 10px;
        margin-bottom: 1rem;
        font-size: 0.875rem;
        font-weight: 500;
        border: 1px solid;
    }

    .alert-error {
        background: rgba(239, 68, 68, 0.1);
        border-color: rgba(239, 68, 68, 0.2);
        color: #dc2626;
    }

    .alert-success {
        background: rgba(16, 185, 129, 0.1);
        border-color: rgba(16, 185, 129, 0.2);
        color: #059669;
    }

    /* تحسينات الأداء */
    .input-field,
    .btn-primary,
    .login-container {
        will-change: transform;
    }

    /* تحسين إمكانية الوصول */
    @media (prefers-reduced-motion: reduce) {
        * {
            animation-duration: 0.01ms !important;
            animation-iteration-count: 1 !important;
            transition-duration: 0.01ms !important;
        }
    }

    /* تحسين التباين للقراءة */
    @media (prefers-contrast: high) {
        :root {
            --glass-bg: rgba(255, 255, 255, 0.2);
            --glass-border: rgba(255, 255, 255, 0.4);
            --text-primary: #000000;
            --text-secondary: #374151;
            --text-muted: #6b7280;
        }

        .input-field {
            background: rgba(255, 255, 255, 0.95);
        }
    }

    /* استجابة للشاشات الصغيرة */
    @media (max-width: 480px) {
        .login-container {
            margin: 1rem;
            padding: 1.5rem;
        }

        .shape {
            display: none;
        }
    }
</style>

<!-- Floating Shapes Background -->
<div class="floating-shapes">
    <div class="shape"></div>
    <div class="shape"></div>
    <div class="shape"></div>
    <div class="shape"></div>
    <div class="shape"></div>
</div>

<!-- Login Container -->
<div class="login-container w-full max-w-md p-8 relative z-10">
    <!-- Logo and Title -->
    <div class="text-center mb-8">
        <div class="mb-4">
            <i class="fas fa-cloud-upload-alt text-5xl logo-container"></i>
        </div>
        <h1 class="text-3xl font-bold text-gray-800 mb-2">نظام إدارة الاشتراكات</h1>
        <p class="text-gray-600">مرحباً بك! يرجى تسجيل الدخول للمتابعة</p>
    </div>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
        {% if messages %}
            {% for category, message in messages %}
                <div class="alert alert-{{ 'error' if category == 'error' else category }} mb-4">
                    <i class="fas fa-{{ 'exclamation-circle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} ml-2"></i>
                    {{ message }}
                </div>
            {% endfor %}
        {% endif %}
    {% endwith %}

    <!-- Login Form -->
    <form method="POST" class="space-y-6">
        <!-- Username Field -->
        <div class="input-group">
            <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-user ml-2"></i>
                اسم المستخدم
            </label>
            <input
                type="text"
                id="username"
                name="username"
                class="input-field pr-12"
                placeholder="أدخل اسم المستخدم"
                required
                autocomplete="username"
                autofocus
                value="{{ request.form.username or '' }}"
            >
            <i class="fas fa-user input-icon"></i>
        </div>

        <!-- Password Field -->
        <div class="input-group">
            <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
                <i class="fas fa-lock ml-2"></i>
                كلمة المرور
            </label>
            <input
                type="password"
                id="password"
                name="password"
                class="input-field pr-12 pl-12"
                placeholder="أدخل كلمة المرور"
                required
                autocomplete="current-password"
            >
            <i class="fas fa-lock input-icon"></i>
            <button type="button" id="togglePassword" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-200">
                <i class="fas fa-eye" id="password-toggle-icon"></i>
            </button>
        </div>

        <!-- Remember Me -->
        <div class="flex items-center justify-between">
            <label class="flex items-center">
                <input type="checkbox" name="remember" class="ml-2 accent-blue-600" id="remember">
                <span class="text-sm text-gray-600">تذكرني</span>
            </label>
            <a href="{{ url_for('index') }}" class="text-sm text-gray-600 hover:text-blue-600 transition-colors">
                العودة للرئيسية
            </a>
        </div>

        <!-- Login Button -->
        <button type="submit" class="btn-primary w-full">
            <i class="fas fa-sign-in-alt ml-2"></i>
            تسجيل الدخول
        </button>
    </form>

    <!-- Demo Credentials -->
    <div class="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
        <h4 class="text-sm font-medium text-blue-800 mb-2">
            <i class="fas fa-info-circle ml-2"></i>
            بيانات تجريبية:
        </h4>
        <p class="text-xs text-blue-600">اسم المستخدم: admin</p>
        <p class="text-xs text-blue-600">كلمة المرور: 123456</p>
    </div>

    <!-- Footer -->
    <div class="text-center mt-8 text-sm text-gray-500">
        <p>&copy; 2024 نظام إدارة الاشتراكات</p>
        <p>جميع الحقوق محفوظة</p>
    </div>
</div>

<script>
    // Toggle Password Visibility
    document.getElementById('togglePassword').addEventListener('click', function() {
        const passwordField = document.getElementById('password');
        const icon = this.querySelector('i');
        
        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            icon.classList.remove('fa-eye');
            icon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            icon.classList.remove('fa-eye-slash');
            icon.classList.add('fa-eye');
        }
    });

    // Auto-fill demo credentials
    document.addEventListener('DOMContentLoaded', function() {
        const demoInfo = document.querySelector('.bg-blue-50');
        if (demoInfo) {
            demoInfo.addEventListener('click', function() {
                document.getElementById('username').value = 'admin';
                document.getElementById('password').value = '123456';
            });
        }
    });

    // Form validation محسن
    document.querySelector('form').addEventListener('submit', function(e) {
        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value.trim();
        const submitBtn = this.querySelector('button[type="submit"]');

        if (!username || !password) {
            e.preventDefault();
            showLoginError('يرجى إدخال اسم المستخدم وكلمة المرور');
            return;
        }

        // تأثير التحميل
        if (submitBtn) {
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i> جاري تسجيل الدخول...';
            submitBtn.disabled = true;

            // إعادة تعيين الزر في حالة فشل التسجيل
            setTimeout(() => {
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }, 8000);
        }
    });

    // تحسين تجربة المستخدم
    document.addEventListener('DOMContentLoaded', function() {
        // تأثير ظهور محسن للحاوية
        const container = document.querySelector('.login-container');
        const shapes = document.querySelectorAll('.shape');

        if (container) {
            container.style.opacity = '0';
            container.style.transform = 'translateY(30px) scale(0.95)';

            setTimeout(() => {
                container.style.transition = 'all 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0) scale(1)';
            }, 100);
        }

        // تأثير ظهور الأشكال
        shapes.forEach((shape, index) => {
            shape.style.opacity = '0';
            setTimeout(() => {
                shape.style.transition = 'opacity 1s ease';
                shape.style.opacity = '1';
            }, 200 + (index * 100));
        });

        // تحسين الأداء للحقول
        const inputs = document.querySelectorAll('.input-field');
        inputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'translateY(-2px)';
                this.style.willChange = 'transform';
            });
            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'translateY(0)';
                this.style.willChange = 'auto';
            });
        });

        // تحسين التنقل بالكيبورد
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter') {
                const focusedElement = document.activeElement;
                if (focusedElement.id === 'username') {
                    document.getElementById('password').focus();
                    e.preventDefault();
                }
            }
        });
    });

    // دالة إظهار خطأ محسنة
    function showLoginError(message) {
        // إزالة الرسائل السابقة
        const existingAlerts = document.querySelectorAll('.alert');
        existingAlerts.forEach(alert => alert.remove());

        // إنشاء رسالة جديدة
        const alert = document.createElement('div');
        alert.className = 'alert alert-error mb-4';
        alert.innerHTML = `
            <i class="fas fa-exclamation-circle ml-2"></i>
            ${message}
        `;

        // إدراج الرسالة
        const form = document.querySelector('form');
        form.insertBefore(alert, form.firstChild);

        // إخفاء الرسالة بعد 5 ثوان
        setTimeout(() => {
            alert.style.transition = 'all 0.3s ease';
            alert.style.opacity = '0';
            alert.style.transform = 'translateY(-10px)';
            setTimeout(() => alert.remove(), 300);
        }, 5000);
    }
</script>
{% endblock %}
