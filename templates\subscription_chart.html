{% extends "base.html" %}

{% block title %}مخطط الاشتراكات - نظام إدارة الاشتراكات المتطور{% endblock %}

{% block page_title %}
<div class="flex items-center space-x-3 space-x-reverse">
    <div class="hologram-effect p-2 rounded-lg">
        <i class="fas fa-chart-pie text-xl text-white neon-glow"></i>
    </div>
    <span class="neon-glow">مخطط الاشتراكات التفاعلي</span>
</div>
{% endblock %}

{% block page_description %}
<div class="flex flex-col sm:flex-row items-start sm:items-center justify-between">
    <span>عرض تفاعلي ومرئي لجميع الاشتراكات والإحصائيات</span>
    <span class="text-sm text-blue-600 font-medium mt-1 sm:mt-0">تطوير: المهندس محمد ياسر الجبوري</span>
</div>
{% endblock %}

{% block header_actions %}
<div class="flex flex-wrap items-center gap-2 lg:gap-3">
    <button id="refreshChartBtn" class="btn-secondary ripple-effect light-effect">
        <i class="fas fa-sync-alt ml-2"></i>
        <span class="hidden sm:inline">تحديث البيانات</span>
    </button>
    <button id="exportChartBtn" class="btn-secondary ripple-effect crystal-effect">
        <i class="fas fa-download ml-2"></i>
        <span class="hidden sm:inline">تصدير المخطط</span>
    </button>
    <a href="{{ url_for('add_subscription') }}" class="btn-primary ripple-effect hologram-effect">
        <i class="fas fa-plus ml-2"></i>
        <span class="hidden sm:inline">إضافة اشتراك</span>
    </a>
</div>
{% endblock %}

{% block content %}
<!-- إحصائيات سريعة -->
<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6 mb-8">
    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-primary);">
            <i class="fas fa-server"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">{{ total_subscriptions }}</div>
            <div class="stats-card-label">إجمالي الاشتراكات</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-success);">
            <i class="fas fa-check-circle"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">{{ active_subscriptions }}</div>
            <div class="stats-card-label">اشتراكات نشطة</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-warning);">
            <i class="fas fa-exclamation-triangle"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">{{ expiring_soon }}</div>
            <div class="stats-card-label">تنتهي قريباً</div>
        </div>
    </div>

    <div class="stats-card ripple-effect light-effect particles-bg">
        <div class="stats-card-icon" style="background: var(--gradient-info);">
            <i class="fas fa-dollar-sign"></i>
        </div>
        <div class="stats-card-content">
            <div class="stats-card-number">${{ "%.0f"|format(total_cost) }}</div>
            <div class="stats-card-label">إجمالي التكلفة</div>
        </div>
    </div>
</div>

<!-- المخططات التفاعلية -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- مخطط دائري للحالات -->
    <div class="card crystal-effect">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="fas fa-chart-pie ml-2 text-blue-600"></i>
                توزيع الاشتراكات حسب الحالة
            </h3>
        </div>
        <div class="card-body">
            <canvas id="statusChart" width="400" height="300"></canvas>
        </div>
    </div>

    <!-- مخطط عمودي للمزودين -->
    <div class="card crystal-effect">
        <div class="card-header">
            <h3 class="text-lg font-semibold text-gray-900 flex items-center">
                <i class="fas fa-chart-bar ml-2 text-green-600"></i>
                الاشتراكات حسب المزود
            </h3>
        </div>
        <div class="card-body">
            <canvas id="providerChart" width="400" height="300"></canvas>
        </div>
    </div>
</div>

<!-- مخطط خطي للتكاليف الشهرية -->
<div class="card crystal-effect mb-8">
    <div class="card-header">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            <i class="fas fa-chart-line ml-2 text-purple-600"></i>
            تطور التكاليف الشهرية
        </h3>
    </div>
    <div class="card-body">
        <canvas id="costChart" width="800" height="400"></canvas>
    </div>
</div>

<!-- جدول تفصيلي -->
<div class="card crystal-effect">
    <div class="card-header">
        <h3 class="text-lg font-semibold text-gray-900 flex items-center">
            <i class="fas fa-table ml-2 text-indigo-600"></i>
            تفاصيل الاشتراكات
        </h3>
    </div>
    <div class="card-body">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="table-header">
                    <tr>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">الاسم</th>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">المزود</th>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">الحالة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">التكلفة</th>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">تاريخ الانتهاء</th>
                        <th class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider">الإجراءات</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    {% for subscription in subscriptions %}
                    <tr class="table-row hover:bg-gray-50">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="h-8 w-8 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
                                    <i class="fas fa-server text-white text-xs"></i>
                                </div>
                                <div class="mr-3">
                                    <div class="text-sm font-medium text-gray-900">{{ subscription.name }}</div>
                                    {% if subscription.cloud_name %}
                                    <div class="text-xs text-gray-500">{{ subscription.cloud_name }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ subscription.provider.name }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="badge-enhanced
                                {% if subscription.status == 'active' %}badge-status-active
                                {% elif subscription.status == 'suspended' %}badge-status-suspended
                                {% elif subscription.status == 'expired' %}badge-status-expired
                                {% endif %}">
                                {% if subscription.status == 'active' %}نشط
                                {% elif subscription.status == 'suspended' %}معلق
                                {% elif subscription.status == 'expired' %}منتهي
                                {% endif %}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                            ${{ "%.2f"|format(subscription.price) }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            {{ subscription.end_date.strftime('%Y-%m-%d') }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div class="flex space-x-2 space-x-reverse">
                                <button class="action-btn text-blue-600 hover:text-blue-900 hover:bg-blue-100 ripple-effect" 
                                        onclick="viewSubscription({{ subscription.id }})" 
                                        title="عرض">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <a href="{{ url_for('edit_subscription', id=subscription.id) }}" 
                                   class="action-btn text-amber-600 hover:text-amber-900 hover:bg-amber-100 ripple-effect" 
                                   title="تعديل">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <button class="action-btn text-green-600 hover:text-green-900 hover:bg-green-100 ripple-effect" 
                                        onclick="sendEmail({{ subscription.id }})" 
                                        title="إرسال إيميل">
                                    <i class="fas fa-envelope"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- توقيع المطور -->
<div class="mt-8 text-center p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-xl border border-blue-200">
    <div class="flex items-center justify-center space-x-3 space-x-reverse mb-2">
        <i class="fas fa-chart-pie text-2xl text-blue-600"></i>
        <h3 class="text-lg font-bold text-gray-800">مخطط الاشتراكات التفاعلي</h3>
        <i class="fas fa-analytics text-2xl text-purple-600"></i>
    </div>
    <p class="text-sm text-gray-600 mb-3">
        عرض تفاعلي ومرئي شامل لجميع الاشتراكات مع إحصائيات متقدمة ومخططات بيانية
    </p>
    <div class="flex items-center justify-center space-x-2 space-x-reverse">
        <i class="fas fa-user-tie text-blue-600"></i>
        <span class="font-semibold text-gray-800">تطوير وتصميم:</span>
        <span class="font-bold text-blue-600 neon-glow">المهندس محمد ياسر الجبوري</span>
        <i class="fas fa-heart text-red-500"></i>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// بيانات المخططات
const statusData = {
    labels: ['نشط', 'معلق', 'منتهي'],
    datasets: [{
        data: [{{ active_subscriptions }}, {{ suspended_subscriptions }}, {{ expired_subscriptions }}],
        backgroundColor: [
            'rgba(34, 197, 94, 0.8)',
            'rgba(251, 191, 36, 0.8)',
            'rgba(239, 68, 68, 0.8)'
        ],
        borderColor: [
            'rgba(34, 197, 94, 1)',
            'rgba(251, 191, 36, 1)',
            'rgba(239, 68, 68, 1)'
        ],
        borderWidth: 2
    }]
};

const providerData = {
    labels: {{ provider_names | tojsonfilter }},
    datasets: [{
        label: 'عدد الاشتراكات',
        data: {{ provider_counts | tojsonfilter }},
        backgroundColor: 'rgba(59, 130, 246, 0.8)',
        borderColor: 'rgba(59, 130, 246, 1)',
        borderWidth: 2
    }]
};

// إنشاء المخططات
const statusChart = new Chart(document.getElementById('statusChart'), {
    type: 'doughnut',
    data: statusData,
    options: {
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom'
            }
        }
    }
});

const providerChart = new Chart(document.getElementById('providerChart'), {
    type: 'bar',
    data: providerData,
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// وظائف التفاعل
function viewSubscription(id) {
    // عرض تفاصيل الاشتراك
    alert('عرض تفاصيل الاشتراك رقم: ' + id);
}

function sendEmail(id) {
    // إرسال إيميل للعميل
    window.location.href = `/send_email?subscription_id=${id}`;
}

// تحديث البيانات
document.getElementById('refreshChartBtn').addEventListener('click', function() {
    location.reload();
});

// تصدير المخطط
document.getElementById('exportChartBtn').addEventListener('click', function() {
    window.print();
});
</script>
{% endblock %}
