#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحسينات الأداء والأمان
نظام إدارة الاشتراكات المتطور
تطوير: المهندس محمد ياسر الجبوري
"""

import os
import hashlib
import secrets
import time
from functools import wraps
from datetime import datetime, timedelta
from flask import request, session, jsonify, abort
from werkzeug.security import generate_password_hash
import redis
import logging
from logging.handlers import RotatingFileHandler
import json

# إعداد Redis للتخزين المؤقت (اختياري)
try:
    redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
    REDIS_AVAILABLE = True
except:
    redis_client = None
    REDIS_AVAILABLE = False

class SecurityManager:
    """مدير الأمان المتقدم"""
    
    def __init__(self, app=None):
        self.app = app
        self.failed_attempts = {}
        self.blocked_ips = set()
        self.rate_limits = {}
        
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """تهيئة مدير الأمان مع التطبيق"""
        self.app = app
        
        # إعداد الأمان
        app.config.setdefault('MAX_LOGIN_ATTEMPTS', 5)
        app.config.setdefault('LOCKOUT_DURATION', 900)  # 15 دقيقة
        app.config.setdefault('RATE_LIMIT_PER_MINUTE', 60)
        app.config.setdefault('SESSION_TIMEOUT', 3600)  # ساعة واحدة
        
        # تشفير قوي للجلسات
        if not app.config.get('SECRET_KEY'):
            app.config['SECRET_KEY'] = secrets.token_hex(32)
        
        # إعداد headers الأمان
        @app.after_request
        def add_security_headers(response):
            response.headers['X-Content-Type-Options'] = 'nosniff'
            response.headers['X-Frame-Options'] = 'DENY'
            response.headers['X-XSS-Protection'] = '1; mode=block'
            response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
            response.headers['Content-Security-Policy'] = "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:;"
            return response
    
    def check_rate_limit(self, identifier, limit_per_minute=60):
        """فحص حد المعدل"""
        current_time = time.time()
        minute_key = int(current_time // 60)
        
        if identifier not in self.rate_limits:
            self.rate_limits[identifier] = {}
        
        # تنظيف البيانات القديمة
        old_keys = [k for k in self.rate_limits[identifier].keys() if k < minute_key - 1]
        for key in old_keys:
            del self.rate_limits[identifier][key]
        
        # فحص الحد الحالي
        current_count = self.rate_limits[identifier].get(minute_key, 0)
        
        if current_count >= limit_per_minute:
            return False
        
        # زيادة العداد
        self.rate_limits[identifier][minute_key] = current_count + 1
        return True
    
    def record_failed_login(self, identifier):
        """تسجيل محاولة دخول فاشلة"""
        current_time = time.time()
        
        if identifier not in self.failed_attempts:
            self.failed_attempts[identifier] = []
        
        self.failed_attempts[identifier].append(current_time)
        
        # تنظيف المحاولات القديمة (أكثر من ساعة)
        self.failed_attempts[identifier] = [
            attempt for attempt in self.failed_attempts[identifier]
            if current_time - attempt < 3600
        ]
        
        # فحص إذا تم تجاوز الحد
        recent_attempts = [
            attempt for attempt in self.failed_attempts[identifier]
            if current_time - attempt < 900  # آخر 15 دقيقة
        ]
        
        if len(recent_attempts) >= self.app.config['MAX_LOGIN_ATTEMPTS']:
            self.blocked_ips.add(identifier)
            return True
        
        return False
    
    def is_blocked(self, identifier):
        """فحص إذا كان المعرف محظور"""
        return identifier in self.blocked_ips
    
    def unblock_ip(self, identifier):
        """إلغاء حظر IP"""
        self.blocked_ips.discard(identifier)
        if identifier in self.failed_attempts:
            del self.failed_attempts[identifier]

class PerformanceManager:
    """مدير الأداء المتقدم"""
    
    def __init__(self, app=None):
        self.app = app
        self.cache = {}
        self.cache_stats = {'hits': 0, 'misses': 0}
        
        if app:
            self.init_app(app)
    
    def init_app(self, app):
        """تهيئة مدير الأداء مع التطبيق"""
        self.app = app
        
        # إعداد التخزين المؤقت
        app.config.setdefault('CACHE_TIMEOUT', 300)  # 5 دقائق
        app.config.setdefault('ENABLE_COMPRESSION', True)
        
        # ضغط الاستجابات
        if app.config.get('ENABLE_COMPRESSION'):
            @app.after_request
            def compress_response(response):
                if (response.content_length and 
                    response.content_length > 1000 and
                    'gzip' in request.headers.get('Accept-Encoding', '')):
                    response.headers['Content-Encoding'] = 'gzip'
                return response
    
    def cache_get(self, key):
        """الحصول على قيمة من التخزين المؤقت"""
        if REDIS_AVAILABLE:
            try:
                value = redis_client.get(f"cache:{key}")
                if value:
                    self.cache_stats['hits'] += 1
                    return json.loads(value)
            except:
                pass
        
        # التخزين المؤقت المحلي
        if key in self.cache:
            data, timestamp = self.cache[key]
            if time.time() - timestamp < self.app.config['CACHE_TIMEOUT']:
                self.cache_stats['hits'] += 1
                return data
            else:
                del self.cache[key]
        
        self.cache_stats['misses'] += 1
        return None
    
    def cache_set(self, key, value, timeout=None):
        """تعيين قيمة في التخزين المؤقت"""
        if timeout is None:
            timeout = self.app.config['CACHE_TIMEOUT']
        
        if REDIS_AVAILABLE:
            try:
                redis_client.setex(f"cache:{key}", timeout, json.dumps(value))
                return
            except:
                pass
        
        # التخزين المؤقت المحلي
        self.cache[key] = (value, time.time())
    
    def cache_delete(self, key):
        """حذف قيمة من التخزين المؤقت"""
        if REDIS_AVAILABLE:
            try:
                redis_client.delete(f"cache:{key}")
            except:
                pass
        
        if key in self.cache:
            del self.cache[key]
    
    def get_cache_stats(self):
        """الحصول على إحصائيات التخزين المؤقت"""
        total = self.cache_stats['hits'] + self.cache_stats['misses']
        hit_rate = (self.cache_stats['hits'] / total * 100) if total > 0 else 0
        
        return {
            'hits': self.cache_stats['hits'],
            'misses': self.cache_stats['misses'],
            'hit_rate': round(hit_rate, 2),
            'cache_size': len(self.cache)
        }

class DatabaseOptimizer:
    """محسن قاعدة البيانات"""
    
    @staticmethod
    def optimize_queries():
        """تحسين استعلامات قاعدة البيانات"""
        optimizations = {
            'indexes': [
                'CREATE INDEX IF NOT EXISTS idx_subscription_status ON subscription(status);',
                'CREATE INDEX IF NOT EXISTS idx_subscription_end_date ON subscription(end_date);',
                'CREATE INDEX IF NOT EXISTS idx_invoice_status ON invoice(status);',
                'CREATE INDEX IF NOT EXISTS idx_notification_user_read ON notification(user_id, is_read);',
                'CREATE INDEX IF NOT EXISTS idx_activity_log_user_date ON activity_log(user_id, created_at);'
            ],
            'cleanup': [
                'DELETE FROM activity_log WHERE created_at < date("now", "-6 months");',
                'DELETE FROM notification WHERE created_at < date("now", "-3 months") AND is_read = 1;'
            ]
        }
        return optimizations

# Decorators للأمان والأداء

def rate_limit(per_minute=60):
    """محدد معدل الطلبات"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            identifier = request.remote_addr
            
            if hasattr(f, 'security_manager'):
                if not f.security_manager.check_rate_limit(identifier, per_minute):
                    abort(429)  # Too Many Requests
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def cache_result(timeout=300):
    """تخزين مؤقت للنتائج"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # إنشاء مفتاح التخزين المؤقت
            cache_key = f"{f.__name__}:{hashlib.md5(str(args + tuple(kwargs.items())).encode()).hexdigest()}"
            
            if hasattr(f, 'performance_manager'):
                # محاولة الحصول من التخزين المؤقت
                cached_result = f.performance_manager.cache_get(cache_key)
                if cached_result is not None:
                    return cached_result
                
                # تنفيذ الدالة وحفظ النتيجة
                result = f(*args, **kwargs)
                f.performance_manager.cache_set(cache_key, result, timeout)
                return result
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def security_check():
    """فحص أمني شامل"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # فحص IP المحظور
            if hasattr(f, 'security_manager'):
                if f.security_manager.is_blocked(request.remote_addr):
                    abort(403)  # Forbidden
            
            # فحص صحة الجلسة
            if 'user_id' in session:
                # فحص انتهاء صلاحية الجلسة
                last_activity = session.get('last_activity')
                if last_activity:
                    if time.time() - last_activity > 3600:  # ساعة واحدة
                        session.clear()
                        abort(401)  # Unauthorized
                
                session['last_activity'] = time.time()
            
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def audit_log(action):
    """تسجيل العمليات للمراجعة"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            start_time = time.time()
            
            try:
                result = f(*args, **kwargs)
                
                # تسجيل العملية الناجحة
                log_entry = {
                    'timestamp': datetime.utcnow().isoformat(),
                    'user_id': session.get('user_id'),
                    'action': action,
                    'function': f.__name__,
                    'ip_address': request.remote_addr,
                    'user_agent': request.headers.get('User-Agent'),
                    'duration': round(time.time() - start_time, 3),
                    'status': 'success'
                }
                
                # حفظ في ملف السجل
                logging.getLogger('audit').info(json.dumps(log_entry))
                
                return result
                
            except Exception as e:
                # تسجيل العملية الفاشلة
                log_entry = {
                    'timestamp': datetime.utcnow().isoformat(),
                    'user_id': session.get('user_id'),
                    'action': action,
                    'function': f.__name__,
                    'ip_address': request.remote_addr,
                    'user_agent': request.headers.get('User-Agent'),
                    'duration': round(time.time() - start_time, 3),
                    'status': 'error',
                    'error': str(e)
                }
                
                logging.getLogger('audit').error(json.dumps(log_entry))
                raise
                
        return decorated_function
    return decorator

# إعداد نظام السجلات المتقدم
def setup_advanced_logging(app):
    """إعداد نظام السجلات المتقدم"""
    
    # إنشاء مجلد السجلات
    if not os.path.exists('logs'):
        os.makedirs('logs')
    
    # سجل الأمان
    security_handler = RotatingFileHandler(
        'logs/security.log', maxBytes=10*1024*1024, backupCount=10
    )
    security_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s'
    ))
    security_logger = logging.getLogger('security')
    security_logger.addHandler(security_handler)
    security_logger.setLevel(logging.INFO)
    
    # سجل المراجعة
    audit_handler = RotatingFileHandler(
        'logs/audit.log', maxBytes=10*1024*1024, backupCount=10
    )
    audit_handler.setFormatter(logging.Formatter(
        '%(asctime)s: %(message)s'
    ))
    audit_logger = logging.getLogger('audit')
    audit_logger.addHandler(audit_handler)
    audit_logger.setLevel(logging.INFO)
    
    # سجل الأداء
    performance_handler = RotatingFileHandler(
        'logs/performance.log', maxBytes=10*1024*1024, backupCount=10
    )
    performance_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s: %(message)s'
    ))
    performance_logger = logging.getLogger('performance')
    performance_logger.addHandler(performance_handler)
    performance_logger.setLevel(logging.INFO)

# دالة تهيئة شاملة
def init_security_performance(app):
    """تهيئة أنظمة الأمان والأداء"""
    
    # إنشاء مديري الأمان والأداء
    security_manager = SecurityManager(app)
    performance_manager = PerformanceManager(app)
    
    # إعداد السجلات
    setup_advanced_logging(app)
    
    # إرجاع المديرين للاستخدام
    return security_manager, performance_manager
