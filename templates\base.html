<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة الاشتراكات{% endblock %}</title>
    
    <!-- CSS Libraries -->
    <script src="https://cdn.tailwindcss.com"></script>
    <meta name="description" content="نظام إدارة الاشتراكات المتطور - تطوير المهندس محمد ياسر الجبوري">
    <meta name="keywords" content="إدارة الاشتراكات, الخدمات السحابية, الفواتير, التقارير">
    <meta name="author" content="المهندس محمد ياسر الجبوري">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        body { 
            font-family: 'Cairo', sans-serif; 
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }
        .sidebar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-left: 1px solid rgba(102, 126, 234, 0.1);
            box-shadow: 4px 0 20px rgba(0, 0, 0, 0.05);
        }
        .nav-item {
            transition: all 0.3s ease;
            border-radius: 12px;
            margin: 4px 0;
            position: relative;
            overflow: hidden;
        }
        .nav-item:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: translateX(-5px);
        }
        .nav-item.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }
        .btn-primary { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; 
            padding: 12px 24px; 
            border-radius: 12px; 
            transition: all 0.3s ease;
            border: none;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
        }
        .btn-primary:hover { 
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
            color: white;
            text-decoration: none;
        }
        .btn-secondary {
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            border: 2px solid rgba(102, 126, 234, 0.2);
            padding: 10px 20px;
            border-radius: 12px;
            transition: all 0.3s ease;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
        }
        .btn-secondary:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
            text-decoration: none;
        }
        .btn-danger {
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s ease;
            border: none;
            font-weight: 500;
            text-decoration: none;
            display: inline-block;
        }
        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(239, 68, 68, 0.4);
            color: white;
            text-decoration: none;
        }
        .alert {
            padding: 12px 16px;
            border-radius: 12px;
            margin-bottom: 16px;
            border: 1px solid transparent;
        }
        .alert-success {
            background: rgba(16, 185, 129, 0.1);
            border-color: rgba(16, 185, 129, 0.2);
            color: #065f46;
        }
        .alert-error {
            background: rgba(239, 68, 68, 0.1);
            border-color: rgba(239, 68, 68, 0.2);
            color: #991b1b;
        }
        .alert-warning {
            background: rgba(245, 158, 11, 0.1);
            border-color: rgba(245, 158, 11, 0.2);
            color: #92400e;
        }
        .input-field {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(102, 126, 234, 0.2);
            border-radius: 12px;
            padding: 12px 16px;
            transition: all 0.3s ease;
            width: 100%;
        }
        .input-field:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: white;
        }
        .badge-success { 
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            color: white; 
            padding: 6px 12px; 
            border-radius: 20px; 
            font-size: 12px; 
            font-weight: 600;
        }
        .badge-warning { 
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
            color: white; 
            padding: 6px 12px; 
            border-radius: 20px; 
            font-size: 12px; 
            font-weight: 600;
        }
        .badge-danger { 
            background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
            color: white; 
            padding: 6px 12px; 
            border-radius: 20px; 
            font-size: 12px; 
            font-weight: 600;
        }
        .badge-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white; 
            padding: 6px 12px; 
            border-radius: 20px; 
            font-size: 12px; 
            font-weight: 600;
        }
        .table-row {
            transition: all 0.2s ease;
        }
        .table-row:hover {
            background: rgba(102, 126, 234, 0.05);
            transform: scale(1.01);
        }

        /* Badge Styles */
        .badge-primary {
            display: inline-flex;
            align-items: center;
            padding: 0.125rem 0.625rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            background-color: #dbeafe;
            color: #1e40af;
        }

        .badge-success {
            display: inline-flex;
            align-items: center;
            padding: 0.125rem 0.625rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            background-color: #dcfce7;
            color: #166534;
        }

        .badge-warning {
            display: inline-flex;
            align-items: center;
            padding: 0.125rem 0.625rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            background-color: #fef3c7;
            color: #92400e;
        }

        .badge-danger {
            display: inline-flex;
            align-items: center;
            padding: 0.125rem 0.625rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            background-color: #fee2e2;
            color: #991b1b;
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body class="bg-gray-50">
    {% if session.user_id %}
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="sidebar w-64 fixed h-full z-30">
            <!-- Logo -->
            <div class="p-6 border-b border-gray-200">
                <div class="flex items-center">
                    <i class="fas fa-cloud-upload-alt text-2xl text-blue-600 ml-3"></i>
                    <div>
                        <h2 class="text-lg font-bold text-gray-800">إدارة الاشتراكات</h2>
                        <p class="text-xs text-gray-500">نظام شامل ومتطور</p>
                    </div>
                </div>
            </div>

            <!-- Navigation المحسن مع التفرع -->
            <nav class="p-4 space-y-1">
                <!-- لوحة التحكم -->
                <a href="{{ url_for('dashboard') }}" class="nav-item flex items-center p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint == 'dashboard' %}active bg-blue-600 text-white{% else %}text-gray-600 hover:bg-gray-100{% endif %}">
                    <i class="fas fa-tachometer-alt w-5 h-5 ml-3"></i>
                    لوحة المعلومات
                </a>

                <!-- قسم الاشتراكات مع التفرع -->
                <div class="nav-group">
                    <div class="nav-parent cursor-pointer" onclick="toggleSubmenu('subscriptions-menu')">
                        <div class="nav-item flex items-center justify-between p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint in ['subscriptions', 'add_subscription', 'edit_subscription', 'subscription_chart', 'subscription_analytics', 'subscription_reports'] %}active bg-blue-600 text-white{% else %}text-gray-600 hover:bg-gray-100{% endif %}">
                            <div class="flex items-center">
                                <i class="fas fa-server w-5 h-5 ml-3"></i>
                                <span>إدارة الاشتراكات</span>
                            </div>
                            <i class="fas fa-chevron-down transition-transform duration-300" id="subscriptions-arrow"></i>
                        </div>
                    </div>

                    <div class="submenu overflow-hidden transition-all duration-300 max-h-0" id="subscriptions-menu">
                        <div class="mr-6 mt-1 space-y-1">
                            <a href="{{ url_for('subscription_chart') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'subscription_chart' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-chart-pie w-4 h-4 ml-2"></i>
                                <span>مخطط الاشتراكات</span>
                            </a>
                            <a href="{{ url_for('subscriptions') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'subscriptions' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-list w-4 h-4 ml-2"></i>
                                <span>قائمة الاشتراكات</span>
                            </a>
                            <a href="{{ url_for('add_subscription') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'add_subscription' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-plus-circle w-4 h-4 ml-2"></i>
                                <span>إضافة اشتراك جديد</span>
                            </a>
                            <a href="{{ url_for('subscription_analytics') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'subscription_analytics' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-chart-line w-4 h-4 ml-2"></i>
                                <span>تحليلات الاشتراكات</span>
                            </a>
                            <a href="{{ url_for('subscription_reports') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'subscription_reports' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-file-chart-line w-4 h-4 ml-2"></i>
                                <span>تقارير الاشتراكات</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- قسم الفواتير مع التفرع -->
                <div class="nav-group">
                    <div class="nav-parent cursor-pointer" onclick="toggleSubmenu('invoices-menu')">
                        <div class="nav-item flex items-center justify-between p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint in ['invoices', 'add_invoice', 'edit_invoice', 'invoice_reports'] %}active bg-blue-600 text-white{% else %}text-gray-600 hover:bg-gray-100{% endif %}">
                            <div class="flex items-center">
                                <i class="fas fa-file-invoice w-5 h-5 ml-3"></i>
                                <span>إدارة الفواتير</span>
                            </div>
                            <i class="fas fa-chevron-down transition-transform duration-300" id="invoices-arrow"></i>
                        </div>
                    </div>

                    <div class="submenu overflow-hidden transition-all duration-300 max-h-0" id="invoices-menu">
                        <div class="mr-6 mt-1 space-y-1">
                            <a href="{{ url_for('invoices') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'invoices' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-list-alt w-4 h-4 ml-2"></i>
                                <span>قائمة الفواتير</span>
                            </a>
                            <a href="{{ url_for('add_invoice') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'add_invoice' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-plus-square w-4 h-4 ml-2"></i>
                                <span>إنشاء فاتورة جديدة</span>
                            </a>
                            <a href="{{ url_for('invoice_reports') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'invoice_reports' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-chart-bar w-4 h-4 ml-2"></i>
                                <span>تقارير الفواتير</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- قسم التواصل والإيميل -->
                <div class="nav-group">
                    <div class="nav-parent cursor-pointer" onclick="toggleSubmenu('email-menu')">
                        <div class="nav-item flex items-center justify-between p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint in ['email_center', 'send_email', 'email_templates'] %}active bg-blue-600 text-white{% else %}text-gray-600 hover:bg-gray-100{% endif %}">
                            <div class="flex items-center">
                                <i class="fas fa-envelope w-5 h-5 ml-3"></i>
                                <span>مركز التواصل</span>
                            </div>
                            <i class="fas fa-chevron-down transition-transform duration-300" id="email-arrow"></i>
                        </div>
                    </div>

                    <div class="submenu overflow-hidden transition-all duration-300 max-h-0" id="email-menu">
                        <div class="mr-6 mt-1 space-y-1">
                            <a href="{{ url_for('email_center') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'email_center' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-inbox w-4 h-4 ml-2"></i>
                                <span>مركز الرسائل</span>
                            </a>
                            <a href="{{ url_for('send_email') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'send_email' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-paper-plane w-4 h-4 ml-2"></i>
                                <span>إرسال رسالة</span>
                            </a>
                            <a href="{{ url_for('email_templates') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'email_templates' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-file-alt w-4 h-4 ml-2"></i>
                                <span>قوالب الرسائل</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- التقارير -->
                <a href="{{ url_for('reports') }}" class="nav-item flex items-center p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint == 'reports' %}active bg-blue-600 text-white{% else %}text-gray-600 hover:bg-gray-100{% endif %}">
                    <i class="fas fa-chart-bar w-5 h-5 ml-3"></i>
                    التقارير العامة
                </a>

                {% if session.role == 'admin' %}
                <!-- إدارة المستخدمين -->
                <a href="{{ url_for('users') }}" class="nav-item flex items-center p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint in ['users', 'add_user', 'edit_user'] %}active bg-blue-600 text-white{% else %}text-gray-600 hover:bg-gray-100{% endif %}">
                    <i class="fas fa-users w-5 h-5 ml-3"></i>
                    إدارة المستخدمين
                </a>
                {% endif %}

                <!-- قسم الإدارة المتقدمة -->
                {% if session.role == 'admin' %}
                <div class="nav-group">
                    <div class="nav-parent cursor-pointer" onclick="toggleSubmenu('admin-menu')">
                        <div class="nav-item flex items-center justify-between p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint in ['settings', 'activity_logs', 'system_health'] %}active bg-blue-600 text-white{% else %}text-gray-600 hover:bg-gray-100{% endif %}">
                            <div class="flex items-center">
                                <i class="fas fa-shield-alt w-5 h-5 ml-3"></i>
                                <span>الإدارة المتقدمة</span>
                            </div>
                            <i class="fas fa-chevron-down transition-transform duration-300" id="admin-arrow"></i>
                        </div>
                    </div>

                    <div class="submenu overflow-hidden transition-all duration-300 max-h-0" id="admin-menu">
                        <div class="mr-6 mt-1 space-y-1">
                            <a href="{{ url_for('settings') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'settings' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-cog w-4 h-4 ml-2"></i>
                                <span>إعدادات النظام</span>
                            </a>
                            <a href="{{ url_for('activity_logs') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'activity_logs' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-history w-4 h-4 ml-2"></i>
                                <span>سجل الأنشطة</span>
                            </a>
                            <a href="{{ url_for('system_health') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'system_health' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-heartbeat w-4 h-4 ml-2"></i>
                                <span>صحة النظام</span>
                            </a>
                            <a href="{{ url_for('create_backup_route') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors text-gray-600 hover:bg-gray-50">
                                <i class="fas fa-save w-4 h-4 ml-2"></i>
                                <span>نسخة احتياطية</span>
                            </a>
                        </div>
                    </div>
                </div>
                {% else %}
                <!-- الإعدادات للمستخدمين العاديين -->
                <a href="{{ url_for('settings') }}" class="nav-item flex items-center p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint == 'settings' %}active bg-blue-600 text-white{% else %}text-gray-600 hover:bg-gray-100{% endif %}">
                    <i class="fas fa-cog w-5 h-5 ml-3"></i>
                    الإعدادات
                </a>
                {% endif %}
            </nav>

            <!-- User Profile -->
            <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                        {{ session.username[0].upper() }}
                    </div>
                    <div class="mr-3 flex-1">
                        <p class="text-sm font-medium text-gray-900">{{ session.username }}</p>
                        <p class="text-xs text-gray-500">{{ session.role }}</p>
                    </div>
                    <a href="{{ url_for('logout') }}" class="text-gray-400 hover:text-red-500 transition-colors">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 mr-64">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b border-gray-200 p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold text-gray-900">{% block page_title %}لوحة التحكم{% endblock %}</h1>
                        <p class="text-gray-600">{% block page_description %}مرحباً بك في نظام إدارة الاشتراكات{% endblock %}</p>
                    </div>
                    <div class="flex items-center space-x-4 space-x-reverse">
                        {% block header_actions %}{% endblock %}
                    </div>
                </div>
            </header>

            <!-- Content -->
            <main class="p-6">
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ 'error' if category == 'error' else category }}">
                                <i class="fas fa-{{ 'exclamation-circle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} ml-2"></i>
                                {{ message }}
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                {% block content %}{% endblock %}
            </main>
        </div>
    </div>
    {% else %}
        {% block full_content %}{% endblock %}
    {% endif %}

    <!-- JavaScript للتحكم في القوائم المنسدلة -->
    <script>
        // تبديل القوائم الفرعية
        function toggleSubmenu(menuId) {
            const menu = document.getElementById(menuId);
            const arrow = document.getElementById(menuId.replace('-menu', '-arrow'));

            if (menu.style.maxHeight === '0px' || menu.style.maxHeight === '') {
                // فتح القائمة
                menu.style.maxHeight = menu.scrollHeight + 'px';
                if (arrow) arrow.style.transform = 'rotate(180deg)';
            } else {
                // إغلاق القائمة
                menu.style.maxHeight = '0px';
                if (arrow) arrow.style.transform = 'rotate(0deg)';
            }
        }

        // فتح القائمة الفرعية إذا كانت الصفحة الحالية ضمنها
        document.addEventListener('DOMContentLoaded', function() {
            // فحص قائمة الاشتراكات
            const subscriptionsMenu = document.getElementById('subscriptions-menu');
            const subscriptionsArrow = document.getElementById('subscriptions-arrow');
            if (subscriptionsMenu && subscriptionsMenu.querySelector('.bg-blue-100')) {
                subscriptionsMenu.style.maxHeight = subscriptionsMenu.scrollHeight + 'px';
                if (subscriptionsArrow) subscriptionsArrow.style.transform = 'rotate(180deg)';
            }

            // فحص قائمة الفواتير
            const invoicesMenu = document.getElementById('invoices-menu');
            const invoicesArrow = document.getElementById('invoices-arrow');
            if (invoicesMenu && invoicesMenu.querySelector('.bg-blue-100')) {
                invoicesMenu.style.maxHeight = invoicesMenu.scrollHeight + 'px';
                if (invoicesArrow) invoicesArrow.style.transform = 'rotate(180deg)';
            }

            // فحص قائمة الإيميل
            const emailMenu = document.getElementById('email-menu');
            const emailArrow = document.getElementById('email-arrow');
            if (emailMenu && emailMenu.querySelector('.bg-blue-100')) {
                emailMenu.style.maxHeight = emailMenu.scrollHeight + 'px';
                if (emailArrow) emailArrow.style.transform = 'rotate(180deg)';
            }

            // فحص قائمة الإدارة المتقدمة
            const adminMenu = document.getElementById('admin-menu');
            const adminArrow = document.getElementById('admin-arrow');
            if (adminMenu && adminMenu.querySelector('.bg-blue-100')) {
                adminMenu.style.maxHeight = adminMenu.scrollHeight + 'px';
                if (adminArrow) adminArrow.style.transform = 'rotate(180deg)';
            }
        });

        // إضافة تأثيرات hover للقوائم
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('mouseenter', function() {
                if (!this.classList.contains('active')) {
                    this.style.transform = 'translateX(4px)';
                }
            });

            item.addEventListener('mouseleave', function() {
                if (!this.classList.contains('active')) {
                    this.style.transform = 'translateX(0)';
                }
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
