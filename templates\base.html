<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}نظام إدارة الاشتراكات{% endblock %}</title>
    <link rel="icon" type="image/x-icon" href="{{ url_for('static', filename='favicon.ico') }}">
    
    <!-- CSS Libraries -->
    <script src="https://cdn.tailwindcss.com"></script>
    <meta name="description" content="نظام إدارة الاشتراكات المتطور - تطوير المهندس محمد ياسر الجبوري">
    <meta name="keywords" content="إدارة الاشتراكات, الخدمات السحابية, الفواتير, التقارير">
    <meta name="author" content="المهندس محمد ياسر الجبوري">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/animations.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/notifications.css') }}">

    <!-- PWA Meta Tags -->
    <link rel="manifest" href="{{ url_for('static', filename='manifest.json') }}">
    <meta name="theme-color" content="#667eea">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="إدارة الاشتراكات">
    <link rel="apple-touch-icon" href="{{ url_for('static', filename='images/icon-192x192.png') }}">

    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="{{ url_for('static', filename='js/navigation.js') }}"></script>
    <script src="{{ url_for('static', filename='js/notifications.js') }}"></script>
    <script src="{{ url_for('static', filename='js/pwa.js') }}"></script>
    
    <style>
        :root {
            /* الألوان الأساسية المحسنة للقراءة */
            --primary-color: #4f46e5;
            --secondary-color: #7c3aed;
            --accent-color: #06b6d4;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --error-color: #ef4444;
            --info-color: #3b82f6;

            /* التدرجات المحسنة */
            --primary-gradient: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
            --warning-gradient: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
            --danger-gradient: linear-gradient(135deg, var(--error-color) 0%, #dc2626 100%);
            --accent-gradient: linear-gradient(135deg, var(--accent-color) 0%, #0891b2 100%);

            /* خلفيات زجاجية محسنة */
            --glass-bg: rgba(255, 255, 255, 0.08);
            --glass-bg-light: rgba(255, 255, 255, 0.12);
            --glass-bg-heavy: rgba(255, 255, 255, 0.15);
            --glass-border: rgba(255, 255, 255, 0.15);
            --glass-border-light: rgba(255, 255, 255, 0.2);

            /* ظلال محسنة */
            --shadow-light: 0 4px 16px rgba(0, 0, 0, 0.08);
            --shadow-medium: 0 8px 32px rgba(0, 0, 0, 0.12);
            --shadow-heavy: 0 16px 48px rgba(0, 0, 0, 0.16);
            --shadow-colored: 0 8px 32px rgba(79, 70, 229, 0.15);

            /* نصوص محسنة للقراءة */
            --text-primary: rgba(255, 255, 255, 0.95);
            --text-secondary: rgba(255, 255, 255, 0.8);
            --text-muted: rgba(255, 255, 255, 0.65);
            --text-dim: rgba(255, 255, 255, 0.5);

            /* تأثيرات وانتقالات */
            --blur-amount: 20px;
            --blur-light: 16px;
            --blur-heavy: 24px;
            --border-radius: 16px;
            --border-radius-small: 8px;
            --border-radius-large: 24px;
            --transition-fast: 0.15s ease;
            --transition-medium: 0.25s ease;
            --transition-slow: 0.4s ease;
            --transition-bounce: 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        body {
            font-family: 'Cairo', sans-serif;
            background: var(--primary-gradient);
            background-attachment: fixed;
            min-height: 100vh;
            overflow-x: hidden;
            line-height: 1.6;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* خلفية متحركة محسنة */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
            animation: backgroundShift 20s ease-in-out infinite;
            z-index: -1;
        }

        @keyframes backgroundShift {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }

        .sidebar {
            background: var(--glass-bg);
            backdrop-filter: blur(25px);
            border-left: 1px solid var(--glass-border);
            box-shadow: var(--shadow-light);
            position: relative;
            overflow: hidden;
        }

        .sidebar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--primary-gradient);
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0%, 100% { opacity: 0.5; }
            50% { opacity: 1; }
        }

        .nav-item {
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border-radius: 15px;
            margin: 6px 0;
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(10px);
        }

        .nav-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .nav-item:hover::before {
            left: 100%;
        }

        .nav-item:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateX(-8px) scale(1.02);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        .nav-item.active {
            background: var(--primary-gradient);
            color: white;
            transform: translateX(-5px);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
        }
        .card {
            background: var(--glass-bg-light);
            backdrop-filter: blur(var(--blur-amount));
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-medium);
            border: 1px solid var(--glass-border);
            transition: all var(--transition-medium);
            position: relative;
            overflow: hidden;
            will-change: transform;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: var(--primary-gradient);
            opacity: 0.6;
        }

        .card:hover {
            transform: translateY(-4px) scale(1.01);
            box-shadow: var(--shadow-heavy);
            background: var(--glass-bg-heavy);
            border-color: var(--glass-border-light);
        }

        .btn-primary {
            background: var(--primary-gradient);
            color: var(--text-primary);
            padding: 0.75rem 1.5rem;
            border-radius: var(--border-radius-small);
            transition: all var(--transition-medium);
            border: none;
            font-weight: 600;
            font-size: 0.875rem;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            position: relative;
            overflow: hidden;
            cursor: pointer;
            will-change: transform;
            letter-spacing: 0.025em;
            box-shadow: var(--shadow-colored);
        }

        .btn-primary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-primary:hover::before {
            left: 100%;
        }

        .btn-primary:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: var(--shadow-heavy);
            color: var(--text-primary);
            text-decoration: none;
        }
        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: #667eea;
            border: 2px solid rgba(102, 126, 234, 0.3);
            padding: 12px 24px;
            border-radius: 15px;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
        }

        .btn-secondary::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: var(--primary-gradient);
            transition: left 0.5s;
            z-index: -1;
        }

        .btn-secondary:hover::before {
            left: 0;
        }

        .btn-secondary:hover {
            color: white;
            transform: translateY(-3px) scale(1.05);
            text-decoration: none;
            border-color: transparent;
        }

        .btn-danger {
            background: var(--danger-gradient);
            color: white;
            padding: 10px 20px;
            border-radius: 12px;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            border: none;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            position: relative;
            overflow: hidden;
        }

        .btn-danger::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-danger:hover::before {
            left: 100%;
        }

        .btn-danger:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 12px 25px rgba(239, 68, 68, 0.4);
            color: white;
            text-decoration: none;
        }
        .alert {
            padding: 16px 20px;
            border-radius: 15px;
            margin-bottom: 20px;
            border: 1px solid transparent;
            backdrop-filter: blur(10px);
            position: relative;
            overflow: hidden;
            animation: slideInDown 0.5s ease-out;
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .alert::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            animation: progressBar 3s ease-in-out;
        }

        @keyframes progressBar {
            from { width: 100%; }
            to { width: 0%; }
        }

        .alert-success {
            background: rgba(16, 185, 129, 0.15);
            border-color: rgba(16, 185, 129, 0.3);
            color: #065f46;
        }

        .alert-success::before {
            background: var(--success-gradient);
        }

        .alert-error {
            background: rgba(239, 68, 68, 0.15);
            border-color: rgba(239, 68, 68, 0.3);
            color: #991b1b;
        }

        .alert-error::before {
            background: var(--danger-gradient);
        }

        .alert-warning {
            background: rgba(245, 158, 11, 0.15);
            border-color: rgba(245, 158, 11, 0.3);
            color: #92400e;
        }

        .alert-warning::before {
            background: var(--warning-gradient);
        }

        .input-field {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(102, 126, 234, 0.2);
            border-radius: 15px;
            padding: 14px 18px;
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            width: 100%;
            backdrop-filter: blur(10px);
            position: relative;
        }

        .input-field:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15);
            background: rgba(255, 255, 255, 0.95);
            transform: scale(1.02);
        }
        .badge-success {
            background: var(--success-gradient);
            color: white;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 12px;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
            animation: pulse 2s infinite;
        }

        .badge-warning {
            background: var(--warning-gradient);
            color: white;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 12px;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            box-shadow: 0 4px 15px rgba(245, 158, 11, 0.3);
            animation: pulse 2s infinite;
        }

        .badge-danger {
            background: var(--danger-gradient);
            color: white;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 12px;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
            animation: pulse 2s infinite;
        }

        .badge-primary {
            background: var(--primary-gradient);
            color: white;
            padding: 8px 16px;
            border-radius: 25px;
            font-size: 12px;
            font-weight: 600;
            display: inline-flex;
            align-items: center;
            gap: 6px;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }

        .table-row {
            transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            backdrop-filter: blur(10px);
            border-radius: 10px;
            margin: 4px 0;
        }

        .table-row:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateX(8px) scale(1.02);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        }

        /* Advanced UI Components */
        .floating-action-btn {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            background: var(--primary-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
            z-index: 1000;
            animation: float 3s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .floating-action-btn:hover {
            transform: scale(1.1);
            box-shadow: 0 15px 35px rgba(102, 126, 234, 0.6);
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(102, 126, 234, 0.3);
            border-top: 4px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .glass-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .glass-card:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-5px);
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: var(--primary-gradient);
            border-radius: 10px;
            transition: width 0.5s ease;
            position: relative;
            overflow: hidden;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        @keyframes fadeInUp {
            from {
                transform: translateY(20px);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        /* تحسينات الأداء */
        .card,
        .btn-primary,
        .nav-item,
        .input-field {
            will-change: transform;
        }

        /* تحسين إمكانية الوصول */
        @media (prefers-reduced-motion: reduce) {
            * {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* تحسين التباين للقراءة */
        @media (prefers-contrast: high) {
            :root {
                --glass-bg: rgba(255, 255, 255, 0.2);
                --glass-bg-light: rgba(255, 255, 255, 0.25);
                --glass-bg-heavy: rgba(255, 255, 255, 0.3);
                --glass-border: rgba(255, 255, 255, 0.4);
                --glass-border-light: rgba(255, 255, 255, 0.5);
                --text-primary: rgba(255, 255, 255, 1);
                --text-secondary: rgba(255, 255, 255, 0.9);
                --text-muted: rgba(255, 255, 255, 0.8);
            }
        }

        /* تحسين للشاشات عالية الكثافة */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
            .card {
                border-width: 0.5px;
            }
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-right: 0;
            }

            .card {
                margin: 0.5rem;
                border-radius: var(--border-radius-small);
            }

            .btn-primary {
                padding: 0.625rem 1.25rem;
                font-size: 0.8rem;
            }
        }
    </style>
    
    {% block extra_css %}{% endblock %}
</head>
<body class="bg-gray-50">
    {% if session.user_id %}
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="sidebar w-64 fixed h-full z-30">
            <!-- Logo -->
            <div class="p-6 border-b border-white border-opacity-20">
                <div class="flex items-center">
                    <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center ml-3 shadow-lg">
                        <i class="fas fa-cloud-upload-alt text-xl text-white"></i>
                    </div>
                    <div>
                        <h2 class="text-lg font-bold text-white">إدارة الاشتراكات</h2>
                        <p class="text-xs text-white text-opacity-70">نظام شامل ومتطور</p>
                    </div>
                </div>
                <div class="mt-4 h-1 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full"></div>
            </div>

            <!-- Navigation المحسن مع التفرع -->
            <nav class="p-4 space-y-1">
                <!-- لوحة التحكم -->
                <a href="{{ url_for('dashboard') }}" class="nav-item flex items-center p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint == 'dashboard' %}active text-white{% else %}text-white text-opacity-80 hover:text-white{% endif %}">
                    <div class="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center ml-3">
                        <i class="fas fa-tachometer-alt text-sm"></i>
                    </div>
                    <span>لوحة المعلومات</span>
                    {% if request.endpoint == 'dashboard' %}
                    <div class="mr-auto w-2 h-2 bg-white rounded-full animate-pulse"></div>
                    {% endif %}
                </a>

                <!-- قسم الاشتراكات مع التفرع -->
                <div class="nav-group">
                    <div class="nav-parent cursor-pointer" onclick="toggleSubmenu('subscriptions-menu')">
                        <div class="nav-item flex items-center justify-between p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint in ['subscriptions', 'add_subscription', 'edit_subscription', 'subscription_chart', 'subscription_analytics', 'subscription_reports'] %}active text-white{% else %}text-white text-opacity-80 hover:text-white{% endif %}">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center ml-3">
                                    <i class="fas fa-server text-sm"></i>
                                </div>
                                <span>إدارة الاشتراكات</span>
                            </div>
                            <div class="flex items-center gap-2">
                                {% if request.endpoint in ['subscriptions', 'add_subscription', 'edit_subscription', 'subscription_chart', 'subscription_analytics', 'subscription_reports'] %}
                                <div class="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                                {% endif %}
                                <i class="fas fa-chevron-down transition-transform duration-300 text-xs" id="subscriptions-arrow"></i>
                            </div>
                        </div>
                    </div>

                    <div class="submenu overflow-hidden transition-all duration-300 max-h-0" id="subscriptions-menu">
                        <div class="mr-6 mt-2 space-y-2">
                            <a href="{{ url_for('subscription_chart') }}" class="nav-subitem flex items-center p-3 text-sm rounded-xl transition-all duration-300 {% if request.endpoint == 'subscription_chart' %}bg-white bg-opacity-20 text-white{% else %}text-white text-opacity-70 hover:bg-white hover:bg-opacity-10 hover:text-white{% endif %}">
                                <div class="w-6 h-6 bg-white bg-opacity-20 rounded-lg flex items-center justify-center ml-3">
                                    <i class="fas fa-chart-pie text-xs"></i>
                                </div>
                                <span>مخطط الاشتراكات</span>
                                {% if request.endpoint == 'subscription_chart' %}
                                <div class="mr-auto w-1.5 h-1.5 bg-white rounded-full animate-pulse"></div>
                                {% endif %}
                            </a>
                            <a href="{{ url_for('subscriptions') }}" class="nav-subitem flex items-center p-3 text-sm rounded-xl transition-all duration-300 {% if request.endpoint == 'subscriptions' %}bg-white bg-opacity-20 text-white{% else %}text-white text-opacity-70 hover:bg-white hover:bg-opacity-10 hover:text-white{% endif %}">
                                <div class="w-6 h-6 bg-white bg-opacity-20 rounded-lg flex items-center justify-center ml-3">
                                    <i class="fas fa-list text-xs"></i>
                                </div>
                                <span>قائمة الاشتراكات</span>
                                {% if request.endpoint == 'subscriptions' %}
                                <div class="mr-auto w-1.5 h-1.5 bg-white rounded-full animate-pulse"></div>
                                {% endif %}
                            </a>
                            <a href="{{ url_for('add_subscription') }}" class="nav-subitem flex items-center p-3 text-sm rounded-xl transition-all duration-300 {% if request.endpoint == 'add_subscription' %}bg-white bg-opacity-20 text-white{% else %}text-white text-opacity-70 hover:bg-white hover:bg-opacity-10 hover:text-white{% endif %}">
                                <div class="w-6 h-6 bg-white bg-opacity-20 rounded-lg flex items-center justify-center ml-3">
                                    <i class="fas fa-plus-circle text-xs"></i>
                                </div>
                                <span>إضافة اشتراك جديد</span>
                                {% if request.endpoint == 'add_subscription' %}
                                <div class="mr-auto w-1.5 h-1.5 bg-white rounded-full animate-pulse"></div>
                                {% endif %}
                            </a>
                            <a href="{{ url_for('subscription_analytics') }}" class="nav-subitem flex items-center p-3 text-sm rounded-xl transition-all duration-300 {% if request.endpoint == 'subscription_analytics' %}bg-white bg-opacity-20 text-white{% else %}text-white text-opacity-70 hover:bg-white hover:bg-opacity-10 hover:text-white{% endif %}">
                                <div class="w-6 h-6 bg-white bg-opacity-20 rounded-lg flex items-center justify-center ml-3">
                                    <i class="fas fa-chart-line text-xs"></i>
                                </div>
                                <span>تحليلات الاشتراكات</span>
                                {% if request.endpoint == 'subscription_analytics' %}
                                <div class="mr-auto w-1.5 h-1.5 bg-white rounded-full animate-pulse"></div>
                                {% endif %}
                            </a>
                            <a href="{{ url_for('subscription_reports') }}" class="nav-subitem flex items-center p-3 text-sm rounded-xl transition-all duration-300 {% if request.endpoint == 'subscription_reports' %}bg-white bg-opacity-20 text-white{% else %}text-white text-opacity-70 hover:bg-white hover:bg-opacity-10 hover:text-white{% endif %}">
                                <div class="w-6 h-6 bg-white bg-opacity-20 rounded-lg flex items-center justify-center ml-3">
                                    <i class="fas fa-file-chart-line text-xs"></i>
                                </div>
                                <span>تقارير الاشتراكات</span>
                                {% if request.endpoint == 'subscription_reports' %}
                                <div class="mr-auto w-1.5 h-1.5 bg-white rounded-full animate-pulse"></div>
                                {% endif %}
                            </a>
                        </div>
                    </div>
                </div>

                <!-- قسم الفواتير مع التفرع -->
                <div class="nav-group">
                    <div class="nav-parent cursor-pointer" onclick="toggleSubmenu('invoices-menu')">
                        <div class="nav-item flex items-center justify-between p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint in ['invoices', 'add_invoice', 'edit_invoice', 'invoice_reports'] %}active text-white{% else %}text-white text-opacity-80 hover:text-white{% endif %}">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center ml-3">
                                    <i class="fas fa-file-invoice text-sm"></i>
                                </div>
                                <span>إدارة الفواتير</span>
                            </div>
                            <div class="flex items-center gap-2">
                                {% if request.endpoint in ['invoices', 'add_invoice', 'edit_invoice', 'invoice_reports'] %}
                                <div class="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                                {% endif %}
                                <i class="fas fa-chevron-down transition-transform duration-300 text-xs" id="invoices-arrow"></i>
                            </div>
                        </div>
                    </div>

                    <div class="submenu overflow-hidden transition-all duration-300 max-h-0" id="invoices-menu">
                        <div class="mr-6 mt-2 space-y-2">
                            <a href="{{ url_for('invoices') }}" class="nav-subitem flex items-center p-3 text-sm rounded-xl transition-all duration-300 {% if request.endpoint == 'invoices' %}bg-white bg-opacity-20 text-white{% else %}text-white text-opacity-70 hover:bg-white hover:bg-opacity-10 hover:text-white{% endif %}">
                                <div class="w-6 h-6 bg-white bg-opacity-20 rounded-lg flex items-center justify-center ml-3">
                                    <i class="fas fa-list-alt text-xs"></i>
                                </div>
                                <span>قائمة الفواتير</span>
                                {% if request.endpoint == 'invoices' %}
                                <div class="mr-auto w-1.5 h-1.5 bg-white rounded-full animate-pulse"></div>
                                {% endif %}
                            </a>
                            <a href="{{ url_for('add_invoice') }}" class="nav-subitem flex items-center p-3 text-sm rounded-xl transition-all duration-300 {% if request.endpoint == 'add_invoice' %}bg-white bg-opacity-20 text-white{% else %}text-white text-opacity-70 hover:bg-white hover:bg-opacity-10 hover:text-white{% endif %}">
                                <div class="w-6 h-6 bg-white bg-opacity-20 rounded-lg flex items-center justify-center ml-3">
                                    <i class="fas fa-plus-square text-xs"></i>
                                </div>
                                <span>إنشاء فاتورة جديدة</span>
                                {% if request.endpoint == 'add_invoice' %}
                                <div class="mr-auto w-1.5 h-1.5 bg-white rounded-full animate-pulse"></div>
                                {% endif %}
                            </a>
                            <a href="{{ url_for('invoice_reports') }}" class="nav-subitem flex items-center p-3 text-sm rounded-xl transition-all duration-300 {% if request.endpoint == 'invoice_reports' %}bg-white bg-opacity-20 text-white{% else %}text-white text-opacity-70 hover:bg-white hover:bg-opacity-10 hover:text-white{% endif %}">
                                <div class="w-6 h-6 bg-white bg-opacity-20 rounded-lg flex items-center justify-center ml-3">
                                    <i class="fas fa-chart-bar text-xs"></i>
                                </div>
                                <span>تقارير الفواتير</span>
                                {% if request.endpoint == 'invoice_reports' %}
                                <div class="mr-auto w-1.5 h-1.5 bg-white rounded-full animate-pulse"></div>
                                {% endif %}
                            </a>
                        </div>
                    </div>
                </div>

                <!-- قسم التواصل والإيميل -->
                <div class="nav-group">
                    <div class="nav-parent cursor-pointer" onclick="toggleSubmenu('email-menu')">
                        <div class="nav-item flex items-center justify-between p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint in ['email_center', 'send_email', 'email_templates'] %}active bg-blue-600 text-white{% else %}text-gray-600 hover:bg-gray-100{% endif %}">
                            <div class="flex items-center">
                                <i class="fas fa-envelope w-5 h-5 ml-3"></i>
                                <span>مركز التواصل</span>
                            </div>
                            <i class="fas fa-chevron-down transition-transform duration-300" id="email-arrow"></i>
                        </div>
                    </div>

                    <div class="submenu overflow-hidden transition-all duration-300 max-h-0" id="email-menu">
                        <div class="mr-6 mt-1 space-y-1">
                            <a href="{{ url_for('email_center') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'email_center' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-inbox w-4 h-4 ml-2"></i>
                                <span>مركز الرسائل</span>
                            </a>
                            <a href="{{ url_for('send_email') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'send_email' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-paper-plane w-4 h-4 ml-2"></i>
                                <span>إرسال رسالة</span>
                            </a>
                            <a href="{{ url_for('email_templates') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'email_templates' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-file-alt w-4 h-4 ml-2"></i>
                                <span>قوالب الرسائل</span>
                            </a>
                        </div>
                    </div>
                </div>

                <!-- التقارير -->
                <a href="{{ url_for('reports') }}" class="nav-item flex items-center p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint == 'reports' %}active bg-blue-600 text-white{% else %}text-gray-600 hover:bg-gray-100{% endif %}">
                    <i class="fas fa-chart-bar w-5 h-5 ml-3"></i>
                    التقارير العامة
                </a>

                {% if session.role == 'admin' %}
                <!-- إدارة المستخدمين -->
                <a href="{{ url_for('user_management') }}" class="nav-item flex items-center p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint in ['user_management', 'add_user', 'edit_user'] %}active text-white{% else %}text-white text-opacity-80 hover:text-white{% endif %}">
                    <div class="w-8 h-8 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center ml-3">
                        <i class="fas fa-users-cog text-sm"></i>
                    </div>
                    <span>إدارة المستخدمين</span>
                    {% if request.endpoint in ['user_management', 'add_user', 'edit_user'] %}
                    <div class="mr-auto w-2 h-2 bg-white rounded-full animate-pulse"></div>
                    {% endif %}
                </a>
                {% endif %}

                <!-- لوحة التحكم الذكية -->
                <a href="{{ url_for('smart_dashboard') }}" class="nav-item flex items-center p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint == 'smart_dashboard' %}active text-white{% else %}text-white text-opacity-80 hover:text-white{% endif %}">
                    <div class="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center ml-3">
                        <i class="fas fa-brain text-sm"></i>
                    </div>
                    <span>لوحة التحكم الذكية</span>
                    {% if request.endpoint == 'smart_dashboard' %}
                    <div class="mr-auto w-2 h-2 bg-white rounded-full animate-pulse"></div>
                    {% endif %}
                </a>

                <!-- تخصيص الواجهة -->
                <a href="{{ url_for('ui_customizer') }}" class="nav-item flex items-center p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint == 'ui_customizer' %}active text-white{% else %}text-white text-opacity-80 hover:text-white{% endif %}">
                    <div class="w-8 h-8 bg-gradient-to-r from-orange-500 to-red-500 rounded-lg flex items-center justify-center ml-3">
                        <i class="fas fa-palette text-sm"></i>
                    </div>
                    <span>تخصيص الواجهة</span>
                    {% if request.endpoint == 'ui_customizer' %}
                    <div class="mr-auto w-2 h-2 bg-white rounded-full animate-pulse"></div>
                    {% endif %}
                </a>

                <!-- مركز الحماية -->
                <a href="{{ url_for('security_center') }}" class="nav-item flex items-center p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint == 'security_center' %}active text-white{% else %}text-white text-opacity-80 hover:text-white{% endif %}">
                    <div class="w-8 h-8 bg-gradient-to-r from-red-500 to-pink-500 rounded-lg flex items-center justify-center ml-3">
                        <i class="fas fa-shield-alt text-sm"></i>
                    </div>
                    <span>مركز الحماية</span>
                    {% if request.endpoint == 'security_center' %}
                    <div class="mr-auto w-2 h-2 bg-white rounded-full animate-pulse"></div>
                    {% endif %}
                </a>

                <!-- التقارير المتقدمة -->
                <a href="{{ url_for('advanced_reports') }}" class="nav-item flex items-center p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint == 'advanced_reports' %}active text-white{% else %}text-white text-opacity-80 hover:text-white{% endif %}">
                    <div class="w-8 h-8 bg-gradient-to-r from-green-500 to-blue-500 rounded-lg flex items-center justify-center ml-3">
                        <i class="fas fa-chart-pie text-sm"></i>
                    </div>
                    <span>التقارير المتقدمة</span>
                    {% if request.endpoint == 'advanced_reports' %}
                    <div class="mr-auto w-2 h-2 bg-white rounded-full animate-pulse"></div>
                    {% endif %}
                </a>

                <!-- قسم الإدارة المتقدمة -->
                {% if session.role == 'admin' %}
                <div class="nav-group">
                    <div class="nav-parent cursor-pointer" onclick="toggleSubmenu('admin-menu')">
                        <div class="nav-item flex items-center justify-between p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint in ['settings', 'activity_logs', 'system_health'] %}active bg-blue-600 text-white{% else %}text-gray-600 hover:bg-gray-100{% endif %}">
                            <div class="flex items-center">
                                <i class="fas fa-shield-alt w-5 h-5 ml-3"></i>
                                <span>الإدارة المتقدمة</span>
                            </div>
                            <i class="fas fa-chevron-down transition-transform duration-300" id="admin-arrow"></i>
                        </div>
                    </div>

                    <div class="submenu overflow-hidden transition-all duration-300 max-h-0" id="admin-menu">
                        <div class="mr-6 mt-1 space-y-1">
                            <a href="{{ url_for('settings') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'settings' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-cog w-4 h-4 ml-2"></i>
                                <span>إعدادات النظام</span>
                            </a>
                            <a href="{{ url_for('activity_logs') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'activity_logs' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-history w-4 h-4 ml-2"></i>
                                <span>سجل الأنشطة</span>
                            </a>
                            <a href="{{ url_for('system_health') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors {% if request.endpoint == 'system_health' %}bg-blue-100 text-blue-700{% else %}text-gray-600 hover:bg-gray-50{% endif %}">
                                <i class="fas fa-heartbeat w-4 h-4 ml-2"></i>
                                <span>صحة النظام</span>
                            </a>
                            <a href="{{ url_for('create_backup_route') }}" class="nav-subitem flex items-center p-2 text-sm rounded-md transition-colors text-gray-600 hover:bg-gray-50">
                                <i class="fas fa-save w-4 h-4 ml-2"></i>
                                <span>نسخة احتياطية</span>
                            </a>
                        </div>
                    </div>
                </div>
                {% else %}
                <!-- الإعدادات للمستخدمين العاديين -->
                <a href="{{ url_for('settings') }}" class="nav-item flex items-center p-3 text-sm font-medium rounded-lg transition-all duration-200 {% if request.endpoint == 'settings' %}active bg-blue-600 text-white{% else %}text-gray-600 hover:bg-gray-100{% endif %}">
                    <i class="fas fa-cog w-5 h-5 ml-3"></i>
                    الإعدادات
                </a>
                {% endif %}
            </nav>

            <!-- User Profile -->
            <div class="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                        {{ session.username[0].upper() }}
                    </div>
                    <div class="mr-3 flex-1">
                        <p class="text-sm font-medium text-gray-900">{{ session.username }}</p>
                        <p class="text-xs text-gray-500">{{ session.role }}</p>
                    </div>
                    <a href="{{ url_for('logout') }}" class="text-gray-400 hover:text-red-500 transition-colors">
                        <i class="fas fa-sign-out-alt"></i>
                    </a>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 mr-64">
            <!-- Header -->
            <header class="bg-white bg-opacity-10 backdrop-filter backdrop-blur-lg border-b border-white border-opacity-20 p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
                            <i class="fas fa-chart-line text-white text-lg"></i>
                        </div>
                        <div>
                            <h1 class="text-2xl font-bold text-white">{% block page_title %}لوحة التحكم{% endblock %}</h1>
                            <p class="text-white text-opacity-70">{% block page_description %}مرحباً بك في نظام إدارة الاشتراكات{% endblock %}</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4 space-x-reverse">
                        <!-- Notification Bell -->
                        <div class="relative">
                            <button class="w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center text-white hover:bg-opacity-30 transition-all duration-300">
                                <i class="fas fa-bell"></i>
                            </button>
                            <div class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                                <span class="text-xs text-white font-bold">3</span>
                            </div>
                        </div>

                        <!-- Search Button -->
                        <button class="w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center text-white hover:bg-opacity-30 transition-all duration-300">
                            <i class="fas fa-search"></i>
                        </button>

                        <!-- Settings Button -->
                        <button class="w-10 h-10 bg-white bg-opacity-20 rounded-xl flex items-center justify-center text-white hover:bg-opacity-30 transition-all duration-300">
                            <i class="fas fa-cog"></i>
                        </button>

                        {% block header_actions %}{% endblock %}
                    </div>
                </div>
            </header>

            <!-- Content -->
            <main class="p-6 min-h-screen">
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        <div class="fixed top-4 right-4 z-50 space-y-2">
                            {% for category, message in messages %}
                                <div class="alert alert-{{ 'error' if category == 'error' else category }} max-w-md transform transition-all duration-500 ease-in-out">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center ml-3">
                                            <i class="fas fa-{{ 'exclamation-circle' if category == 'error' else 'check-circle' if category == 'success' else 'info-circle' }} text-sm"></i>
                                        </div>
                                        <span class="flex-1">{{ message }}</span>
                                        <button onclick="this.parentElement.parentElement.remove()" class="w-6 h-6 bg-white bg-opacity-20 rounded-lg flex items-center justify-center hover:bg-opacity-30 transition-all duration-300">
                                            <i class="fas fa-times text-xs"></i>
                                        </button>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                {% endwith %}

                <!-- Content Area -->
                <div class="space-y-6">
                    {% block content %}{% endblock %}
                </div>
            </main>
        </div>
    </div>
    {% else %}
        {% block full_content %}{% endblock %}
    {% endif %}

    <!-- JavaScript للتحكم في القوائم المنسدلة والتفاعلات المتقدمة -->
    <script>
        // تبديل القوائم الفرعية مع تأثيرات محسنة
        function toggleSubmenu(menuId) {
            const menu = document.getElementById(menuId);
            const arrow = document.getElementById(menuId.replace('-menu', '-arrow'));
            const parent = menu.closest('.nav-group');

            if (menu.style.maxHeight === '0px' || menu.style.maxHeight === '') {
                // فتح القائمة مع تأثير
                menu.style.maxHeight = menu.scrollHeight + 'px';
                menu.style.opacity = '1';
                if (arrow) {
                    arrow.style.transform = 'rotate(180deg)';
                    arrow.classList.add('text-white');
                }
                parent.classList.add('expanded');

                // تأثير الانزلاق
                setTimeout(() => {
                    menu.style.transform = 'translateX(0)';
                }, 100);
            } else {
                // إغلاق القائمة مع تأثير
                menu.style.maxHeight = '0px';
                menu.style.opacity = '0';
                menu.style.transform = 'translateX(-10px)';
                if (arrow) {
                    arrow.style.transform = 'rotate(0deg)';
                    arrow.classList.remove('text-white');
                }
                parent.classList.remove('expanded');
            }
        }

        // تأثيرات التحميل والانيميشن
        document.addEventListener('DOMContentLoaded', function() {
            // تأثير التحميل التدريجي للعناصر
            const animateElements = document.querySelectorAll('.nav-item, .card, .btn-primary');
            animateElements.forEach((element, index) => {
                element.style.opacity = '0';
                element.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    element.style.transition = 'all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275)';
                    element.style.opacity = '1';
                    element.style.transform = 'translateY(0)';
                }, index * 100);
            });

            // تأثير الماوس للكروت
            document.querySelectorAll('.card').forEach(card => {
                card.addEventListener('mousemove', function(e) {
                    const rect = card.getBoundingClientRect();
                    const x = e.clientX - rect.left;
                    const y = e.clientY - rect.top;

                    const centerX = rect.width / 2;
                    const centerY = rect.height / 2;

                    const rotateX = (y - centerY) / 10;
                    const rotateY = (centerX - x) / 10;

                    card.style.transform = `perspective(1000px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) translateZ(10px)`;
                });

                card.addEventListener('mouseleave', function() {
                    card.style.transform = 'perspective(1000px) rotateX(0) rotateY(0) translateZ(0)';
                });
            });

            // إخفاء التنبيهات تلقائياً
            setTimeout(() => {
                document.querySelectorAll('.alert').forEach(alert => {
                    alert.style.transition = 'all 0.5s ease-out';
                    alert.style.opacity = '0';
                    alert.style.transform = 'translateX(100%)';
                    setTimeout(() => alert.remove(), 500);
                });
            }, 5000);
        });

        // فتح القائمة الفرعية إذا كانت الصفحة الحالية ضمنها
        document.addEventListener('DOMContentLoaded', function() {
            // فحص قائمة الاشتراكات
            const subscriptionsMenu = document.getElementById('subscriptions-menu');
            const subscriptionsArrow = document.getElementById('subscriptions-arrow');
            if (subscriptionsMenu && subscriptionsMenu.querySelector('.bg-blue-100')) {
                subscriptionsMenu.style.maxHeight = subscriptionsMenu.scrollHeight + 'px';
                if (subscriptionsArrow) subscriptionsArrow.style.transform = 'rotate(180deg)';
            }

            // فحص قائمة الفواتير
            const invoicesMenu = document.getElementById('invoices-menu');
            const invoicesArrow = document.getElementById('invoices-arrow');
            if (invoicesMenu && invoicesMenu.querySelector('.bg-blue-100')) {
                invoicesMenu.style.maxHeight = invoicesMenu.scrollHeight + 'px';
                if (invoicesArrow) invoicesArrow.style.transform = 'rotate(180deg)';
            }

            // فحص قائمة الإيميل
            const emailMenu = document.getElementById('email-menu');
            const emailArrow = document.getElementById('email-arrow');
            if (emailMenu && emailMenu.querySelector('.bg-blue-100')) {
                emailMenu.style.maxHeight = emailMenu.scrollHeight + 'px';
                if (emailArrow) emailArrow.style.transform = 'rotate(180deg)';
            }

            // فحص قائمة الإدارة المتقدمة
            const adminMenu = document.getElementById('admin-menu');
            const adminArrow = document.getElementById('admin-arrow');
            if (adminMenu && adminMenu.querySelector('.bg-blue-100')) {
                adminMenu.style.maxHeight = adminMenu.scrollHeight + 'px';
                if (adminArrow) adminArrow.style.transform = 'rotate(180deg)';
            }
        });

        // إضافة تأثيرات hover للقوائم
        document.querySelectorAll('.nav-item').forEach(item => {
            item.addEventListener('mouseenter', function() {
                if (!this.classList.contains('active')) {
                    this.style.transform = 'translateX(4px)';
                }
            });

            item.addEventListener('mouseleave', function() {
                if (!this.classList.contains('active')) {
                    this.style.transform = 'translateX(0)';
                }
            });
        });

        // نظام إدارة التفضيلات السريع
        class PreferencesManager {
            constructor() {
                this.preferences = this.loadPreferences();
                this.autoSaveDelay = 300; // 300ms للحفظ السريع
                this.saveTimeout = null;
                this.init();
            }

            init() {
                this.applyPreferences();
                this.setupAutoSave();
            }

            loadPreferences() {
                try {
                    const stored = localStorage.getItem('user_preferences_v2');
                    return stored ? JSON.parse(stored) : {};
                } catch (error) {
                    console.warn('خطأ في تحميل التفضيلات:', error);
                    return {};
                }
            }

            savePreferences() {
                try {
                    localStorage.setItem('user_preferences_v2', JSON.stringify(this.preferences));
                    this.syncWithServer();
                } catch (error) {
                    console.warn('خطأ في حفظ التفضيلات:', error);
                }
            }

            async syncWithServer() {
                try {
                    await fetch('/api/user/preferences', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(this.preferences)
                    });
                } catch (error) {
                    // تجاهل أخطاء الشبكة - سيتم المحاولة لاحقاً
                }
            }

            setPreference(key, value) {
                this.preferences[key] = value;
                this.scheduleAutoSave();
                this.applyPreference(key, value);
            }

            scheduleAutoSave() {
                clearTimeout(this.saveTimeout);
                this.saveTimeout = setTimeout(() => {
                    this.savePreferences();
                }, this.autoSaveDelay);
            }

            applyPreferences() {
                Object.keys(this.preferences).forEach(key => {
                    this.applyPreference(key, this.preferences[key]);
                });
            }

            applyPreference(key, value) {
                switch (key) {
                    case 'sidebar_collapsed':
                        const sidebar = document.querySelector('.sidebar');
                        if (sidebar) {
                            sidebar.classList.toggle('collapsed', value);
                        }
                        break;
                    case 'animations_enabled':
                        document.documentElement.style.setProperty('--animation-speed', value ? '1' : '0');
                        break;
                    case 'high_contrast':
                        document.documentElement.classList.toggle('high-contrast', value);
                        break;
                    case 'reduced_motion':
                        document.documentElement.classList.toggle('reduced-motion', value);
                        break;
                }
            }

            setupAutoSave() {
                // مراقبة تغييرات الواجهة وحفظها تلقائياً
                const observer = new MutationObserver((mutations) => {
                    mutations.forEach((mutation) => {
                        if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                            const target = mutation.target;
                            if (target.classList.contains('sidebar')) {
                                this.setPreference('sidebar_collapsed', target.classList.contains('collapsed'));
                            }
                        }
                    });
                });

                // مراقبة الشريط الجانبي
                const sidebar = document.querySelector('.sidebar');
                if (sidebar) {
                    observer.observe(sidebar, { attributes: true, attributeFilter: ['class'] });
                }
            }
        }

        // تهيئة مدير التفضيلات
        const preferencesManager = new PreferencesManager();

        // نظام إشعارات محسن
        function showNotification(message, type = 'info', duration = 4000) {
            const notification = document.createElement('div');
            notification.className = `alert alert-${type} notification-toast`;
            notification.innerHTML = `
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-white bg-opacity-20 rounded-lg flex items-center justify-center ml-3">
                        <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle text-sm"></i>
                    </div>
                    <span class="flex-1">${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="w-6 h-6 bg-white bg-opacity-20 rounded-lg flex items-center justify-center hover:bg-opacity-30 transition-all duration-300">
                        <i class="fas fa-times text-xs"></i>
                    </button>
                </div>
            `;

            notification.style.cssText = `
                position: fixed;
                top: 1rem;
                right: 1rem;
                z-index: 9999;
                max-width: 24rem;
                transform: translateX(100%);
                transition: transform 0.3s ease;
                animation: slideInRight 0.3s ease forwards;
            `;

            document.body.appendChild(notification);

            // إخفاء الإشعار
            setTimeout(() => {
                notification.style.animation = 'slideOutRight 0.3s ease forwards';
                setTimeout(() => notification.remove(), 300);
            }, duration);
        }

        // تحسين الأداء للعناصر التفاعلية
        function optimizeInteractiveElements() {
            const interactiveElements = document.querySelectorAll('.card, .btn-primary, .nav-item');
            interactiveElements.forEach(element => {
                element.addEventListener('mouseenter', function() {
                    this.style.willChange = 'transform';
                }, { passive: true });

                element.addEventListener('mouseleave', function() {
                    this.style.willChange = 'auto';
                }, { passive: true });
            });
        }

        // تحسين معالجة الأزرار
        function enhanceButtons() {
            const buttons = document.querySelectorAll('button[type="submit"], .btn-primary');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    if (this.form && this.form.checkValidity()) {
                        const originalText = this.innerHTML;
                        this.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i> جاري المعالجة...';
                        this.disabled = true;

                        // إعادة تعيين الزر بعد 8 ثوان
                        setTimeout(() => {
                            this.innerHTML = originalText;
                            this.disabled = false;
                        }, 8000);
                    }
                }, { passive: false });
            });
        }

        // تهيئة التحسينات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            optimizeInteractiveElements();
            enhanceButtons();

            // تحسين التمرير
            if ('scrollBehavior' in document.documentElement.style) {
                document.documentElement.style.scrollBehavior = 'smooth';
            }
        });

        // تصدير الدوال للاستخدام العام
        window.preferencesManager = preferencesManager;
        window.showNotification = showNotification;
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
