{% extends "base.html" %}

{% block title %}إعدادات التنقل والقوائم - نظام إدارة الاشتراكات{% endblock %}
{% block page_title %}إعدادات التنقل والقوائم{% endblock %}
{% block page_description %}تخصيص شامل للتنقل والقوائم والواجهة{% endblock %}

{% block extra_css %}
<style>
.navigation-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.nav-section {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 2rem;
}

.section-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--glass-border);
}

.section-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 8px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.section-title {
    color: var(--text-primary);
    font-size: 1.25rem;
    font-weight: 700;
    margin: 0;
}

.menu-items {
    list-style: none;
    padding: 0;
    margin: 0;
}

.menu-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    margin-bottom: 0.75rem;
    transition: all var(--transition-medium);
}

.menu-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateX(-4px);
}

.menu-item-content {
    display: flex;
    align-items: center;
    padding: 1rem;
    gap: 1rem;
}

.menu-item-icon {
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    color: white;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.menu-item-info {
    flex: 1;
}

.menu-item-title {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.menu-item-description {
    color: var(--text-secondary);
    font-size: 0.75rem;
}

.menu-item-controls {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.toggle-switch {
    position: relative;
    width: 44px;
    height: 24px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    cursor: pointer;
    transition: all var(--transition-medium);
}

.toggle-switch.active {
    background: var(--success-color);
}

.toggle-switch::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: all var(--transition-medium);
}

.toggle-switch.active::before {
    transform: translateX(20px);
}

.drag-handle {
    cursor: grab;
    color: var(--text-muted);
    font-size: 1rem;
}

.drag-handle:active {
    cursor: grabbing;
}

.layout-options {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
}

.layout-option {
    padding: 1.5rem;
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-medium);
    background: rgba(255, 255, 255, 0.05);
}

.layout-option:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.layout-option.active {
    background: rgba(59, 130, 246, 0.1);
    border-color: var(--primary-color);
}

.layout-preview {
    width: 60px;
    height: 40px;
    margin: 0 auto 1rem;
    border: 2px solid var(--glass-border);
    border-radius: 4px;
    position: relative;
    background: rgba(255, 255, 255, 0.1);
}

.layout-preview::before,
.layout-preview::after {
    content: '';
    position: absolute;
    background: var(--primary-color);
    border-radius: 1px;
}

.layout-option[data-layout="sidebar"]::before {
    top: 4px;
    left: 4px;
    width: 12px;
    height: 30px;
}

.layout-option[data-layout="sidebar"]::after {
    top: 4px;
    right: 4px;
    width: 40px;
    height: 8px;
}

.layout-option[data-layout="top"]::before {
    top: 4px;
    left: 4px;
    width: 50px;
    height: 8px;
}

.layout-option[data-layout="top"]::after {
    bottom: 4px;
    left: 4px;
    width: 50px;
    height: 24px;
}

.layout-option[data-layout="minimal"]::before {
    top: 4px;
    left: 4px;
    width: 50px;
    height: 4px;
}

.layout-option[data-layout="minimal"]::after {
    top: 12px;
    left: 4px;
    width: 50px;
    height: 20px;
}

.layout-name {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.875rem;
    margin-bottom: 0.25rem;
}

.layout-description {
    color: var(--text-secondary);
    font-size: 0.75rem;
}

.quick-settings {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
    margin-bottom: 2rem;
}

.quick-setting {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: 8px;
}

.quick-setting-label {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.875rem;
}

.breadcrumb-settings {
    margin-bottom: 2rem;
}

.breadcrumb-preview {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
}

.breadcrumb-items {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.breadcrumb-item {
    color: var(--text-primary);
}

.breadcrumb-separator {
    color: var(--text-muted);
}

.action-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    grid-column: 1 / -1;
}

.action-btn {
    flex: 1;
    padding: 0.75rem 1.5rem;
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    font-weight: 600;
    cursor: pointer;
    transition: all var(--transition-medium);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.action-btn.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-color: var(--primary-color);
}

.action-btn.success {
    background: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

.sortable-ghost {
    opacity: 0.5;
}

.sortable-chosen {
    transform: scale(1.02);
}

@media (max-width: 768px) {
    .navigation-container {
        grid-template-columns: 1fr;
    }
    
    .layout-options {
        grid-template-columns: 1fr;
    }
    
    .quick-settings {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="navigation-container">
    <!-- إعدادات القائمة الجانبية -->
    <div class="nav-section">
        <div class="section-header">
            <div class="section-icon">
                <i class="fas fa-bars"></i>
            </div>
            <h3 class="section-title">القائمة الجانبية</h3>
        </div>
        
        <ul class="menu-items" id="sidebarItems">
            <li class="menu-item" data-item="dashboard">
                <div class="menu-item-content">
                    <div class="menu-item-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                        <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <div class="menu-item-info">
                        <div class="menu-item-title">لوحة التحكم</div>
                        <div class="menu-item-description">الصفحة الرئيسية والإحصائيات</div>
                    </div>
                    <div class="menu-item-controls">
                        <div class="toggle-switch active" data-toggle="dashboard"></div>
                        <div class="drag-handle">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                    </div>
                </div>
            </li>
            
            <li class="menu-item" data-item="subscriptions">
                <div class="menu-item-content">
                    <div class="menu-item-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="menu-item-info">
                        <div class="menu-item-title">إدارة الاشتراكات</div>
                        <div class="menu-item-description">عرض وإدارة جميع الاشتراكات</div>
                    </div>
                    <div class="menu-item-controls">
                        <div class="toggle-switch active" data-toggle="subscriptions"></div>
                        <div class="drag-handle">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                    </div>
                </div>
            </li>
            
            <li class="menu-item" data-item="advanced-users">
                <div class="menu-item-content">
                    <div class="menu-item-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                        <i class="fas fa-users-cog"></i>
                    </div>
                    <div class="menu-item-info">
                        <div class="menu-item-title">إدارة المستخدمين</div>
                        <div class="menu-item-description">إدارة متقدمة للمستخدمين والأذونات</div>
                    </div>
                    <div class="menu-item-controls">
                        <div class="toggle-switch active" data-toggle="advanced-users"></div>
                        <div class="drag-handle">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                    </div>
                </div>
            </li>
            
            <li class="menu-item" data-item="distributors">
                <div class="menu-item-content">
                    <div class="menu-item-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                        <i class="fas fa-store"></i>
                    </div>
                    <div class="menu-item-info">
                        <div class="menu-item-title">إدارة الموزعين</div>
                        <div class="menu-item-description">إدارة الموزعين والباقات</div>
                    </div>
                    <div class="menu-item-controls">
                        <div class="toggle-switch active" data-toggle="distributors"></div>
                        <div class="drag-handle">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                    </div>
                </div>
            </li>
            
            <li class="menu-item" data-item="reports">
                <div class="menu-item-content">
                    <div class="menu-item-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626);">
                        <i class="fas fa-chart-bar"></i>
                    </div>
                    <div class="menu-item-info">
                        <div class="menu-item-title">التقارير المتقدمة</div>
                        <div class="menu-item-description">تقارير شاملة وتحليلات</div>
                    </div>
                    <div class="menu-item-controls">
                        <div class="toggle-switch active" data-toggle="reports"></div>
                        <div class="drag-handle">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                    </div>
                </div>
            </li>
            
            <li class="menu-item" data-item="theme-settings">
                <div class="menu-item-content">
                    <div class="menu-item-icon" style="background: linear-gradient(135deg, #ec4899, #be185d);">
                        <i class="fas fa-palette"></i>
                    </div>
                    <div class="menu-item-info">
                        <div class="menu-item-title">إعدادات الألوان</div>
                        <div class="menu-item-description">تخصيص الألوان والثيمات</div>
                    </div>
                    <div class="menu-item-controls">
                        <div class="toggle-switch active" data-toggle="theme-settings"></div>
                        <div class="drag-handle">
                            <i class="fas fa-grip-vertical"></i>
                        </div>
                    </div>
                </div>
            </li>
        </ul>
    </div>

    <!-- إعدادات التخطيط -->
    <div class="nav-section">
        <div class="section-header">
            <div class="section-icon">
                <i class="fas fa-layout"></i>
            </div>
            <h3 class="section-title">تخطيط الواجهة</h3>
        </div>
        
        <div class="layout-options">
            <div class="layout-option active" data-layout="sidebar">
                <div class="layout-preview"></div>
                <div class="layout-name">القائمة الجانبية</div>
                <div class="layout-description">تخطيط كلاسيكي مع قائمة جانبية</div>
            </div>
            
            <div class="layout-option" data-layout="top">
                <div class="layout-preview"></div>
                <div class="layout-name">القائمة العلوية</div>
                <div class="layout-description">قائمة أفقية في الأعلى</div>
            </div>
            
            <div class="layout-option" data-layout="minimal">
                <div class="layout-preview"></div>
                <div class="layout-name">التخطيط المبسط</div>
                <div class="layout-description">واجهة نظيفة ومبسطة</div>
            </div>
        </div>
        
        <div class="quick-settings">
            <div class="quick-setting">
                <span class="quick-setting-label">القائمة المطوية</span>
                <div class="toggle-switch" data-setting="collapsed"></div>
            </div>
            
            <div class="quick-setting">
                <span class="quick-setting-label">الوضع المظلم</span>
                <div class="toggle-switch active" data-setting="dark-mode"></div>
            </div>
            
            <div class="quick-setting">
                <span class="quick-setting-label">الرسوم المتحركة</span>
                <div class="toggle-switch active" data-setting="animations"></div>
            </div>
            
            <div class="quick-setting">
                <span class="quick-setting-label">الأصوات</span>
                <div class="toggle-switch" data-setting="sounds"></div>
            </div>
        </div>
        
        <div class="breadcrumb-settings">
            <h4 style="color: var(--text-primary); margin-bottom: 1rem;">مسار التنقل</h4>
            <div class="breadcrumb-preview">
                <div class="breadcrumb-items">
                    <span class="breadcrumb-item">الرئيسية</span>
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-item">إدارة المستخدمين</span>
                    <span class="breadcrumb-separator">/</span>
                    <span class="breadcrumb-item">إضافة مستخدم</span>
                </div>
            </div>
            
            <div class="quick-setting">
                <span class="quick-setting-label">إظهار مسار التنقل</span>
                <div class="toggle-switch active" data-setting="breadcrumbs"></div>
            </div>
        </div>
    </div>
</div>

<div class="action-buttons">
    <button class="action-btn" onclick="resetSettings()">
        <i class="fas fa-undo"></i>
        إعادة تعيين
    </button>
    <button class="action-btn" onclick="previewChanges()">
        <i class="fas fa-eye"></i>
        معاينة
    </button>
    <button class="action-btn primary" onclick="saveSettings()">
        <i class="fas fa-save"></i>
        حفظ الإعدادات
    </button>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js"></script>
<script>
// إعدادات التنقل والقوائم
document.addEventListener('DOMContentLoaded', function() {
    initializeNavigationSettings();
});

function initializeNavigationSettings() {
    // تفعيل السحب والإفلات للقائمة
    const sidebarItems = document.getElementById('sidebarItems');
    Sortable.create(sidebarItems, {
        animation: 150,
        ghostClass: 'sortable-ghost',
        chosenClass: 'sortable-chosen',
        onEnd: function(evt) {
            updateMenuOrder();
        }
    });
    
    // ربط أحداث التبديل
    document.querySelectorAll('.toggle-switch').forEach(toggle => {
        toggle.addEventListener('click', function() {
            this.classList.toggle('active');
            handleToggleChange(this);
        });
    });
    
    // ربط أحداث تخطيط الواجهة
    document.querySelectorAll('.layout-option').forEach(option => {
        option.addEventListener('click', function() {
            document.querySelectorAll('.layout-option').forEach(opt => opt.classList.remove('active'));
            this.classList.add('active');
            changeLayout(this.dataset.layout);
        });
    });
}

function handleToggleChange(toggle) {
    const setting = toggle.dataset.toggle || toggle.dataset.setting;
    const isActive = toggle.classList.contains('active');
    
    switch(setting) {
        case 'collapsed':
            toggleSidebarCollapse(isActive);
            break;
        case 'dark-mode':
            toggleDarkMode(isActive);
            break;
        case 'animations':
            toggleAnimations(isActive);
            break;
        case 'sounds':
            toggleSounds(isActive);
            break;
        case 'breadcrumbs':
            toggleBreadcrumbs(isActive);
            break;
        default:
            toggleMenuItem(setting, isActive);
    }
}

function toggleSidebarCollapse(collapsed) {
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
        if (collapsed) {
            sidebar.classList.add('collapsed');
        } else {
            sidebar.classList.remove('collapsed');
        }
    }
}

function toggleDarkMode(enabled) {
    document.documentElement.classList.toggle('dark-mode', enabled);
    localStorage.setItem('darkMode', enabled);
}

function toggleAnimations(enabled) {
    document.documentElement.classList.toggle('no-animations', !enabled);
    localStorage.setItem('animations', enabled);
}

function toggleSounds(enabled) {
    localStorage.setItem('sounds', enabled);
}

function toggleBreadcrumbs(enabled) {
    const breadcrumbs = document.querySelectorAll('.breadcrumb');
    breadcrumbs.forEach(breadcrumb => {
        breadcrumb.style.display = enabled ? 'flex' : 'none';
    });
    localStorage.setItem('breadcrumbs', enabled);
}

function toggleMenuItem(item, enabled) {
    const menuItem = document.querySelector(`[href*="${item}"]`);
    if (menuItem) {
        const parentLi = menuItem.closest('li');
        if (parentLi) {
            parentLi.style.display = enabled ? 'block' : 'none';
        }
    }
}

function changeLayout(layout) {
    const body = document.body;
    body.className = body.className.replace(/layout-\w+/g, '');
    body.classList.add(`layout-${layout}`);
    localStorage.setItem('layout', layout);
}

function updateMenuOrder() {
    const items = Array.from(document.querySelectorAll('#sidebarItems .menu-item'));
    const order = items.map(item => item.dataset.item);
    localStorage.setItem('menuOrder', JSON.stringify(order));
}

function resetSettings() {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟')) {
        // إعادة تعيين جميع التبديلات
        document.querySelectorAll('.toggle-switch').forEach(toggle => {
            toggle.classList.remove('active');
        });
        
        // تفعيل الإعدادات الافتراضية
        document.querySelector('[data-setting="dark-mode"]').classList.add('active');
        document.querySelector('[data-setting="animations"]').classList.add('active');
        document.querySelector('[data-setting="breadcrumbs"]').classList.add('active');
        
        // إعادة تعيين التخطيط
        document.querySelectorAll('.layout-option').forEach(opt => opt.classList.remove('active'));
        document.querySelector('[data-layout="sidebar"]').classList.add('active');
        
        // مسح التخزين المحلي
        localStorage.removeItem('menuOrder');
        localStorage.removeItem('layout');
        localStorage.removeItem('darkMode');
        localStorage.removeItem('animations');
        localStorage.removeItem('sounds');
        localStorage.removeItem('breadcrumbs');
        
        showNotification('تم إعادة تعيين الإعدادات بنجاح', 'success');
    }
}

function previewChanges() {
    showNotification('معاينة التغييرات...', 'info');
    
    // تطبيق التغييرات مؤقتاً
    const currentSettings = getCurrentSettings();
    applySettings(currentSettings);
    
    // إظهار نافذة المعاينة
    setTimeout(() => {
        if (confirm('هل تريد الاحتفاظ بهذه التغييرات؟')) {
            saveSettings();
        } else {
            // إرجاع الإعدادات السابقة
            location.reload();
        }
    }, 3000);
}

function saveSettings() {
    const settings = getCurrentSettings();
    
    fetch('/api/navigation/save', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(settings)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('تم حفظ إعدادات التنقل بنجاح', 'success');
            applySettings(settings);
        } else {
            showNotification('فشل في حفظ الإعدادات: ' + data.message, 'error');
        }
    })
    .catch(error => {
        showNotification('خطأ في الاتصال بالخادم', 'error');
    });
}

function getCurrentSettings() {
    const settings = {
        layout: document.querySelector('.layout-option.active').dataset.layout,
        menuItems: {},
        quickSettings: {},
        menuOrder: Array.from(document.querySelectorAll('#sidebarItems .menu-item')).map(item => item.dataset.item)
    };
    
    // جمع حالة عناصر القائمة
    document.querySelectorAll('[data-toggle]').forEach(toggle => {
        const item = toggle.dataset.toggle;
        settings.menuItems[item] = toggle.classList.contains('active');
    });
    
    // جمع الإعدادات السريعة
    document.querySelectorAll('[data-setting]').forEach(toggle => {
        const setting = toggle.dataset.setting;
        settings.quickSettings[setting] = toggle.classList.contains('active');
    });
    
    return settings;
}

function applySettings(settings) {
    // تطبيق التخطيط
    changeLayout(settings.layout);
    
    // تطبيق حالة عناصر القائمة
    Object.entries(settings.menuItems).forEach(([item, enabled]) => {
        toggleMenuItem(item, enabled);
    });
    
    // تطبيق الإعدادات السريعة
    Object.entries(settings.quickSettings).forEach(([setting, enabled]) => {
        const toggle = document.querySelector(`[data-setting="${setting}"]`);
        if (toggle) {
            toggle.classList.toggle('active', enabled);
            handleToggleChange(toggle);
        }
    });
}

// تحميل الإعدادات المحفوظة عند بدء التشغيل
window.addEventListener('load', function() {
    const savedLayout = localStorage.getItem('layout');
    if (savedLayout) {
        changeLayout(savedLayout);
        document.querySelector(`[data-layout="${savedLayout}"]`).classList.add('active');
    }
    
    const savedOrder = localStorage.getItem('menuOrder');
    if (savedOrder) {
        // إعادة ترتيب القائمة حسب الترتيب المحفوظ
        const order = JSON.parse(savedOrder);
        const container = document.getElementById('sidebarItems');
        order.forEach(itemId => {
            const item = container.querySelector(`[data-item="${itemId}"]`);
            if (item) {
                container.appendChild(item);
            }
        });
    }
});
</script>
{% endblock %}
