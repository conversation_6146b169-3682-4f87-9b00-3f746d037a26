{% extends "base.html" %}

{% block title %}إدارة المستخدمين المتقدمة - نظام إدارة الاشتراكات{% endblock %}
{% block page_title %}إدارة المستخدمين المتقدمة{% endblock %}
{% block page_description %}إدارة شاملة للمستخدمين والموزعين والحسابات{% endblock %}

{% block extra_css %}
<style>
.users-dashboard {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
}

.users-sidebar {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    height: fit-content;
}

.users-main {
    background: var(--glass-bg);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius);
    padding: 1.5rem;
}

.user-type-filter {
    margin-bottom: 2rem;
}

.filter-title {
    color: var(--text-primary);
    font-size: 1.125rem;
    font-weight: 700;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.filter-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.filter-item {
    margin-bottom: 0.5rem;
}

.filter-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: 8px;
    transition: all var(--transition-medium);
    border: 1px solid transparent;
}

.filter-link:hover,
.filter-link.active {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
    border-color: var(--glass-border);
    transform: translateX(-4px);
}

.filter-icon {
    width: 2rem;
    height: 2rem;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    color: white;
}

.filter-count {
    margin-right: auto;
    background: rgba(255, 255, 255, 0.1);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
}

.users-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 2rem;
}

.users-title {
    color: var(--text-primary);
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.users-actions {
    display: flex;
    gap: 1rem;
}

.action-btn {
    padding: 0.75rem 1.5rem;
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    text-decoration: none;
    font-weight: 600;
    transition: all var(--transition-medium);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.action-btn.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border-color: var(--primary-color);
}

.users-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1.5rem;
}

.user-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid var(--glass-border);
    border-radius: 12px;
    padding: 1.5rem;
    transition: all var(--transition-medium);
    position: relative;
    overflow: hidden;
}

.user-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
    border-color: var(--glass-border-light);
}

.user-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
}

.user-card.admin::before {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.user-card.main::before {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.user-card.sub::before {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.user-card.distributor::before {
    background: linear-gradient(135deg, #10b981, #059669);
}

.user-card.user::before {
    background: linear-gradient(135deg, #f59e0b, #d97706);
}

.user-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    font-weight: bold;
}

.user-info {
    flex: 1;
}

.user-name {
    color: var(--text-primary);
    font-size: 1.125rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.user-role {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
}

.user-status {
    position: absolute;
    top: 1rem;
    left: 1rem;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: var(--success-color);
    box-shadow: 0 0 10px rgba(16, 185, 129, 0.5);
}

.user-status.inactive {
    background: var(--error-color);
    box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
}

.user-details {
    margin-bottom: 1.5rem;
}

.detail-row {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    color: var(--text-secondary);
    font-size: 0.875rem;
    font-weight: 500;
}

.detail-value {
    color: var(--text-primary);
    font-weight: 600;
    font-size: 0.875rem;
}

.balance-display {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.75rem;
    font-weight: 600;
}

.user-actions {
    display: flex;
    gap: 0.5rem;
}

.user-btn {
    flex: 1;
    padding: 0.5rem;
    border: 1px solid var(--glass-border);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-secondary);
    font-size: 0.75rem;
    cursor: pointer;
    transition: all var(--transition-medium);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
}

.user-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-primary);
}

.user-btn.primary {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.user-btn.success {
    background: var(--success-color);
    color: white;
    border-color: var(--success-color);
}

.user-btn.warning {
    background: var(--warning-color);
    color: white;
    border-color: var(--warning-color);
}

.search-bar {
    margin-bottom: 2rem;
}

.search-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid var(--glass-border);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: all var(--transition-medium);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-input::placeholder {
    color: var(--text-muted);
}

@media (max-width: 768px) {
    .users-dashboard {
        grid-template-columns: 1fr;
    }
    
    .users-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .users-actions {
        justify-content: center;
    }
    
    .users-grid {
        grid-template-columns: 1fr;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="users-dashboard">
    <!-- الشريط الجانبي -->
    <div class="users-sidebar">
        <div class="user-type-filter">
            <h3 class="filter-title">
                <i class="fas fa-filter"></i>
                فلترة المستخدمين
            </h3>
            <ul class="filter-list">
                <li class="filter-item">
                    <a href="#" class="filter-link active" data-type="all">
                        <div class="filter-icon" style="background: linear-gradient(135deg, #6b7280, #4b5563);">
                            <i class="fas fa-users"></i>
                        </div>
                        <span>جميع المستخدمين</span>
                        <span class="filter-count">{{ total_users or 0 }}</span>
                    </a>
                </li>
                <li class="filter-item">
                    <a href="#" class="filter-link" data-type="admin">
                        <div class="filter-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626);">
                            <i class="fas fa-crown"></i>
                        </div>
                        <span>المديرين</span>
                        <span class="filter-count">{{ admin_count or 0 }}</span>
                    </a>
                </li>
                <li class="filter-item">
                    <a href="#" class="filter-link" data-type="main">
                        <div class="filter-icon" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8);">
                            <i class="fas fa-user-tie"></i>
                        </div>
                        <span>المستخدمين الرئيسيين</span>
                        <span class="filter-count">{{ main_count or 0 }}</span>
                    </a>
                </li>
                <li class="filter-item">
                    <a href="#" class="filter-link" data-type="sub">
                        <div class="filter-icon" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                            <i class="fas fa-user-friends"></i>
                        </div>
                        <span>المستخدمين الفرعيين</span>
                        <span class="filter-count">{{ sub_count or 0 }}</span>
                    </a>
                </li>
                <li class="filter-item">
                    <a href="#" class="filter-link" data-type="distributor">
                        <div class="filter-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                            <i class="fas fa-store"></i>
                        </div>
                        <span>الموزعين</span>
                        <span class="filter-count">{{ distributor_count or 0 }}</span>
                    </a>
                </li>
                <li class="filter-item">
                    <a href="#" class="filter-link" data-type="user">
                        <div class="filter-icon" style="background: linear-gradient(135deg, #f59e0b, #d97706);">
                            <i class="fas fa-user"></i>
                        </div>
                        <span>المستخدمين العاديين</span>
                        <span class="filter-count">{{ user_count or 0 }}</span>
                    </a>
                </li>
            </ul>
        </div>
    </div>

    <!-- المحتوى الرئيسي -->
    <div class="users-main">
        <div class="users-header">
            <h2 class="users-title">إدارة المستخدمين</h2>
            <div class="users-actions">
                <a href="#" class="action-btn" onclick="exportUsers()">
                    <i class="fas fa-download"></i>
                    تصدير
                </a>
                <a href="#" class="action-btn primary" onclick="addNewUser()">
                    <i class="fas fa-plus"></i>
                    إضافة مستخدم
                </a>
            </div>
        </div>

        <!-- شريط البحث -->
        <div class="search-bar">
            <input type="text" class="search-input" placeholder="البحث عن المستخدمين..." id="userSearch">
        </div>

        <!-- شبكة المستخدمين -->
        <div class="users-grid" id="usersGrid">
            {% for user in users %}
            <div class="user-card {{ user.role.role_type if user.role else 'user' }}" data-type="{{ user.role.role_type if user.role else 'user' }}">
                <div class="user-status {% if user.is_active %}active{% else %}inactive{% endif %}"></div>
                
                <div class="user-header">
                    <div class="user-avatar">
                        {{ user.first_name[0] if user.first_name else user.username[0] }}
                    </div>
                    <div class="user-info">
                        <div class="user-name">{{ user.first_name }} {{ user.last_name or '' }}</div>
                        <div class="user-role">{{ user.role.name_ar if user.role else 'مستخدم' }}</div>
                    </div>
                </div>
                
                <div class="user-details">
                    <div class="detail-row">
                        <span class="detail-label">اسم المستخدم:</span>
                        <span class="detail-value">{{ user.username }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">البريد الإلكتروني:</span>
                        <span class="detail-value">{{ user.email }}</span>
                    </div>
                    <div class="detail-row">
                        <span class="detail-label">الهاتف:</span>
                        <span class="detail-value">{{ user.phone or 'غير محدد' }}</span>
                    </div>
                    {% if user.is_distributor %}
                    <div class="detail-row">
                        <span class="detail-label">كود الموزع:</span>
                        <span class="detail-value">{{ user.distributor_code or 'غير محدد' }}</span>
                    </div>
                    {% endif %}
                    <div class="detail-row">
                        <span class="detail-label">الرصيد الرئيسي:</span>
                        <span class="balance-display">{{ '{:,.0f}'.format(user.get_main_balance() if hasattr(user, 'get_main_balance') else 0) }} ر.ي</span>
                    </div>
                </div>
                
                <div class="user-actions">
                    <button class="user-btn primary" onclick="viewUser({{ user.id }})">
                        <i class="fas fa-eye"></i>
                        عرض
                    </button>
                    <button class="user-btn" onclick="editUser({{ user.id }})">
                        <i class="fas fa-edit"></i>
                        تعديل
                    </button>
                    {% if current_user.can_charge_user(user) %}
                    <button class="user-btn success" onclick="chargeBalance({{ user.id }})">
                        <i class="fas fa-plus-circle"></i>
                        شحن
                    </button>
                    {% endif %}
                    {% if user.is_distributor %}
                    <button class="user-btn warning" onclick="manageDistributor({{ user.id }})">
                        <i class="fas fa-store"></i>
                        إدارة
                    </button>
                    {% endif %}
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// إدارة المستخدمين المتقدمة
document.addEventListener('DOMContentLoaded', function() {
    initializeUserManagement();
});

function initializeUserManagement() {
    // فلترة المستخدمين
    document.querySelectorAll('.filter-link').forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            filterUsers(this.dataset.type);
            
            // تحديث الفلتر النشط
            document.querySelectorAll('.filter-link').forEach(l => l.classList.remove('active'));
            this.classList.add('active');
        });
    });
    
    // البحث
    document.getElementById('userSearch').addEventListener('input', function() {
        searchUsers(this.value);
    });
}

function filterUsers(type) {
    const userCards = document.querySelectorAll('.user-card');
    
    userCards.forEach(card => {
        if (type === 'all' || card.dataset.type === type) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

function searchUsers(query) {
    const userCards = document.querySelectorAll('.user-card');
    const searchTerm = query.toLowerCase();
    
    userCards.forEach(card => {
        const userName = card.querySelector('.user-name').textContent.toLowerCase();
        const userRole = card.querySelector('.user-role').textContent.toLowerCase();
        const username = card.querySelector('.detail-value').textContent.toLowerCase();
        
        if (userName.includes(searchTerm) || userRole.includes(searchTerm) || username.includes(searchTerm)) {
            card.style.display = 'block';
        } else {
            card.style.display = 'none';
        }
    });
}

function addNewUser() {
    window.location.href = '/users/add';
}

function viewUser(userId) {
    window.location.href = `/users/${userId}/view`;
}

function editUser(userId) {
    window.location.href = `/users/${userId}/edit`;
}

function chargeBalance(userId) {
    const amount = prompt('أدخل مبلغ الشحن:');
    if (amount && !isNaN(amount) && parseFloat(amount) > 0) {
        fetch(`/api/users/${userId}/charge`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                amount: parseFloat(amount),
                description: 'شحن رصيد من لوحة التحكم'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('تم شحن الرصيد بنجاح', 'success');
                setTimeout(() => location.reload(), 1000);
            } else {
                showNotification('فشل في شحن الرصيد: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showNotification('خطأ في الاتصال بالخادم', 'error');
        });
    }
}

function manageDistributor(userId) {
    window.location.href = `/distributors/${userId}/manage`;
}

function exportUsers() {
    window.open('/users/export', '_blank');
}
</script>
{% endblock %}
