/**
 * تهيئة التطبيق التقدمي (PWA)
 * نظام إدارة الاشتراكات المتطور
 * تطوير: المهندس محمد ياسر الجبوري
 */

class PWAManager {
    constructor() {
        this.deferredPrompt = null;
        this.isInstalled = false;
        this.isOnline = navigator.onLine;
        this.init();
    }

    init() {
        console.log('🚀 تهيئة التطبيق التقدمي...');
        
        this.registerServiceWorker();
        this.setupInstallPrompt();
        this.setupOfflineDetection();
        this.setupPushNotifications();
        this.setupAppShortcuts();
        this.checkInstallStatus();
    }

    // تسجيل Service Worker
    async registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            try {
                const registration = await navigator.serviceWorker.register('/static/sw.js', {
                    scope: '/'
                });

                console.log('✅ تم تسجيل Service Worker:', registration.scope);

                // التحقق من التحديثات
                registration.addEventListener('updatefound', () => {
                    const newWorker = registration.installing;
                    newWorker.addEventListener('statechange', () => {
                        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                            this.showUpdateAvailable();
                        }
                    });
                });

                // معالجة رسائل Service Worker
                navigator.serviceWorker.addEventListener('message', event => {
                    this.handleServiceWorkerMessage(event.data);
                });

            } catch (error) {
                console.error('❌ فشل تسجيل Service Worker:', error);
            }
        }
    }

    // إعداد مطالبة التثبيت
    setupInstallPrompt() {
        window.addEventListener('beforeinstallprompt', (e) => {
            console.log('💾 مطالبة التثبيت متاحة');
            e.preventDefault();
            this.deferredPrompt = e;
            this.showInstallButton();
        });

        window.addEventListener('appinstalled', () => {
            console.log('✅ تم تثبيت التطبيق');
            this.isInstalled = true;
            this.hideInstallButton();
            this.showInstallSuccess();
        });
    }

    // إعداد كشف الاتصال
    setupOfflineDetection() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.showOnlineStatus();
            this.syncOfflineData();
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.showOfflineStatus();
        });

        // عرض الحالة الحالية
        this.updateConnectionStatus();
    }

    // إعداد إشعارات Push
    async setupPushNotifications() {
        if ('Notification' in window && 'serviceWorker' in navigator) {
            const permission = await Notification.requestPermission();
            
            if (permission === 'granted') {
                console.log('✅ تم منح إذن الإشعارات');
                this.subscribeToPush();
            } else {
                console.log('❌ تم رفض إذن الإشعارات');
            }
        }
    }

    // الاشتراك في إشعارات Push
    async subscribeToPush() {
        try {
            const registration = await navigator.serviceWorker.ready;
            const subscription = await registration.pushManager.subscribe({
                userVisibleOnly: true,
                applicationServerKey: this.urlBase64ToUint8Array(
                    'YOUR_VAPID_PUBLIC_KEY' // يجب استبدالها بالمفتاح الحقيقي
                )
            });

            console.log('📱 تم الاشتراك في إشعارات Push');
            
            // إرسال الاشتراك للخادم
            await this.sendSubscriptionToServer(subscription);
            
        } catch (error) {
            console.error('❌ فشل الاشتراك في إشعارات Push:', error);
        }
    }

    // إعداد اختصارات التطبيق
    setupAppShortcuts() {
        if ('navigator' in window && 'shortcuts' in navigator) {
            // يمكن إضافة اختصارات ديناميكية هنا
            console.log('⚡ اختصارات التطبيق متاحة');
        }
    }

    // فحص حالة التثبيت
    checkInstallStatus() {
        // فحص إذا كان التطبيق يعمل في وضع standalone
        if (window.matchMedia('(display-mode: standalone)').matches) {
            this.isInstalled = true;
            console.log('📱 التطبيق يعمل في وضع standalone');
        }

        // فحص إذا كان التطبيق مضاف لشاشة البداية
        if (window.navigator.standalone === true) {
            this.isInstalled = true;
            console.log('🍎 التطبيق مضاف لشاشة البداية (iOS)');
        }
    }

    // عرض زر التثبيت
    showInstallButton() {
        const installButton = this.createInstallButton();
        document.body.appendChild(installButton);
    }

    // إخفاء زر التثبيت
    hideInstallButton() {
        const installButton = document.getElementById('pwa-install-button');
        if (installButton) {
            installButton.remove();
        }
    }

    // إنشاء زر التثبيت
    createInstallButton() {
        const button = document.createElement('button');
        button.id = 'pwa-install-button';
        button.className = 'fixed bottom-4 left-4 bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 z-50 flex items-center gap-2';
        button.innerHTML = `
            <i class="fas fa-download"></i>
            <span>تثبيت التطبيق</span>
        `;
        
        button.addEventListener('click', () => {
            this.installApp();
        });

        return button;
    }

    // تثبيت التطبيق
    async installApp() {
        if (this.deferredPrompt) {
            this.deferredPrompt.prompt();
            const { outcome } = await this.deferredPrompt.userChoice;
            
            if (outcome === 'accepted') {
                console.log('✅ المستخدم وافق على التثبيت');
            } else {
                console.log('❌ المستخدم رفض التثبيت');
            }
            
            this.deferredPrompt = null;
        }
    }

    // عرض رسالة نجاح التثبيت
    showInstallSuccess() {
        if (window.notificationSystem) {
            window.notificationSystem.createNotification({
                title: 'تم التثبيت بنجاح!',
                message: 'يمكنك الآن الوصول للتطبيق من شاشة البداية',
                type: 'success',
                persistent: true
            });
        }
    }

    // عرض تحديث متاح
    showUpdateAvailable() {
        if (window.notificationSystem) {
            window.notificationSystem.createNotification({
                title: 'تحديث متاح',
                message: 'إصدار جديد من التطبيق متاح',
                type: 'info',
                persistent: true,
                actions: [
                    {
                        label: 'تحديث الآن',
                        callback: 'pwaManager.updateApp()'
                    },
                    {
                        label: 'لاحقاً',
                        callback: 'pwaManager.dismissUpdate()'
                    }
                ]
            });
        }
    }

    // تحديث التطبيق
    updateApp() {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.controller?.postMessage({ type: 'SKIP_WAITING' });
            window.location.reload();
        }
    }

    // رفض التحديث
    dismissUpdate() {
        console.log('تم تأجيل التحديث');
    }

    // عرض حالة الاتصال
    updateConnectionStatus() {
        const statusElement = this.getOrCreateStatusElement();
        
        if (this.isOnline) {
            statusElement.className = 'connection-status online';
            statusElement.innerHTML = '<i class="fas fa-wifi"></i> متصل';
        } else {
            statusElement.className = 'connection-status offline';
            statusElement.innerHTML = '<i class="fas fa-wifi-slash"></i> غير متصل';
        }
    }

    // عرض حالة الاتصال (متصل)
    showOnlineStatus() {
        this.updateConnectionStatus();
        
        if (window.notificationSystem) {
            window.notificationSystem.createNotification({
                title: 'تم استعادة الاتصال',
                message: 'أصبح بإمكانك الوصول لجميع الميزات',
                type: 'success'
            });
        }
    }

    // عرض حالة الاتصال (غير متصل)
    showOfflineStatus() {
        this.updateConnectionStatus();
        
        if (window.notificationSystem) {
            window.notificationSystem.createNotification({
                title: 'لا يوجد اتصال',
                message: 'يمكنك الاستمرار في استخدام الميزات المحفوظة',
                type: 'warning'
            });
        }
    }

    // الحصول على أو إنشاء عنصر الحالة
    getOrCreateStatusElement() {
        let statusElement = document.getElementById('connection-status');
        
        if (!statusElement) {
            statusElement = document.createElement('div');
            statusElement.id = 'connection-status';
            statusElement.className = 'connection-status';
            document.body.appendChild(statusElement);
        }
        
        return statusElement;
    }

    // مزامنة البيانات المحفوظة محلياً
    async syncOfflineData() {
        if ('serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
            try {
                const registration = await navigator.serviceWorker.ready;
                await registration.sync.register('background-sync');
                console.log('🔄 تم تسجيل مزامنة البيانات');
            } catch (error) {
                console.error('❌ فشل تسجيل مزامنة البيانات:', error);
            }
        }
    }

    // إرسال اشتراك Push للخادم
    async sendSubscriptionToServer(subscription) {
        try {
            const response = await fetch('/api/push-subscription', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(subscription)
            });
            
            if (response.ok) {
                console.log('✅ تم إرسال اشتراك Push للخادم');
            }
        } catch (error) {
            console.error('❌ فشل إرسال اشتراك Push:', error);
        }
    }

    // معالجة رسائل Service Worker
    handleServiceWorkerMessage(data) {
        switch (data.type) {
            case 'VERSION_UPDATE':
                this.showUpdateAvailable();
                break;
            case 'CACHE_UPDATED':
                console.log('📦 تم تحديث Cache');
                break;
            default:
                console.log('📨 رسالة من Service Worker:', data);
        }
    }

    // تحويل VAPID key
    urlBase64ToUint8Array(base64String) {
        const padding = '='.repeat((4 - base64String.length % 4) % 4);
        const base64 = (base64String + padding)
            .replace(/-/g, '+')
            .replace(/_/g, '/');

        const rawData = window.atob(base64);
        const outputArray = new Uint8Array(rawData.length);

        for (let i = 0; i < rawData.length; ++i) {
            outputArray[i] = rawData.charCodeAt(i);
        }
        return outputArray;
    }

    // الحصول على معلومات التطبيق
    getAppInfo() {
        return {
            isInstalled: this.isInstalled,
            isOnline: this.isOnline,
            hasServiceWorker: 'serviceWorker' in navigator,
            hasNotifications: 'Notification' in window,
            hasPushManager: 'PushManager' in window,
            hasBackgroundSync: 'serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype
        };
    }
}

// أنماط CSS للعناصر
const pwaStyles = `
    .connection-status {
        position: fixed;
        top: 10px;
        left: 10px;
        padding: 8px 12px;
        border-radius: 20px;
        font-size: 12px;
        font-weight: 500;
        z-index: 1000;
        transition: all 0.3s ease;
    }
    
    .connection-status.online {
        background: rgba(16, 185, 129, 0.9);
        color: white;
    }
    
    .connection-status.offline {
        background: rgba(239, 68, 68, 0.9);
        color: white;
    }
    
    #pwa-install-button {
        animation: pulse 2s infinite;
    }
    
    @keyframes pulse {
        0%, 100% { transform: scale(1); }
        50% { transform: scale(1.05); }
    }
`;

// إضافة الأنماط
const styleSheet = document.createElement('style');
styleSheet.textContent = pwaStyles;
document.head.appendChild(styleSheet);

// تهيئة PWA عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.pwaManager = new PWAManager();
});

// تصدير للاستخدام العام
window.PWAManager = PWAManager;
