#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام الذكاء الاصطناعي والتحليلات المتقدمة
نظام إدارة الاشتراكات المتطور
تطوير: المهندس محمد ياسر الجبوري
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import json
import logging
from typing import Dict, List, Tuple, Optional

# محاولة استيراد مكتبات التعلم الآلي
try:
    from sklearn.linear_model import LinearRegression
    from sklearn.ensemble import RandomForestRegressor
    from sklearn.preprocessing import StandardScaler
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import mean_absolute_error, r2_score
    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False
    logging.warning("مكتبات التعلم الآلي غير متوفرة. سيتم استخدام التحليلات الأساسية.")

class AIAnalytics:
    """نظام التحليلات الذكية والتنبؤ"""
    
    def __init__(self, db_session=None):
        self.db = db_session
        self.models = {}
        self.scalers = {}
        self.predictions_cache = {}
        
    def prepare_subscription_data(self) -> pd.DataFrame:
        """تحضير بيانات الاشتراكات للتحليل"""
        try:
            # محاكاة البيانات (في التطبيق الحقيقي، سيتم جلبها من قاعدة البيانات)
            data = {
                'subscription_id': range(1, 101),
                'start_date': pd.date_range('2023-01-01', periods=100, freq='D'),
                'price': np.random.normal(50, 15, 100),
                'subscription_type': np.random.choice(['monthly', 'semi_annual', 'annual'], 100),
                'provider': np.random.choice(['AWS', 'Azure', 'GCP', 'DigitalOcean'], 100),
                'status': np.random.choice(['active', 'suspended', 'expired'], 100, p=[0.7, 0.2, 0.1])
            }
            
            df = pd.DataFrame(data)
            df['end_date'] = df['start_date'] + pd.to_timedelta(
                np.where(df['subscription_type'] == 'monthly', 30,
                np.where(df['subscription_type'] == 'semi_annual', 180, 365)), unit='D'
            )
            
            # إضافة ميزات مشتقة
            df['duration_days'] = (df['end_date'] - df['start_date']).dt.days
            df['price_per_day'] = df['price'] / df['duration_days']
            df['month'] = df['start_date'].dt.month
            df['quarter'] = df['start_date'].dt.quarter
            
            return df
            
        except Exception as e:
            logging.error(f"خطأ في تحضير البيانات: {str(e)}")
            return pd.DataFrame()
    
    def revenue_prediction(self, months_ahead: int = 6) -> Dict:
        """التنبؤ بالإيرادات للأشهر القادمة"""
        try:
            df = self.prepare_subscription_data()
            if df.empty:
                return self._fallback_revenue_prediction(months_ahead)
            
            if not ML_AVAILABLE:
                return self._simple_revenue_prediction(df, months_ahead)
            
            # تحضير البيانات للنموذج
            df['revenue'] = df['price']
            monthly_revenue = df.groupby(df['start_date'].dt.to_period('M'))['revenue'].sum()
            
            # إنشاء ميزات زمنية
            X = np.array(range(len(monthly_revenue))).reshape(-1, 1)
            y = monthly_revenue.values
            
            # تدريب النموذج
            model = LinearRegression()
            model.fit(X, y)
            
            # التنبؤ
            future_X = np.array(range(len(monthly_revenue), len(monthly_revenue) + months_ahead)).reshape(-1, 1)
            predictions = model.predict(future_X)
            
            # حساب الثقة
            score = model.score(X, y)
            
            result = {
                'predictions': [
                    {
                        'month': (datetime.now() + timedelta(days=30*i)).strftime('%Y-%m'),
                        'predicted_revenue': float(pred),
                        'confidence': float(score * 100)
                    }
                    for i, pred in enumerate(predictions, 1)
                ],
                'total_predicted': float(np.sum(predictions)),
                'model_accuracy': float(score * 100),
                'trend': 'صاعد' if predictions[-1] > predictions[0] else 'هابط'
            }
            
            return result
            
        except Exception as e:
            logging.error(f"خطأ في التنبؤ بالإيرادات: {str(e)}")
            return self._fallback_revenue_prediction(months_ahead)
    
    def churn_analysis(self) -> Dict:
        """تحليل معدل إلغاء الاشتراكات"""
        try:
            df = self.prepare_subscription_data()
            if df.empty:
                return self._fallback_churn_analysis()
            
            # حساب معدل الإلغاء
            total_subscriptions = len(df)
            churned = len(df[df['status'].isin(['suspended', 'expired'])])
            churn_rate = (churned / total_subscriptions) * 100
            
            # تحليل أسباب الإلغاء
            churn_by_provider = df[df['status'].isin(['suspended', 'expired'])].groupby('provider').size()
            churn_by_type = df[df['status'].isin(['suspended', 'expired'])].groupby('subscription_type').size()
            
            # توقع العملاء المعرضين للخطر
            at_risk_customers = self._identify_at_risk_customers(df)
            
            return {
                'churn_rate': round(churn_rate, 2),
                'total_churned': churned,
                'churn_by_provider': churn_by_provider.to_dict(),
                'churn_by_type': churn_by_type.to_dict(),
                'at_risk_customers': at_risk_customers,
                'recommendations': self._generate_churn_recommendations(churn_rate)
            }
            
        except Exception as e:
            logging.error(f"خطأ في تحليل الإلغاء: {str(e)}")
            return self._fallback_churn_analysis()
    
    def pricing_optimization(self) -> Dict:
        """تحسين الأسعار باستخدام الذكاء الاصطناعي"""
        try:
            df = self.prepare_subscription_data()
            if df.empty:
                return self._fallback_pricing_optimization()
            
            # تحليل الأسعار الحالية
            price_stats = {
                'mean_price': df['price'].mean(),
                'median_price': df['price'].median(),
                'std_price': df['price'].std(),
                'min_price': df['price'].min(),
                'max_price': df['price'].max()
            }
            
            # تحليل الأسعار حسب النوع
            price_by_type = df.groupby('subscription_type')['price'].agg(['mean', 'count'])
            
            # توصيات التسعير
            recommendations = []
            
            for sub_type in df['subscription_type'].unique():
                type_data = df[df['subscription_type'] == sub_type]
                avg_price = type_data['price'].mean()
                
                # تحليل الطلب والعرض
                demand_score = len(type_data) / len(df) * 100
                
                if demand_score > 40:  # طلب عالي
                    recommended_price = avg_price * 1.1
                    reason = "طلب عالي - يمكن زيادة السعر"
                elif demand_score < 20:  # طلب منخفض
                    recommended_price = avg_price * 0.9
                    reason = "طلب منخفض - يُنصح بتخفيض السعر"
                else:
                    recommended_price = avg_price
                    reason = "السعر مناسب حالياً"
                
                recommendations.append({
                    'subscription_type': sub_type,
                    'current_avg_price': round(avg_price, 2),
                    'recommended_price': round(recommended_price, 2),
                    'demand_score': round(demand_score, 2),
                    'reason': reason
                })
            
            return {
                'price_statistics': {k: round(v, 2) for k, v in price_stats.items()},
                'price_by_type': price_by_type.round(2).to_dict(),
                'recommendations': recommendations,
                'market_insights': self._generate_market_insights(df)
            }
            
        except Exception as e:
            logging.error(f"خطأ في تحسين الأسعار: {str(e)}")
            return self._fallback_pricing_optimization()
    
    def customer_segmentation(self) -> Dict:
        """تقسيم العملاء باستخدام التعلم الآلي"""
        try:
            df = self.prepare_subscription_data()
            if df.empty:
                return self._fallback_customer_segmentation()
            
            # إنشاء ميزات للتقسيم
            features = pd.get_dummies(df[['price', 'duration_days', 'subscription_type', 'provider']])
            
            if ML_AVAILABLE:
                from sklearn.cluster import KMeans
                
                # تطبيق K-means
                kmeans = KMeans(n_clusters=4, random_state=42)
                df['segment'] = kmeans.fit_predict(features)
                
                # تحليل الشرائح
                segments = {}
                for i in range(4):
                    segment_data = df[df['segment'] == i]
                    segments[f'segment_{i+1}'] = {
                        'size': len(segment_data),
                        'avg_price': round(segment_data['price'].mean(), 2),
                        'avg_duration': round(segment_data['duration_days'].mean(), 2),
                        'most_common_type': segment_data['subscription_type'].mode().iloc[0],
                        'most_common_provider': segment_data['provider'].mode().iloc[0]
                    }
            else:
                # تقسيم بسيط بناءً على السعر
                segments = self._simple_customer_segmentation(df)
            
            return {
                'segments': segments,
                'total_customers': len(df),
                'segmentation_insights': self._generate_segmentation_insights(segments)
            }
            
        except Exception as e:
            logging.error(f"خطأ في تقسيم العملاء: {str(e)}")
            return self._fallback_customer_segmentation()
    
    def anomaly_detection(self) -> Dict:
        """كشف الشذوذ في البيانات"""
        try:
            df = self.prepare_subscription_data()
            if df.empty:
                return {'anomalies': [], 'total_anomalies': 0}
            
            anomalies = []
            
            # كشف الأسعار الشاذة
            price_mean = df['price'].mean()
            price_std = df['price'].std()
            price_threshold = 2 * price_std
            
            price_anomalies = df[
                (df['price'] > price_mean + price_threshold) | 
                (df['price'] < price_mean - price_threshold)
            ]
            
            for _, row in price_anomalies.iterrows():
                anomalies.append({
                    'type': 'price_anomaly',
                    'subscription_id': row['subscription_id'],
                    'value': row['price'],
                    'expected_range': f"{price_mean - price_threshold:.2f} - {price_mean + price_threshold:.2f}",
                    'severity': 'high' if abs(row['price'] - price_mean) > 3 * price_std else 'medium'
                })
            
            # كشف أنماط غير عادية في التواريخ
            date_gaps = df['start_date'].diff().dt.days
            unusual_gaps = date_gaps[date_gaps > 30]  # فجوات أكثر من 30 يوم
            
            for idx in unusual_gaps.index:
                anomalies.append({
                    'type': 'date_gap_anomaly',
                    'subscription_id': df.loc[idx, 'subscription_id'],
                    'gap_days': unusual_gaps[idx],
                    'severity': 'low'
                })
            
            return {
                'anomalies': anomalies,
                'total_anomalies': len(anomalies),
                'anomaly_summary': {
                    'price_anomalies': len(price_anomalies),
                    'date_anomalies': len(unusual_gaps)
                }
            }
            
        except Exception as e:
            logging.error(f"خطأ في كشف الشذوذ: {str(e)}")
            return {'anomalies': [], 'total_anomalies': 0}
    
    # دوال مساعدة للحالات الاحتياطية
    def _fallback_revenue_prediction(self, months_ahead: int) -> Dict:
        """تنبؤ بسيط بالإيرادات"""
        base_revenue = 10000
        growth_rate = 0.05
        
        predictions = []
        for i in range(1, months_ahead + 1):
            predicted = base_revenue * (1 + growth_rate) ** i
            predictions.append({
                'month': (datetime.now() + timedelta(days=30*i)).strftime('%Y-%m'),
                'predicted_revenue': round(predicted, 2),
                'confidence': 75.0
            })
        
        return {
            'predictions': predictions,
            'total_predicted': sum(p['predicted_revenue'] for p in predictions),
            'model_accuracy': 75.0,
            'trend': 'صاعد'
        }
    
    def _fallback_churn_analysis(self) -> Dict:
        """تحليل بسيط للإلغاء"""
        return {
            'churn_rate': 15.5,
            'total_churned': 23,
            'churn_by_provider': {'AWS': 8, 'Azure': 7, 'GCP': 5, 'DigitalOcean': 3},
            'churn_by_type': {'monthly': 15, 'semi_annual': 5, 'annual': 3},
            'at_risk_customers': [1, 5, 12, 18, 25],
            'recommendations': [
                'تحسين خدمة العملاء',
                'مراجعة الأسعار التنافسية',
                'تطوير برامج الولاء'
            ]
        }
    
    def _simple_customer_segmentation(self, df: pd.DataFrame) -> Dict:
        """تقسيم بسيط للعملاء"""
        price_quartiles = df['price'].quantile([0.25, 0.5, 0.75])
        
        segments = {
            'budget_customers': {
                'size': len(df[df['price'] <= price_quartiles[0.25]]),
                'description': 'عملاء الميزانية المحدودة'
            },
            'standard_customers': {
                'size': len(df[(df['price'] > price_quartiles[0.25]) & (df['price'] <= price_quartiles[0.75])]),
                'description': 'العملاء العاديون'
            },
            'premium_customers': {
                'size': len(df[df['price'] > price_quartiles[0.75]]),
                'description': 'العملاء المميزون'
            }
        }
        
        return segments
    
    def _identify_at_risk_customers(self, df: pd.DataFrame) -> List[int]:
        """تحديد العملاء المعرضين لخطر الإلغاء"""
        # منطق بسيط: العملاء الذين اقترب انتهاء اشتراكهم
        today = datetime.now().date()
        at_risk = df[
            (df['end_date'].dt.date <= today + timedelta(days=30)) &
            (df['status'] == 'active')
        ]
        return at_risk['subscription_id'].tolist()
    
    def _generate_churn_recommendations(self, churn_rate: float) -> List[str]:
        """توليد توصيات لتقليل الإلغاء"""
        recommendations = []
        
        if churn_rate > 20:
            recommendations.extend([
                'مراجعة عاجلة لاستراتيجية الاحتفاظ بالعملاء',
                'تحسين جودة الخدمة',
                'تطوير برامج حوافز للعملاء المخلصين'
            ])
        elif churn_rate > 10:
            recommendations.extend([
                'تحسين تجربة العملاء',
                'مراجعة الأسعار التنافسية',
                'تطوير برامج الولاء'
            ])
        else:
            recommendations.append('معدل الإلغاء ضمن المعدل الطبيعي')
        
        return recommendations
    
    def _generate_market_insights(self, df: pd.DataFrame) -> List[str]:
        """توليد رؤى السوق"""
        insights = []
        
        # تحليل توزيع المزودين
        provider_dist = df['provider'].value_counts()
        most_popular = provider_dist.index[0]
        insights.append(f"المزود الأكثر شعبية: {most_popular}")
        
        # تحليل أنواع الاشتراكات
        type_dist = df['subscription_type'].value_counts()
        most_common_type = type_dist.index[0]
        insights.append(f"نوع الاشتراك الأكثر شيوعاً: {most_common_type}")
        
        return insights
    
    def _generate_segmentation_insights(self, segments: Dict) -> List[str]:
        """توليد رؤى التقسيم"""
        insights = []
        
        total_customers = sum(seg.get('size', 0) for seg in segments.values())
        
        for name, segment in segments.items():
            percentage = (segment.get('size', 0) / total_customers) * 100
            insights.append(f"{name}: {percentage:.1f}% من العملاء")
        
        return insights
