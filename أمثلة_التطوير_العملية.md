# 🛠️ أمثلة التطوير العملية - نظام إدارة الاشتراكات

## 📋 فهرس الأمثلة

1. [إضافة ميزات جديدة](#إضافة-ميزات-جديدة)
2. [تخصيص التصميم](#تخصيص-التصميم)
3. [إضافة تقارير](#إضافة-تقارير)
4. [تحسين الأداء](#تحسين-الأداء)
5. [إضافة API](#إضافة-api)
6. [نظام الإشعارات](#نظام-الإشعارات)

---

## 🔧 إضافة ميزات جديدة

### مثال 1: إضافة نظام التقييمات للعملاء

#### 1. إنشاء جدول التقييمات:
```python
class CustomerRating(db.Model):
    """نموذج تقييمات العملاء"""
    id = db.Column(db.Integer, primary_key=True)
    subscription_id = db.Column(db.Integer, db.ForeignKey('subscription.id'), nullable=False)
    rating = db.Column(db.Integer, nullable=False)  # من 1 إلى 5
    comment = db.Column(db.Text, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    subscription = db.relationship('Subscription', backref='ratings')
    
    def __repr__(self):
        return f'<Rating {self.rating}/5 for {self.subscription.customer_name}>'
```

#### 2. إضافة Route للتقييم:
```python
@app.route('/subscription/<int:subscription_id>/rate', methods=['GET', 'POST'])
@login_required
def rate_subscription(subscription_id):
    """تقييم الاشتراك"""
    subscription = Subscription.query.get_or_404(subscription_id)
    
    if request.method == 'POST':
        rating = CustomerRating(
            subscription_id=subscription_id,
            rating=int(request.form['rating']),
            comment=request.form.get('comment', '')
        )
        
        db.session.add(rating)
        db.session.commit()
        
        flash('تم إضافة التقييم بنجاح', 'success')
        return redirect(url_for('customer_statement', subscription_id=subscription_id))
    
    return render_template('rate_subscription.html', subscription=subscription)
```

#### 3. Template التقييم:
```html
<!-- templates/rate_subscription.html -->
{% extends "base.html" %}
{% block title %}تقييم الخدمة - {{ subscription.customer_name }}{% endblock %}

{% block content %}
<div class="rating-container">
    <div class="rating-card">
        <h3>تقييم خدمة {{ subscription.customer_name }}</h3>
        
        <form method="POST">
            <div class="rating-stars">
                <label>التقييم:</label>
                <div class="stars">
                    {% for i in range(1, 6) %}
                    <input type="radio" name="rating" value="{{ i }}" id="star{{ i }}" required>
                    <label for="star{{ i }}" class="star">⭐</label>
                    {% endfor %}
                </div>
            </div>
            
            <div class="form-group">
                <label for="comment">تعليق (اختياري):</label>
                <textarea name="comment" id="comment" rows="4" 
                          placeholder="شاركنا رأيك في الخدمة..."></textarea>
            </div>
            
            <button type="submit" class="btn-primary">إرسال التقييم</button>
        </form>
    </div>
</div>

<style>
.rating-stars {
    margin: 1rem 0;
}

.stars {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.stars input[type="radio"] {
    display: none;
}

.star {
    font-size: 2rem;
    cursor: pointer;
    transition: all 0.3s ease;
    filter: grayscale(100%);
}

.stars input[type="radio"]:checked ~ .star,
.star:hover {
    filter: grayscale(0%);
    transform: scale(1.1);
}
</style>
{% endblock %}
```

### مثال 2: إضافة نظام الخصومات

#### 1. جدول الخصومات:
```python
class Discount(db.Model):
    """نموذج الخصومات"""
    id = db.Column(db.Integer, primary_key=True)
    code = db.Column(db.String(50), unique=True, nullable=False)
    description = db.Column(db.String(200), nullable=False)
    discount_type = db.Column(db.String(20), nullable=False)  # percentage, fixed
    discount_value = db.Column(db.Float, nullable=False)
    min_amount = db.Column(db.Float, default=0)
    max_uses = db.Column(db.Integer, default=1)
    used_count = db.Column(db.Integer, default=0)
    start_date = db.Column(db.Date, nullable=False)
    end_date = db.Column(db.Date, nullable=False)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def is_valid(self, amount=0):
        """التحقق من صحة الخصم"""
        today = datetime.now().date()
        return (
            self.is_active and
            self.start_date <= today <= self.end_date and
            self.used_count < self.max_uses and
            amount >= self.min_amount
        )
    
    def calculate_discount(self, amount):
        """حساب قيمة الخصم"""
        if not self.is_valid(amount):
            return 0
        
        if self.discount_type == 'percentage':
            return amount * (self.discount_value / 100)
        else:  # fixed
            return min(self.discount_value, amount)
```

#### 2. تطبيق الخصم في الدفعات:
```python
@app.route('/subscription/<int:subscription_id>/add-payment-with-discount', methods=['POST'])
@login_required
def add_payment_with_discount(subscription_id):
    """إضافة دفعة مع خصم"""
    subscription = Subscription.query.get_or_404(subscription_id)
    
    amount = float(request.form['amount'])
    discount_code = request.form.get('discount_code', '').strip()
    
    discount_amount = 0
    discount = None
    
    if discount_code:
        discount = Discount.query.filter_by(code=discount_code).first()
        if discount and discount.is_valid(amount):
            discount_amount = discount.calculate_discount(amount)
            discount.used_count += 1
        else:
            flash('كود الخصم غير صالح أو منتهي الصلاحية', 'error')
            return redirect(url_for('add_payment', subscription_id=subscription_id))
    
    final_amount = amount - discount_amount
    
    payment = Payment(
        subscription_id=subscription_id,
        amount=final_amount,
        original_amount=amount,
        discount_amount=discount_amount,
        discount_code=discount_code,
        payment_date=datetime.strptime(request.form['payment_date'], '%Y-%m-%d').date(),
        payment_method=request.form['payment_method'],
        status='completed'
    )
    
    db.session.add(payment)
    if discount:
        db.session.add(discount)
    db.session.commit()
    
    flash(f'تم إضافة الدفعة بنجاح. تم توفير ${discount_amount:.2f}', 'success')
    return redirect(url_for('customer_statement', subscription_id=subscription_id))
```

---

## 🎨 تخصيص التصميم

### مثال 1: إضافة ثيمات متعددة

#### 1. CSS للثيمات:
```css
/* static/css/themes.css */

/* الثيم الافتراضي */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --card-bg: rgba(255, 255, 255, 0.1);
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.7);
    --accent-color: #667eea;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
}

/* ثيم الليل */
[data-theme="dark"] {
    --primary-gradient: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    --card-bg: rgba(255, 255, 255, 0.05);
    --text-primary: #e2e8f0;
    --text-secondary: rgba(226, 232, 240, 0.6);
    --accent-color: #00d4ff;
}

/* ثيم الطبيعة */
[data-theme="nature"] {
    --primary-gradient: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
    --card-bg: rgba(255, 255, 255, 0.15);
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.8);
    --accent-color: #11998e;
}

/* ثيم الغروب */
[data-theme="sunset"] {
    --primary-gradient: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
    --card-bg: rgba(255, 255, 255, 0.12);
    --text-primary: #ffffff;
    --text-secondary: rgba(255, 255, 255, 0.75);
    --accent-color: #ff6b6b;
}

/* تطبيق المتغيرات */
body {
    background: var(--primary-gradient);
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.card {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-primary {
    background: var(--accent-color);
}
```

#### 2. JavaScript لتبديل الثيمات:
```javascript
// static/js/theme-switcher.js

class ThemeSwitcher {
    constructor() {
        this.currentTheme = localStorage.getItem('theme') || 'default';
        this.applyTheme(this.currentTheme);
        this.initThemeSwitcher();
    }
    
    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('theme', theme);
        this.currentTheme = theme;
    }
    
    initThemeSwitcher() {
        const switcher = document.getElementById('theme-switcher');
        if (switcher) {
            switcher.value = this.currentTheme;
            switcher.addEventListener('change', (e) => {
                this.applyTheme(e.target.value);
                this.showThemeChangeNotification();
            });
        }
    }
    
    showThemeChangeNotification() {
        const notification = document.createElement('div');
        notification.className = 'theme-notification';
        notification.textContent = 'تم تغيير الثيم بنجاح!';
        document.body.appendChild(notification);
        
        setTimeout(() => {
            notification.classList.add('show');
        }, 100);
        
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 300);
        }, 2000);
    }
}

// تهيئة مبدل الثيمات
document.addEventListener('DOMContentLoaded', () => {
    new ThemeSwitcher();
});
```

#### 3. إضافة مبدل الثيمات في القالب:
```html
<!-- في base.html -->
<div class="theme-switcher-container">
    <label for="theme-switcher">الثيم:</label>
    <select id="theme-switcher" class="theme-select">
        <option value="default">افتراضي</option>
        <option value="dark">ليلي</option>
        <option value="nature">طبيعي</option>
        <option value="sunset">غروب</option>
    </select>
</div>

<style>
.theme-switcher-container {
    position: fixed;
    top: 20px;
    left: 20px;
    z-index: 1000;
    background: var(--card-bg);
    padding: 0.5rem 1rem;
    border-radius: 8px;
    backdrop-filter: blur(20px);
}

.theme-select {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: var(--text-primary);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    margin-right: 0.5rem;
}

.theme-notification {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.8);
    background: var(--success-color);
    color: white;
    padding: 1rem 2rem;
    border-radius: 8px;
    opacity: 0;
    transition: all 0.3s ease;
    z-index: 10000;
}

.theme-notification.show {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
}
</style>
```

---

## 📊 إضافة تقارير

### مثال 1: تقرير الأداء المالي

#### 1. Route التقرير:
```python
@app.route('/reports/financial-performance')
@login_required
def financial_performance():
    """تقرير الأداء المالي"""
    from datetime import datetime, timedelta
    from sqlalchemy import func, extract
    
    # فترة التقرير (آخر 12 شهر)
    end_date = datetime.now().date()
    start_date = end_date - timedelta(days=365)
    
    # الإيرادات الشهرية
    monthly_revenue = db.session.query(
        extract('year', Payment.payment_date).label('year'),
        extract('month', Payment.payment_date).label('month'),
        func.sum(Payment.amount).label('revenue'),
        func.count(Payment.id).label('payment_count')
    ).filter(
        Payment.status == 'completed',
        Payment.payment_date >= start_date
    ).group_by(
        extract('year', Payment.payment_date),
        extract('month', Payment.payment_date)
    ).order_by(
        extract('year', Payment.payment_date),
        extract('month', Payment.payment_date)
    ).all()
    
    # أفضل العملاء
    top_customers = db.session.query(
        Subscription.customer_name,
        func.sum(Payment.amount).label('total_paid'),
        func.count(Payment.id).label('payment_count')
    ).join(Payment).filter(
        Payment.status == 'completed',
        Payment.payment_date >= start_date
    ).group_by(
        Subscription.customer_name
    ).order_by(
        func.sum(Payment.amount).desc()
    ).limit(10).all()
    
    # إحصائيات عامة
    total_revenue = db.session.query(func.sum(Payment.amount)).filter(
        Payment.status == 'completed',
        Payment.payment_date >= start_date
    ).scalar() or 0
    
    avg_payment = db.session.query(func.avg(Payment.amount)).filter(
        Payment.status == 'completed',
        Payment.payment_date >= start_date
    ).scalar() or 0
    
    return render_template('financial_performance.html',
                         monthly_revenue=monthly_revenue,
                         top_customers=top_customers,
                         total_revenue=total_revenue,
                         avg_payment=avg_payment,
                         start_date=start_date,
                         end_date=end_date)
```

#### 2. Template التقرير:
```html
<!-- templates/financial_performance.html -->
{% extends "base.html" %}
{% block title %}تقرير الأداء المالي{% endblock %}

{% block extra_css %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<style>
.report-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

.report-header {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    text-align: center;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
}

.chart-container {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
}

.customers-table {
    background: var(--card-bg);
    backdrop-filter: blur(20px);
    border-radius: 16px;
    padding: 2rem;
}
</style>
{% endblock %}

{% block content %}
<div class="report-container">
    <div class="report-header">
        <h1>تقرير الأداء المالي</h1>
        <p>من {{ start_date }} إلى {{ end_date }}</p>
    </div>
    
    <!-- الإحصائيات العامة -->
    <div class="stats-grid">
        <div class="stat-card">
            <h3>إجمالي الإيرادات</h3>
            <div class="stat-value">${{ "%.2f"|format(total_revenue) }}</div>
        </div>
        <div class="stat-card">
            <h3>متوسط الدفعة</h3>
            <div class="stat-value">${{ "%.2f"|format(avg_payment) }}</div>
        </div>
        <div class="stat-card">
            <h3>عدد العملاء</h3>
            <div class="stat-value">{{ top_customers|length }}</div>
        </div>
    </div>
    
    <!-- رسم بياني للإيرادات الشهرية -->
    <div class="chart-container">
        <h3>الإيرادات الشهرية</h3>
        <canvas id="revenueChart" width="400" height="200"></canvas>
    </div>
    
    <!-- جدول أفضل العملاء -->
    <div class="customers-table">
        <h3>أفضل العملاء</h3>
        <table class="table">
            <thead>
                <tr>
                    <th>اسم العميل</th>
                    <th>إجمالي المدفوع</th>
                    <th>عدد الدفعات</th>
                </tr>
            </thead>
            <tbody>
                {% for customer in top_customers %}
                <tr>
                    <td>{{ customer.customer_name }}</td>
                    <td>${{ "%.2f"|format(customer.total_paid) }}</td>
                    <td>{{ customer.payment_count }}</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// رسم بياني للإيرادات الشهرية
const ctx = document.getElementById('revenueChart').getContext('2d');
const revenueData = {
    labels: [
        {% for data in monthly_revenue %}
        '{{ data.month }}/{{ data.year }}'{% if not loop.last %},{% endif %}
        {% endfor %}
    ],
    datasets: [{
        label: 'الإيرادات الشهرية',
        data: [
            {% for data in monthly_revenue %}
            {{ data.revenue }}{% if not loop.last %},{% endif %}
            {% endfor %}
        ],
        borderColor: 'rgba(102, 126, 234, 1)',
        backgroundColor: 'rgba(102, 126, 234, 0.1)',
        tension: 0.4,
        fill: true
    }]
};

new Chart(ctx, {
    type: 'line',
    data: revenueData,
    options: {
        responsive: true,
        plugins: {
            legend: {
                labels: {
                    color: 'white'
                }
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                ticks: {
                    color: 'white',
                    callback: function(value) {
                        return '$' + value.toFixed(2);
                    }
                },
                grid: {
                    color: 'rgba(255, 255, 255, 0.1)'
                }
            },
            x: {
                ticks: {
                    color: 'white'
                },
                grid: {
                    color: 'rgba(255, 255, 255, 0.1)'
                }
            }
        }
    }
});
</script>
{% endblock %}
```

---

## ⚡ تحسين الأداء

### مثال 1: تحسين استعلامات قاعدة البيانات

#### 1. إضافة فهارس:
```python
# في app.py - إضافة فهارس لتحسين الأداء
def create_indexes():
    """إنشاء فهارس لتحسين الأداء"""
    try:
        with db.engine.connect() as conn:
            # فهرس على اسم العميل للبحث السريع
            conn.execute(text('CREATE INDEX IF NOT EXISTS idx_customer_name ON subscription(customer_name)'))
            
            # فهرس على حالة الاشتراك
            conn.execute(text('CREATE INDEX IF NOT EXISTS idx_subscription_status ON subscription(status)'))
            
            # فهرس على تاريخ انتهاء الاشتراك
            conn.execute(text('CREATE INDEX IF NOT EXISTS idx_end_date ON subscription(end_date)'))
            
            # فهرس على تاريخ الدفعة
            conn.execute(text('CREATE INDEX IF NOT EXISTS idx_payment_date ON payment(payment_date)'))
            
            # فهرس مركب على معرف الاشتراك وحالة الدفعة
            conn.execute(text('CREATE INDEX IF NOT EXISTS idx_payment_subscription_status ON payment(subscription_id, status)'))
            
            conn.commit()
            print("✅ تم إنشاء الفهارس بنجاح")
    except Exception as e:
        print(f"⚠️ خطأ في إنشاء الفهارس: {e}")

# استدعاء الدالة عند بدء التطبيق
create_indexes()
```

#### 2. تحسين استعلامات الاشتراكات:
```python
def get_subscriptions_optimized(page=1, per_page=20, search=None, status=None):
    """جلب الاشتراكات مع تحسين الأداء"""
    query = Subscription.query
    
    # إضافة joinedload لتجنب N+1 queries
    query = query.options(db.joinedload(Subscription.provider))
    
    # تطبيق الفلاتر
    if search:
        query = query.filter(
            db.or_(
                Subscription.customer_name.ilike(f'%{search}%'),
                Subscription.name.ilike(f'%{search}%'),
                Subscription.cloud_ip.ilike(f'%{search}%')
            )
        )
    
    if status:
        query = query.filter(Subscription.status == status)
    
    # ترتيب وتقسيم الصفحات
    query = query.order_by(Subscription.created_at.desc())
    
    return query.paginate(
        page=page,
        per_page=per_page,
        error_out=False
    )

@app.route('/api/subscriptions')
@login_required
def api_subscriptions():
    """API محسن للاشتراكات"""
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    search = request.args.get('search', '')
    status = request.args.get('status', '')
    
    pagination = get_subscriptions_optimized(
        page=page,
        per_page=per_page,
        search=search if search else None,
        status=status if status else None
    )
    
    subscriptions_data = []
    for subscription in pagination.items:
        subscriptions_data.append({
            'id': subscription.id,
            'customer_name': subscription.customer_name,
            'name': subscription.name,
            'cloud_ip': subscription.cloud_ip,
            'price': float(subscription.price),
            'status': subscription.status,
            'days_remaining': subscription.days_remaining,
            'provider_name': subscription.provider.name if subscription.provider else 'غير محدد'
        })
    
    return jsonify({
        'subscriptions': subscriptions_data,
        'pagination': {
            'page': pagination.page,
            'pages': pagination.pages,
            'per_page': pagination.per_page,
            'total': pagination.total,
            'has_next': pagination.has_next,
            'has_prev': pagination.has_prev
        }
    })
```

#### 3. تحسين عرض البيانات بـ AJAX:
```javascript
// static/js/subscriptions-optimized.js

class SubscriptionManager {
    constructor() {
        this.currentPage = 1;
        this.perPage = 20;
        this.searchTimeout = null;
        this.init();
    }
    
    init() {
        this.loadSubscriptions();
        this.initSearch();
        this.initFilters();
        this.initPagination();
    }
    
    async loadSubscriptions(page = 1) {
        try {
            this.showLoading();
            
            const params = new URLSearchParams({
                page: page,
                per_page: this.perPage,
                search: document.getElementById('search').value,
                status: document.getElementById('status-filter').value
            });
            
            const response = await fetch(`/api/subscriptions?${params}`);
            const data = await response.json();
            
            this.renderSubscriptions(data.subscriptions);
            this.renderPagination(data.pagination);
            
        } catch (error) {
            console.error('خطأ في تحميل البيانات:', error);
            this.showError('فشل في تحميل البيانات');
        } finally {
            this.hideLoading();
        }
    }
    
    renderSubscriptions(subscriptions) {
        const container = document.getElementById('subscriptions-container');
        
        if (subscriptions.length === 0) {
            container.innerHTML = '<div class="no-data">لا توجد اشتراكات</div>';
            return;
        }
        
        const html = subscriptions.map(sub => `
            <div class="subscription-card" data-id="${sub.id}">
                <div class="subscription-header">
                    <h4>${sub.customer_name}</h4>
                    <span class="status-badge status-${sub.status}">${this.getStatusText(sub.status)}</span>
                </div>
                <div class="subscription-body">
                    <p><strong>الشبكة:</strong> ${sub.name}</p>
                    <p><strong>IP:</strong> ${sub.cloud_ip}</p>
                    <p><strong>السعر:</strong> $${sub.price.toFixed(2)}</p>
                    <p><strong>الأيام المتبقية:</strong> ${sub.days_remaining}</p>
                </div>
                <div class="subscription-actions">
                    <button onclick="viewStatement(${sub.id})" class="btn-primary btn-sm">كشف الحساب</button>
                    <button onclick="renewSubscription(${sub.id})" class="btn-success btn-sm">تجديد</button>
                </div>
            </div>
        `).join('');
        
        container.innerHTML = html;
        
        // إضافة أنيميشن للبطاقات
        container.querySelectorAll('.subscription-card').forEach((card, index) => {
            card.style.animationDelay = `${index * 0.1}s`;
            card.classList.add('fade-in');
        });
    }
    
    initSearch() {
        const searchInput = document.getElementById('search');
        searchInput.addEventListener('input', (e) => {
            clearTimeout(this.searchTimeout);
            this.searchTimeout = setTimeout(() => {
                this.currentPage = 1;
                this.loadSubscriptions();
            }, 300);
        });
    }
    
    initFilters() {
        const statusFilter = document.getElementById('status-filter');
        statusFilter.addEventListener('change', () => {
            this.currentPage = 1;
            this.loadSubscriptions();
        });
    }
    
    showLoading() {
        document.getElementById('loading-spinner').style.display = 'block';
    }
    
    hideLoading() {
        document.getElementById('loading-spinner').style.display = 'none';
    }
    
    getStatusText(status) {
        const statusMap = {
            'active': 'نشط',
            'suspended': 'معلق',
            'expired': 'منتهي'
        };
        return statusMap[status] || status;
    }
}

// تهيئة مدير الاشتراكات
document.addEventListener('DOMContentLoaded', () => {
    new SubscriptionManager();
});
```

---

## 🔔 نظام الإشعارات

### مثال 1: إشعارات الوقت الفعلي

#### 1. نموذج الإشعارات:
```python
class Notification(db.Model):
    """نموذج الإشعارات"""
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=True)
    title = db.Column(db.String(200), nullable=False)
    message = db.Column(db.Text, nullable=False)
    notification_type = db.Column(db.String(50), nullable=False)  # info, warning, error, success
    is_read = db.Column(db.Boolean, default=False)
    action_url = db.Column(db.String(200), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    user = db.relationship('User', backref='notifications')
    
    def to_dict(self):
        return {
            'id': self.id,
            'title': self.title,
            'message': self.message,
            'type': self.notification_type,
            'is_read': self.is_read,
            'action_url': self.action_url,
            'created_at': self.created_at.isoformat()
        }
```

#### 2. خدمة الإشعارات:
```python
class NotificationService:
    """خدمة إدارة الإشعارات"""
    
    @staticmethod
    def create_notification(title, message, notification_type='info', user_id=None, action_url=None):
        """إنشاء إشعار جديد"""
        notification = Notification(
            title=title,
            message=message,
            notification_type=notification_type,
            user_id=user_id,
            action_url=action_url
        )
        
        db.session.add(notification)
        db.session.commit()
        
        # إرسال الإشعار للمستخدمين المتصلين
        socketio.emit('new_notification', notification.to_dict(), room=f'user_{user_id}' if user_id else None)
        
        return notification
    
    @staticmethod
    def check_subscription_expiry():
        """فحص الاشتراكات المنتهية وإنشاء إشعارات"""
        from datetime import datetime, timedelta
        
        # الاشتراكات التي تنتهي خلال 7 أيام
        expiring_soon = Subscription.query.filter(
            Subscription.end_date <= datetime.now().date() + timedelta(days=7),
            Subscription.end_date > datetime.now().date(),
            Subscription.status == 'active'
        ).all()
        
        for subscription in expiring_soon:
            days_left = (subscription.end_date - datetime.now().date()).days
            
            # التحقق من عدم وجود إشعار مسبق
            existing = Notification.query.filter_by(
                title=f'انتهاء اشتراك {subscription.customer_name}',
                is_read=False
            ).first()
            
            if not existing:
                NotificationService.create_notification(
                    title=f'انتهاء اشتراك {subscription.customer_name}',
                    message=f'اشتراك {subscription.customer_name} ينتهي خلال {days_left} أيام',
                    notification_type='warning',
                    action_url=f'/subscription/{subscription.id}/statement'
                )
    
    @staticmethod
    def check_payment_reminders():
        """فحص تذكيرات الدفع"""
        subscriptions_with_debt = Subscription.query.filter(
            Subscription.status == 'active'
        ).all()
        
        for subscription in subscriptions_with_debt:
            if subscription.remaining_amount > 0:
                NotificationService.create_notification(
                    title=f'تذكير دفع - {subscription.customer_name}',
                    message=f'يوجد مبلغ ${subscription.remaining_amount:.2f} غير مدفوع',
                    notification_type='info',
                    action_url=f'/subscription/{subscription.id}/add-payment'
                )

# مهمة دورية لفحص الإشعارات
from apscheduler.schedulers.background import BackgroundScheduler

scheduler = BackgroundScheduler()
scheduler.add_job(
    func=NotificationService.check_subscription_expiry,
    trigger="interval",
    hours=6,  # كل 6 ساعات
    id='check_expiry'
)
scheduler.add_job(
    func=NotificationService.check_payment_reminders,
    trigger="interval",
    days=1,  # يومياً
    id='check_payments'
)
scheduler.start()
```

#### 3. WebSocket للإشعارات الفورية:
```python
# إضافة SocketIO للإشعارات الفورية
from flask_socketio import SocketIO, emit, join_room, leave_room

socketio = SocketIO(app, cors_allowed_origins="*")

@socketio.on('connect')
def on_connect():
    """عند اتصال المستخدم"""
    if current_user.is_authenticated:
        join_room(f'user_{current_user.id}')
        emit('connected', {'message': 'تم الاتصال بنجاح'})

@socketio.on('disconnect')
def on_disconnect():
    """عند قطع الاتصال"""
    if current_user.is_authenticated:
        leave_room(f'user_{current_user.id}')

@app.route('/api/notifications/mark-read/<int:notification_id>', methods=['POST'])
@login_required
def mark_notification_read(notification_id):
    """تحديد الإشعار كمقروء"""
    notification = Notification.query.get_or_404(notification_id)
    notification.is_read = True
    db.session.commit()
    
    return jsonify({'success': True})
```

#### 4. JavaScript للإشعارات:
```javascript
// static/js/notifications-realtime.js

class NotificationManager {
    constructor() {
        this.socket = io();
        this.notifications = [];
        this.init();
    }
    
    init() {
        this.initSocket();
        this.loadNotifications();
        this.initNotificationCenter();
    }
    
    initSocket() {
        this.socket.on('connect', () => {
            console.log('متصل بخادم الإشعارات');
        });
        
        this.socket.on('new_notification', (notification) => {
            this.addNotification(notification);
            this.showToast(notification);
            this.updateNotificationBadge();
        });
    }
    
    async loadNotifications() {
        try {
            const response = await fetch('/api/notifications/unread');
            const notifications = await response.json();
            this.notifications = notifications;
            this.renderNotifications();
            this.updateNotificationBadge();
        } catch (error) {
            console.error('خطأ في تحميل الإشعارات:', error);
        }
    }
    
    addNotification(notification) {
        this.notifications.unshift(notification);
        this.renderNotifications();
    }
    
    showToast(notification) {
        const toast = document.createElement('div');
        toast.className = `notification-toast notification-${notification.type}`;
        toast.innerHTML = `
            <div class="toast-header">
                <strong>${notification.title}</strong>
                <button class="toast-close" onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
            <div class="toast-body">${notification.message}</div>
        `;
        
        document.getElementById('toast-container').appendChild(toast);
        
        // إزالة التوست بعد 5 ثوان
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, 5000);
        
        // تشغيل صوت الإشعار
        this.playNotificationSound(notification.type);
    }
    
    playNotificationSound(type) {
        const audio = new Audio(`/static/sounds/${type}.mp3`);
        audio.volume = 0.3;
        audio.play().catch(() => {
            // تجاهل الأخطاء إذا لم يتمكن من تشغيل الصوت
        });
    }
    
    updateNotificationBadge() {
        const badge = document.getElementById('notification-badge');
        const unreadCount = this.notifications.filter(n => !n.is_read).length;
        
        if (unreadCount > 0) {
            badge.textContent = unreadCount;
            badge.style.display = 'block';
        } else {
            badge.style.display = 'none';
        }
    }
    
    async markAsRead(notificationId) {
        try {
            await fetch(`/api/notifications/mark-read/${notificationId}`, {
                method: 'POST'
            });
            
            const notification = this.notifications.find(n => n.id === notificationId);
            if (notification) {
                notification.is_read = true;
                this.updateNotificationBadge();
                this.renderNotifications();
            }
        } catch (error) {
            console.error('خطأ في تحديث الإشعار:', error);
        }
    }
}

// تهيئة مدير الإشعارات
document.addEventListener('DOMContentLoaded', () => {
    new NotificationManager();
});
```

---

## 🎯 الخلاصة

هذا الملف يحتوي على أمثلة عملية شاملة لتطوير وتحسين نظام إدارة الاشتراكات. يمكنك استخدام هذه الأمثلة كنقطة انطلاق لإضافة ميزات جديدة أو تحسين الميزات الموجودة.

### نصائح مهمة:
1. **اختبر دائماً** أي تعديل قبل تطبيقه على النظام الحي
2. **اعمل نسخة احتياطية** قبل إجراء تغييرات كبيرة
3. **راقب الأداء** بعد إضافة ميزات جديدة
4. **وثق التغييرات** التي تقوم بها
5. **استخدم Git** لتتبع التغييرات

**تم إعداد هذا الدليل بواسطة: المهندس محمد ياسر الجبوري**
**تاريخ الإنشاء: 2025-07-21**
